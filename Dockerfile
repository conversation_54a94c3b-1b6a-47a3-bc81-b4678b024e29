# Build docker image
FROM node:22-alpine AS build-frontend

WORKDIR /app

COPY package.json .
RUN yarn
COPY . .
RUN export $(cat .env | xargs) && yarn build

ARG REACT_APP_ENV
ENV REACT_APP_ENV=$REACT_APP_ENV

# # Upload sourcemaps to Sentry
# RUN yarn global add @sentry/cli && \
#     export SENTRY_RELEASE="release-$(date +%Y%m%d%H%M%S)" && \
#     sentry-cli releases new "$SENTRY_RELEASE" && \
#     sentry-cli sourcemaps inject build && \
#     sentry-cli releases files "$SENTRY_RELEASE" upload-sourcemaps build && \
#     sentry-cli releases finalize "$SENTRY_RELEASE" && \
#     sentry-cli releases deploys "$SENTRY_RELEASE" new --env "$REACT_APP_ENV"

# Serve with NGINX
FROM nginx:alpine

COPY --from=build-frontend /app/dist /usr/share/nginx/html
COPY nginx/default.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]