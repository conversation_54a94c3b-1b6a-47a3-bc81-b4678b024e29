# Build outputs
dist/
build/

# Dependencies
node_modules/

# Generated files
*.min.js
*.bundle.js

# Storybook build
storybook-static/

# Coverage reports
coverage/

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log

# IDE files
.vscode/
.idea/

# OS files
.DS_Store
Thumbs.db

# Markdown files (optional - remove if you want to format markdown)
# *.md

# Config files that should maintain their format
.eslintrc.cjs
vite.config.js

# Temporary files
*.tmp
*.temp
