# Build outputs
dist/
build/

# Dependencies
node_modules/

# Generated files
*.min.js
*.bundle.js

# Storybook build
storybook-static/

# Coverage reports
coverage/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# IDE files
.vscode/
.idea/

# OS files
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
