{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "prettier.requireConfig": true, "files.associations": {"*.jsx": "javascriptreact"}, "eslint.codeActionsOnSave.mode": "all", "eslint.run": "onSave", "typescript.preferences.includePackageJsonAutoImports": "off", "typescript.suggest.autoImports": false}