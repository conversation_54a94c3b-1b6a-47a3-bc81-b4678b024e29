import { createGlobalStyle } from 'styled-components';

const GlobalStyle = createGlobalStyle`

  html,
  body,
  #root {
    width: 100%;
    height: 100%;
  }

  body {
    scroll-behavior: smooth;
    text-rendering: optimizeSpeed;
    background: #f3f3f4;
  }

  /* Modern Scrollbar Styles */
  * {
    scrollbar-width: thin;
    scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
  }
 
  *::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
 
  *::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 10px;
  }
 
  *::-webkit-scrollbar-thumb {
    background-color: rgba(155, 155, 155, 0.5);
    border-radius: 10px;
    border: 2px solid transparent;
    background-clip: padding-box;
    transition: background-color 0.2s ease-in-out;
  }

  *::-webkit-scrollbar-thumb:hover {
    background-color: rgba(155, 155, 155, 0.8);
  }

  *::-webkit-scrollbar-corner {
    background: transparent;
  }

  .hidden {
    display: none;
  }

  .margin-horizontal {
    margin: ${props => props.theme.spacing(0, 1)} !important;
  }

  .MuiTableCell-head {
    font-weight: bold;
  }
  .disabled-text, .disabled-text * {
    color: ${props => props.theme.palette.text.disabled} !important;
  }
  svg.disabled {
    color: ${props => props.theme.palette.text.disabled} !important;
    pointer-events: none;
    opacity: 0.5;
  }
  .default-color {
    color: #00000091;
  }
  .float-right {
    float: right;
  }
  .align-center {
    text-align: center;
  }

  @media print{
   .noprint{
       display:none;
   }
  }
  
  .normal-text {
    text-transform: none;
  }

  .bold-text {
    font-weight: bold;
  }
  
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
`;

export default GlobalStyle;
