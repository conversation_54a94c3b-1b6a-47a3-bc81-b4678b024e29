import { createTheme, responsiveFontSizes } from '@mui/material/styles';

const muiTheme = createTheme({
  spacing: factor => `${factor * 8}px`,
  palette: {
    primary: {
      main: '#D72638',
      dark: '#B71C2C',
      contrastText: '#fff',
    },
    secondary: {
      main: '#4CAF50',
      dark: '#388E3C',
      contrastText: '#fff',
    },
    background: {
      default: '#FAFAF9',
      paper: '#FFFFFF',
    },
    text: {
      primary: '#1E293B',
      secondary: '#6B7280',
      disabled: '#D1D5DB',
      hint: 'rgba(107, 114, 128, 0.7)',
      green: '#4CAF50',
      yellow: '#F59E0B',
      red: '#D72638',
      britishYellow: '#F59E0B',
    },
    divider: '#E5E7EB',
    grey: {
      50: '#FAFAF9',
      100: '#F3F4F6',
      200: '#E5E7EB',
      300: '#D1D5DB',
      400: '#9CA3AF',
      500: '#6B7280',
      600: '#4B5563',
      700: '#374151',
      800: '#1F2937',
      900: '#111827',
    },
  },
  inherit: {
    contrastText: '#fff',
  },
  typography: {
    fontFamily: ['"Noto Sans"', 'sans-serif'],
    textTransform: 'none',
    fontSize: 13,
    button: {
      fontWeight: 'bold',
    },
  },
  colors: {
    // Apple Red variants
    appleRed: '#D72638',
    appleRedDark: '#B71C2C',
    lightAppleRed: '#FEF2F2',

    // Apple Green variants
    appleGreen: '#4CAF50',
    appleGreenDark: '#388E3C',
    lightAppleGreen: '#F0F9FF',

    // Base colors
    appleWhite: '#FAFAF9',
    pureWhite: '#FFFFFF',

    // Neutral grays
    lightGray: '#F3F4F6',
    gray: '#E5E7EB',
    mediumGray: '#D1D5DB',
    darkGray: '#6B7280',
    charcoal: '#1E293B',

    // Legacy colors (mapped to Apple theme)
    lightBlue: '#F0F9FF',
    lightGreen: '#F0F9FF',
    yellow: '#F59E0B',
    redPrimary: '#D72638',
    redSecondary: '#B71C2C',
    lightRed: '#FEF2F2',
    lightCream: '#FAFAF9',
    lightYellow: '#FEF3C7',
    disabledText: '#D1D5DB',
    black: '#000',
    white: '#fff',
    subtleGray: '#9CA3AF',
    whitishGray: '#E5E7EB',
    lightSapGreen: '#F0F9FF',
    whiteGreen: '#F0F9FF',
    lightYellowL: '#FEF3C7',
    darkYellow: '#F59E0B',
    grayTextColor: '#6B7280',
    lightPrimary: '#FEF2F2',
    paleYellow: '#F59E0B',
    mintCream: '#F0F9FF',
  },
  breakpoints: {
    values: {
      xs: 320,
      sm: 375,
      md: 768,
      lg: 1024,
      xl: 1440,
    },
  },
  overrides: {
    MuiBadge: {
      anchorOriginTopRightCircular: {
        transform: 'scale(0.8) translate(50%, -50%)',
      },
    },
  },
});

export default responsiveFontSizes(muiTheme);
