import { createTheme, responsiveFontSizes } from '@mui/material/styles';

const muiTheme = createTheme({
  spacing: factor => `${factor * 8}px`,
  palette: {
    primary: {
      main: '#993333',
      dark: '#800000',
      contrastText: '#fff',
    },
    text: {
      primary: '#2d3941',
      secondary: '#000',
      disabled: '#7c8891',
      hint: 'rgba(0,185,255,0.71)',
      green: '#377E7F',
      yellow: '#ead215',
      red: 'red',
      britishYellow: '#EDC001',
    },
  },
  inherit: {
    contrastText: '#fff',
  },
  typography: {
    fontFamily: ['"Noto Sans"', 'sans-serif'],
    textTransform: 'none',
    fontSize: 13,
    button: {
      fontWeight: 'bold',
    },
  },
  colors: {
    lightBlue: '#DEF0EE',
    lightGreen: '#FFF6D6',
    lightGray: '#F9F9F9',
    yellow: '#F9D849',
    gray: '#E6EDED',
    redPrimary: '#993333',
    redSecondary: '#800000',
    lightRed: '#F5EBEB',
    lightCream: '#F9F9F9',
    lightYellow: '#FFEFA9',
    disabledText: '#959595',
    black: '#000',
    white: '#fff',
    subtleGray: '#9e9e9e',
    darkGray: '#8A8A8A',
    whitishGray: '#d3d3d3',
    lightSapGreen: '#DAE9EA',
    whiteGreen: '#EEF2F5',
    lightYellowL: '#FFF7E1',
    darkYellow: '#FE9B0E',
    grayTextColor: '#403F42',
    lightPrimary: '#F5EBEB',
    paleYellow: '#FE9B0E',
    mintCream: '#F6FFFE',
  },
  breakpoints: {
    values: {
      xs: 320,
      sm: 375,
      md: 768,
      lg: 1024,
      xl: 1440,
    },
  },
  overrides: {
    MuiBadge: {
      anchorOriginTopRightCircular: {
        transform: 'scale(0.8) translate(50%, -50%)',
      },
    },
  },
});

export default responsiveFontSizes(muiTheme);
