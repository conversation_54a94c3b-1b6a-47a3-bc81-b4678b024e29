import { makeStyles } from '@mui/styles';

const useAtomicStyles = makeStyles(() => ({
  border2: {
    border: '2px solid black',
  },
  borderRight2: {
    borderRight: '2px solid black',
  },
  borderBottom2: {
    borderBottom: '2px solid black !important',
  },
  borderBottom3: {
    borderBottom: '3px solid black',
  },
  marginLeft2: {
    marginLeft: '2px',
  },
  marginTop2: {
    marginTop: '2px',
  },
  marginTop6: {
    marginTop: '6px',
  },
  padding2: {
    padding: '2px',
  },
  padding10: {
    padding: '10px !important',
  },
  padding22: {
    padding: '22px !important',
  },
  paddingLeft2: {
    padding: '2px',
  },
  fontSize20: {
    fontSize: '24px',
  },
  boldFont: {
    fontWeight: 'bold',
  },
  flexRow: {
    display: 'flex',
  },
  flexColumn: {
    display: 'flex',
    flexDirection: 'column',
  },
  centerAlign: {
    alignItems: 'center',
  },
  justifySpaceBetween: {
    justifyContent: 'space-between',
  },
  font12: {
    fontSize: '12px',
  },
  minHeight22: {
    minHeight: '22px',
  },
  colorRed: {
    color: '#D72638',
  },
  marginTop4: {
    marginTop: '4px',
  },
  marginTop12: {
    marginTop: '12px',
  },
  justifyCenter: {
    justifyContent: 'center',
  },
  font16: {
    fontSize: '16px',
  },
  font14: {
    fontSize: '14px',
  },
  textCenter: {
    textAlign: 'center',
  },
  textRight: {
    textAlign: 'right',
  },
  marginRight2: {
    marginRight: '2px',
  },
  marginTop8: {
    marginTop: '8px',
  },
  summaryFont: {
    fontSize: '18px',
    fontWeight: 'bold',
  },
  lightRed: {
    background: '#FEF2F2',
  },
  lightYellow: {
    background: '#FEF3C7',
  },
  lightGreen: {
    background: '#F0F9FF',
  },
  green_3: {
    background: '#DCFCE7',
  },
  green_4: {
    color: '#4CAF50',
  },
  red_1: {
    background: '#FEF2F2',
  },
  red_2: {
    color: '#D72638',
  },
  yellow_1: {
    color: '#F59E0B',
  },
  lightGray: {
    background: '#F3F4F6',
  },
  grayBorder: {
    border: '2px #E5E7EB solid',
  },
  grayText: {
    color: '#6B7280',
  },
  positionRelative: {
    position: 'relative',
  },
}));

export default useAtomicStyles;
