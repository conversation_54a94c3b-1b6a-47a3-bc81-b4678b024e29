import { makeStyles } from '@mui/styles';

const useAtomicStyles = makeStyles(() => ({
  border2: {
    border: '2px solid black',
  },
  borderRight2: {
    borderRight: '2px solid black',
  },
  borderBottom2: {
    borderBottom: '2px solid black !important',
  },
  borderBottom3: {
    borderBottom: '3px solid black',
  },
  marginLeft2: {
    marginLeft: '2px',
  },
  marginTop2: {
    marginTop: '2px',
  },
  marginTop6: {
    marginTop: '6px',
  },
  padding2: {
    padding: '2px',
  },
  padding10: {
    padding: '10px !important',
  },
  padding22: {
    padding: '22px !important',
  },
  paddingLeft2: {
    padding: '2px',
  },
  fontSize20: {
    fontSize: '24px',
  },
  boldFont: {
    fontWeight: 'bold',
  },
  flexRow: {
    display: 'flex',
  },
  flexColumn: {
    display: 'flex',
    flexDirection: 'column',
  },
  centerAlign: {
    alignItems: 'center',
  },
  justifySpaceBetween: {
    justifyContent: 'space-between',
  },
  font12: {
    fontSize: '12px',
  },
  minHeight22: {
    minHeight: '22px',
  },
  colorRed: {
    color: 'red',
  },
  marginTop4: {
    marginTop: '4px',
  },
  marginTop12: {
    marginTop: '12px',
  },
  justifyCenter: {
    justifyContent: 'center',
  },
  font16: {
    fontSize: '16px',
  },
  font14: {
    fontSize: '14px',
  },
  textCenter: {
    textAlign: 'center',
  },
  textRight: {
    textAlign: 'right',
  },
  marginRight2: {
    marginRight: '2px',
  },
  marginTop8: {
    marginTop: '8px',
  },
  summaryFont: {
    fontSize: '18px',
    fontWeight: 'bold',
  },
  lightRed: {
    background: '#FF949A',
  },
  lightYellow: {
    background: '#FDEFC7',
  },
  lightGreen: {
    background: '#D9E4E3',
  },
  green_3: {
    background: '#bad0d1',
  },
  green_4: {
    color: '#377E7F',
  },
  red_1: {
    background: '#FFCFC9',
  },
  red_2: {
    color: '#E52810',
  },
  yellow_1: {
    color: '#FFA700',
  },
  lightGray: {
    background: '#f5f5f5',
  },
  grayBorder: {
    border: '2px #f5f5f5 solid',
  },
  grayText: {
    color: '#607274',
  },
  positionRelative: {
    position: 'relative',
  },
}));

export default useAtomicStyles;
