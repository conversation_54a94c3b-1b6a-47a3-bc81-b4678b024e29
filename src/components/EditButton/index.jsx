import React from 'react';

import { Edit as EditIcon } from '@mui/icons-material';
import { Button, IconButton } from '@mui/material';

const EditButton = ({ href, isEditable, handleEdit, children }) => {
  if (isEditable && children) {
    return children;
  }
  return isEditable && href ? (
    <Icon href={href} />
  ) : isEditable ? (
    <Btn handleEdit={handleEdit} />
  ) : (
    <></>
  );
};

const Icon = ({ href }) => {
  return (
    <IconButton aria-label='Edit' href={href}>
      <EditIcon />
    </IconButton>
  );
};

const Btn = ({ handleEdit }) => {
  return (
    <Button
      size='small'
      variant='contained'
      color='primary'
      onClick={handleEdit}
      className='margin-horizontal'
    >
      Edit
    </Button>
  );
};

export default EditButton;
