import {
  Apartment as ApartmentIcon,
  DepartureBoard as DepartureBoardIcon,
  Home,
  ListAlt as ListAltIcon,
  LocalShipping as LocalShippingIcon,
  Receipt as ReceiptIcon,
  PersonAdd as PersonAddIcon,
  Login as LoginIcon,
  Unarchive as UnarchiveIcon,
  Scale as ScaleIcon,
  Gavel as GavelIcon,
  Token as TokenIcon,
  AttachMoney as AttachMoneyIcon,
  Store as StoreIcon,
  People as PeopleIcon,
  LocalShipping as DeliveryIcon,
  ViewList as ViewListIcon,
  Storefront as StorefrontIcon,
  Security as SecurityIcon,
  DirectionsCar as DirectionsCarIcon,
  Payment as PaymentIcon,
  TrendingUp as TrendingUpIcon,
  LocalParking as LocalParkingIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import EnergySavingsLeafIcon from '@mui/icons-material/EnergySavingsLeaf';

const IconMapping = {
  receipt: ReceiptIcon,
  shipping: LocalShippingIcon,
  harvest: EnergySavingsLeafIcon,
  inventory: ApartmentIcon,
  salesorder: ListAltIcon,
  dcarrivals: DepartureBoardIcon,
  home: Home,
  // New icons for homepage modules
  registration: PersonAddIcon,
  gatein: LoginIcon,
  unloading: UnarchiveIcon,
  grading: ScaleIcon,
  weighment: ScaleIcon,
  auction: GavelIcon,
  tokens: TokenIcon,
  cashadvance: AttachMoneyIcon,
  sales: StoreIcon,
  partnerlist: PeopleIcon,
  materialdelivery: DeliveryIcon,
  sequencing: ViewListIcon,
  catalogue: StorefrontIcon,
  bidderaccess: SecurityIcon,
  trip: DirectionsCarIcon,
  paymentrequest: PaymentIcon,
  templimitbooster: TrendingUpIcon,
  parkingfee: LocalParkingIcon,
  slotbooking: ScheduleIcon,
};

const AppIcons = ({ name, ...restProps }) => {
  const IconComponent = IconMapping[name];

  if (!IconComponent) {
    console.warn(`AppIcons: No icon found for name '${name}'`);
    return <Home {...restProps} />; // Fallback to Home icon
  }

  return <IconComponent {...restProps} />;
};

export default AppIcons;
