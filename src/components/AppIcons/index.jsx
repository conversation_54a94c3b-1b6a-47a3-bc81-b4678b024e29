import {
  Apartment as ApartmentIcon,
  DepartureBoard as DepartureBoardIcon,
  Home,
  ListAlt as ListAltIcon,
  LocalShipping as LocalShippingIcon,
  Receipt as ReceiptIcon,
} from '@mui/icons-material';
import EnergySavingsLeafIcon from '@mui/icons-material/EnergySavingsLeaf';

const IconMapping = {
  receipt: ReceiptIcon,
  shipping: LocalShippingIcon,
  harvest: EnergySavingsLeafIcon,
  inventory: ApartmentIcon,
  salesorder: ListAltIcon,
  dcarrivals: DepartureBoardIcon,
  home: Home,
};

const AppIcons = ({ name, ...restProps }) => {
  const IconComponent = IconMapping[name];

  if (!IconComponent) {
    console.warn(`AppIcons: No icon found for name '${name}'`);
    return <Home {...restProps} />; // Fallback to Home icon
  }

  return <IconComponent {...restProps} />;
};

export default AppIcons;
