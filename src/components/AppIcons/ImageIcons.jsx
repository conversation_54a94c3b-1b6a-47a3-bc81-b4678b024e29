import attachment from 'Assets/attachment.png';
import awardRibbon from 'Assets/award-ribbon-check.png';
import axisBankLogo from 'Assets/axis-bank-logo.png';
import bankNotUploaded from 'Assets/bank-notuploaded.png';
import bankPending from 'Assets/bank-pending.png';
import bankRejected from 'Assets/bank-rejected.png';
import bankVerified from 'Assets/bank-verified.png';
import bankLogo from 'Assets/bankLogo.png';
import bobLogo from 'Assets/bob-logo.png';
import boiLogo from 'Assets/boi_logo.png';
import box from 'Assets/box-close.png';
import cbiLogo from 'Assets/cbi-logo.png';
import userIcon from 'Assets/circle-user-solid.png';
import crateIcon from 'Assets/crate.png';
import crates from 'Assets/crates.png';
import curr_pomo from 'Assets/curr_pomo.png';
import date from 'Assets/date.png';
import filterIcon from 'Assets/filter-icon.svg';
import freeGiftUnderline from 'Assets/free-gift-underline.svg';
import freeGift from 'Assets/free-gift.svg';
import fruitXShortLogo from 'Assets/fruitX-short-logo.svg';
import logo from 'Assets/FruitX.png';
import gift from 'Assets/gift.svg';
import weight from 'Assets/heap.png';
import iciciLogo from 'Assets/icici-logo.png';
import fruitXLogo from 'Assets/KDC-FruitX-logo.png';
import kg from 'Assets/kg.png';
import kycNotUploaded from 'Assets/kyc-notuploaded.png';
import kycPending from 'Assets/kyc-pending.png';
import kycRejected from 'Assets/kyc-rejected.png';
import kycVerified from 'Assets/kyc-verified.png';
import limit from 'Assets/limit.png';
import location from 'Assets/location-dot-solid.png';
import mango from 'Assets/mango.png';
import multifruit from 'Assets/multifruit.png';
import mUpi from 'Assets/mUpi.png';
import orange from 'Assets/orange.png';
import otpNotUploaded from 'Assets/otp-notuploaded.png';
import otpPending from 'Assets/otp-pending.png';
import otpRejected from 'Assets/otp-rejected.png';
import otpVerified from 'Assets/otp-verified.png';
import pack from 'Assets/package.png';
import paid from 'Assets/paid.png';
import percentLogo from 'Assets/percent-logo.gif';
import phone from 'Assets/phone-solid.png';
import pnbLogo from 'Assets/pnb-logo.png';
import pomo from 'Assets/pomo.png';
import Pomo_five from 'Assets/Pomo_five.png';
import Pomo_four from 'Assets/Pomo_four.png';
import Pomo_one from 'Assets/Pomo_one.png';
import Pomo_six from 'Assets/Pomo_six.png';
import Pomo_three from 'Assets/Pomo_three.png';
import Pomo_two from 'Assets/Pomo_two.png';
import RMGBLogo from 'Assets/rmgb-logo.png';
import SBILogo from 'Assets/sbi-logo.png';
import smallUpi from 'Assets/smallUpi.png';
import sold_pomo from 'Assets/sold_pomo.png';
import specialGift from 'Assets/special-gift.svg';
import transaction from 'Assets/transaction.png';
import truck from 'Assets/truck.png';
import truckIcon from 'Assets/truckIcon.png';
import upiNotUploaded from 'Assets/upi-notuploaded.png';
import upiPending from 'Assets/upi-pending.png';
import upiRejected from 'Assets/upi-rejected.png';
import upiVerified from 'Assets/upi-verified.png';
import user from 'Assets/user.png';
import wkg from 'Assets/wKg.png';

const IconMapping = {
  box,
  weight,
  phone,
  location,
  crateIcon,
  userIcon,
  logo,
  crates,
  truck,
  user,
  truckIcon,
  fruitXLogo,
  attachment,
  kycPending,
  kycRejected,
  kycVerified,
  bankPending,
  bankRejected,
  bankVerified,
  otpPending,
  otpRejected,
  otpVerified,
  upiPending,
  upiRejected,
  upiVerified,
  kycNotUploaded,
  bankNotUploaded,
  otpNotUploaded,
  upiNotUploaded,
  kg,
  pack,
  wkg,
  pomo,
  curr_pomo,
  sold_pomo,
  transaction,
  date,
  limit,
  mango,
  multifruit,
  orange,
  paid,
  smallUpi,
  mUpi,
  Pomo_one,
  Pomo_two,
  Pomo_three,
  Pomo_four,
  Pomo_five,
  Pomo_six,
  percentLogo,
  SBILogo,
  fruitXShortLogo,
  axisBankLogo,
  bobLogo,
  boiLogo,
  cbiLogo,
  pnbLogo,
  RMGBLogo,
  iciciLogo,
  bankLogo,
  filterIcon,
  awardRibbon,
  specialGift,
  gift,
  freeGift,
  freeGiftUnderline,
};

const ImageIcons = ({ name, src, ...restProps }) => {
  const icon = src || IconMapping[name];
  return <img src={icon} {...restProps} alt={name} />;
};

export default ImageIcons;
