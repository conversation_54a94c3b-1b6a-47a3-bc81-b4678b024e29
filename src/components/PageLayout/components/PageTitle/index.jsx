import { useState } from 'react';

import {
  ArrowBack as ArrowBackIcon,
  FilterList as FilterListIcon,
} from '@mui/icons-material';
import { Box, Button, Menu, MenuItem, Typography } from '@mui/material';
import { withStyles } from '@mui/styles';

import { useSiteValue } from 'App/SiteContext';

import {
  ArrowWrapper,
  FilterButton,
  LeftSection,
  PageTitleWrapper,
  RightSection,
  TitleWrapper,
} from './styled';

const StyledMenu = withStyles({
  paper: {
    border: '1px solid #d3d4d5',
  },
})(props => (
  <Menu
    elevation={0}
    anchorOrigin={{
      vertical: 'bottom',
      horizontal: 'center',
    }}
    transformOrigin={{
      vertical: 'top',
      horizontal: 'center',
    }}
    {...props}
  />
));

const PageTitle = ({
  showBackHandler,
  showSelectDC,
  showFilterHandler,
  isFilterChanged,
  title,
  titleHelper,
  children,
}) => {
  const { userInfo, setDCId, dcId } = useSiteValue();
  const [anchorDCEl, setAnchorDCEl] = useState(null);

  const handleClose = () => {
    setAnchorDCEl(null);
  };

  const selectDC = id => {
    setDCId(id);
    handleClose();
  };

  return (
    <PageTitleWrapper elevation={0} square>
      <LeftSection>
        <TitleWrapper>
          {showBackHandler && (
            <ArrowWrapper onClick={showBackHandler}>
              <ArrowBackIcon />
            </ArrowWrapper>
          )}
          <Typography variant='h2' component='h2' data-cy='mandi.pageTitle'>
            <b>{title}</b>
          </Typography>
          {!!titleHelper && titleHelper}
          {showSelectDC && (
            <Box
              aria-label='Select DC'
              aria-controls='select-dc'
              aria-haspopup='true'
              data-cy='mandi.selectDC'
              onClick={event =>
                userInfo.dcs?.length > 1 && setAnchorDCEl(event.currentTarget)
              }
            >
              <Button color='primary' variant='outlined' size='small'>
                {(
                  (userInfo.dcs || []).find(d => d.id === parseInt(dcId, 10)) ||
                  {}
                ).name || 'Select DC'}
              </Button>
            </Box>
          )}
          <StyledMenu
            id='select-dc'
            anchorEl={anchorDCEl}
            keepMounted
            open={!!anchorDCEl}
            onClose={handleClose}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'center',
            }}
          >
            {[
              ...userInfo.dcs.map(({ id, name }) => (
                <MenuItem
                  key={id}
                  data-cy={`mandi.DCS.${name}`}
                  onClick={() => selectDC(id)}
                >
                  {name}
                </MenuItem>
              )),
            ]}
          </StyledMenu>
        </TitleWrapper>
        <FilterButton sx={{ display: { xs: 'block', md: 'none' } }}>
          {!!showFilterHandler && (
            <FilterListIcon
              width='2em'
              height='2em'
              data-cy='mandi.filterButton'
              onClick={showFilterHandler}
            />
          )}
        </FilterButton>
      </LeftSection>
      {children && <RightSection>{children}</RightSection>}
      <FilterButton
        active={isFilterChanged}
        sx={{ display: { xs: 'none', sm: 'block' } }}
      >
        {!!showFilterHandler && (
          <FilterListIcon
            data-cy='mandi.filterButton'
            width='2em'
            onClick={showFilterHandler}
          />
        )}
      </FilterButton>
    </PageTitleWrapper>
  );
};

export default PageTitle;
