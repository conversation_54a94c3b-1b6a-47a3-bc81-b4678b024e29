import { useEffect } from 'react';

import AppLoader from 'Components/AppLoader';
import useNotify from 'Hooks/useNotify';

import PageFooter from './components/PageFooter';
import PageTitle from './components/PageTitle/SelectMandi';
import { PageBody } from './styled';

const PageLayout = ({
  isLoading = false,
  children,
  showBackHandler,
  disableMandiSelection = false,
  showSelectMandi = false,
  disableAuctionDate = false,
  showAuctionDate,
  isFilterChanged,
  showFilterHandler,
  title,
  titleHelper,
  titleComponent,
  showDate = false,
  sequenceDate,
  leftSectionFullWidth,
}) => {
  const NotificationBar = useNotify();
  const showError = content => {
    NotificationBar(content, 'error');
  };

  return (
    <>
      <PageTitle
        showSelectMandi={showSelectMandi}
        disableAuctionDate={disableAuctionDate}
        disableMandiSelection={disableMandiSelection}
        showBackHandler={showBackHandler}
        showFilterHandler={showFilterHandler}
        isFilterChanged={isFilterChanged}
        titleHelper={titleHelper}
        title={title}
        showAuctionDate={showAuctionDate}
        showDate={showDate}
        sequenceDate={sequenceDate}
        leftSectionFullWidth={leftSectionFullWidth}
      >
        {titleComponent}
      </PageTitle>
      {isLoading ? <AppLoader /> : children}
    </>
  );
};

PageLayout.Body = PageBody;
PageLayout.Footer = PageFooter;
export default PageLayout;
