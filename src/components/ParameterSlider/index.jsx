import { Box, Slider, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledSlider = styled(Slider)(({ theme }) => ({
  '& .MuiSlider-markLabel': {
    top: '-15px',
    fontSize: '8px',
    color: theme.palette.text.primary,
    backgroundColor: '#d3d3d3',
    borderRadius: '2px',
    padding: '2px 4px',
    transition: 'color 0.3s, background-color 0.3s',
  },
  '& .MuiSlider-markLabelActive': {
    color: '#fff',
    backgroundColor: theme.palette.primary.main,
  },
  '& .MuiSlider-mark': {
    height: '6px',
    width: '2px',
    borderRadius: '2px',
    backgroundColor: theme.palette.primary.main,
    top: '10px',
  },
  '& .MuiSlider-thumb': {
    '&::before': {
      display: 'none',
    },
    display: 'none',
  },
  '& .MuiSlider-track': {
    height: '4px',
    backgroundColor: theme.palette.primary.main,
  },
  '& .MuiSlider-rail': {
    height: '4px',
    opacity: 0.5,
    backgroundColor: theme.palette.grey[400],
  },
}));

// Marks starting from 10 and going up to 100
const marks = [
  { value: 10, label: '1' },
  { value: 20, label: '2' },
  { value: 30, label: '3' },
  { value: 40, label: '4' },
  { value: 50, label: '5' },
  { value: 60, label: '6' },
  { value: 70, label: '7' },
  { value: 80, label: '8' },
  { value: 90, label: '9' },
  { value: 100, label: '10' },
];

function valuetext(value) {
  return `${value}`;
}

const ParameterSlider = ({ handleChange, value, title }) => (
  <Box sx={{ width: 600, marginTop: 4 }}>
    <StyledSlider
      aria-label='Parameter'
      defaultValue={10}
      value={value || 10}
      onChange={handleChange}
      getAriaValueText={valuetext}
      step={null}
      marks={marks.map(mark => ({
        ...mark,
        label: (
          <span
            className={mark.value === value ? 'MuiSlider-markLabelActive' : ''}
          >
            {mark.label}
          </span>
        ),
      }))}
      valueLabelDisplay='auto'
    />
    <Typography>{title}</Typography>
  </Box>
);

export default ParameterSlider;
