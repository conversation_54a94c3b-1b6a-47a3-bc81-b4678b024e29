import { Box, Button, Grid } from '@mui/material';
import { styled } from '@mui/material/styles';

const NumButton = styled(Button)({
  width: '60px',
  height: '40px',
  margin: '4px',
  fontSize: '24px'
});

const ClearButton = styled(Button)({
  width: '135px',
  height: '40px',
  margin: '4px',
  fontSize: '24px',
  backgroundColor: 'rgba(253, 165, 165, 1)',
  color: 'rgba(29, 36, 54, 0.6)',
  fontWeight: 400,
  textTransform: 'none',
  '&:hover': {
    backgroundColor: 'rgba(253, 165, 165, 0.8)'
  }
});

const ScreenKeypad = ({ onClear, onChange, disabled, showClear = true }) => {
  const buttons = [
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z'
  ];

  if (showClear) {
    buttons.push('CLEAR');
  }

  return (
    <Box sx={{ maxWidth: 320 }}>
      <Grid container>
        {buttons.map((btn) => (
          <Grid item xs={btn === 'CLEAR' ? 6 : 3} key={btn}>
            {btn === 'CLEAR' ? (
              <ClearButton
                variant="contained"
                onClick={onClear}
                disabled={disabled}
              >
                {btn}
              </ClearButton>
            ) : (
              <NumButton
                variant="contained"
                color="inherit"
                onClick={() => onChange(btn)}
                disabled={disabled}
              >
                {btn}
              </NumButton>
            )}
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default ScreenKeypad;
