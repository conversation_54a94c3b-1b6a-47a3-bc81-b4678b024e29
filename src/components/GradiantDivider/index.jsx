import { Box, Typography } from '@mui/material';

const GradientDivider = ({
  text,
  hideLeft = false,
  hideRight = false,
  containerProps = {},
  ...props
}) => (
  <Box display='flex' alignItems='center' {...containerProps}>
    {!hideLeft && (
      <Box
        flex={1}
        height='1px'
        sx={{
          background: 'linear-gradient(to right, transparent, #8E8E8E)',
        }}
      />
    )}
    <Typography
      variant='body2'
      color='text.secondary'
      fontWeight='medium'
      {...props}
      sx={{ mx: 2, whiteSpace: 'nowrap' }}
    >
      {text}
    </Typography>
    {!hideRight && (
      <Box
        flex={1}
        height='1px'
        sx={{
          background: 'linear-gradient(to left, transparent, #8E8E8E)',
        }}
      />
    )}
  </Box>
);

export default GradientDivider;
