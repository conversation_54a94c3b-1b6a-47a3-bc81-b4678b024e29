import React from 'react';

import { KeyboardArrowLeft } from '@mui/icons-material';
import { Grid, IconButton, useMediaQuery } from '@mui/material';

import { RightSection } from './styled';

const Sm = ({
  children = null,
  backHandler = () => {},
  selectedId = null,
  rightSection = <></>,
  leftSection = <></>,
  ...props
}) => {
  const matches = useMediaQuery(theme => theme.breakpoints.down('md'));

  const component = () => {
    if (matches) {
      if (selectedId) {
        return (
          <MobileRightSection
            rightSection={rightSection}
            backHandler={backHandler}
          />
        );
      }
      return leftSection;
    }
    return (
      children || (
        <>
          {leftSection}
          {rightSection}
        </>
      )
    );
  };

  return (
    <Grid container {...props}>
      {component()}
    </Grid>
  );
};

const MobileRightSection = ({ rightSection, backHandler }) => {
  return (
    <RightSection>
      <Grid container direction='row'>
        <IconButton
          size='small'
          variant='contained'
          color='primary'
          onClick={backHandler}
        >
          <KeyboardArrowLeft fontSize='large' />
          Back
        </IconButton>
        {rightSection}
      </Grid>
    </RightSection>
  );
};

export default Sm;
