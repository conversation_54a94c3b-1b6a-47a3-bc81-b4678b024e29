import React, { useState } from 'react';

import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from '@mui/material';

import AppButton from 'Components/AppButton';
import { OTP_TYPE } from 'Utilities/constants/otp';

import OtpInput from './OtpInput';
import { styles } from './styled';

const OTP = ({
  open = true,
  phoneNumber = '',
  handleOtp = () => {},
  sendOtpToUser = () => {},
  isOtpIncorrect = false,
  setIsOtpIncorrect = () => {},
  handleClose = () => {},
}) => {
  const [otp, setOtp] = useState('');
  const {
    dialog,
    contactInfo,
    enterOtp,
    resendLink,
    resendOtpWrapper,
    errorText,
  } = styles();

  const verifyEnteredOtp = () => {
    handleOtp(otp);
  };

  const contactInfoText = isOtpIncorrect ? (
    <div className={errorText}>wrong registration code entered</div>
  ) : (
    <div className={contactInfo}>
      code sent to your registered mobile number +91 {phoneNumber}
    </div>
  );

  const otpInput = (
    <OtpInput
      otpVerificationError={isOtpIncorrect}
      otp={otp}
      setOtp={setOtp}
      setIsOtpIncorrect={setIsOtpIncorrect}
      isOtpIncorrect={isOtpIncorrect}
    />
  );
  return (
    <Dialog open={open} className={dialog} maxWidth='xs' onClose={handleClose}>
      <DialogTitle>Registration Code</DialogTitle>
      <DialogContent>
        {contactInfoText}
        <div className={enterOtp}>{otpInput}</div>
        <div className={resendOtpWrapper}>
          didn't receive code ?
          <span className={resendLink} onClick={sendOtpToUser}>
            resend
          </span>
        </div>
      </DialogContent>
      <DialogActions>
        <AppButton
          variant='contained'
          className='normal-text'
          color='primary'
          onClick={verifyEnteredOtp}
        >
          Verify
        </AppButton>
      </DialogActions>
    </Dialog>
  );
};

export default OTP;
