import React, { useEffect, useMemo, useState } from 'react';

import { OTP_TYPE } from 'Utilities/constants/otp';

import { EVENT_NAMES, REGEX_ALPHANUMERIC, REGEX_DIGIT } from './const';
import { CustomInput, styles } from './styled';

const OtpInput = ({
  otpLength = 4,
  otpVerificationError = false,
  otp = '',
  setOtp = () => {},
  setIsOtpIncorrect = () => {},
  type = OTP_TYPE.ALPHA_NUMERIC,
}) => {
  const [showEnterOtp, setShowEnterOtp] = useState(true);

  const { otpGroup } = styles();

  const REGEX_TYPE =
    type === OTP_TYPE.ALPHA_NUMERIC ? REGEX_ALPHANUMERIC : REGEX_DIGIT;

  useEffect(() => {
    const shouldVerifyOtp = `${otp}`.trim().length === otpLength;
    if (!shouldVerifyOtp && otpVerificationError) {
      setIsOtpIncorrect(false);
    }
  }, [otp, otpLength]);

  const otpDigits = useMemo(() => {
    const otpArray = otp.split('');
    const digits = [];
    for (let i = 0; i < otpLength; i += 1) {
      const digit = otpArray[i];
      if (REGEX_TYPE.test(digit)) {
        digits.push(digit);
      } else {
        digits.push('');
      }
    }
    return digits;
  }, [otp, otpLength]);

  const focusOnNextELement = target => {
    const nextInputBox = target.nextElementSibling;
    nextInputBox?.focus();
  };

  const focusOnPreviousElement = target => {
    const previousInputBox = target.previousElementSibling;
    previousInputBox?.focus();
  };

  const onInputChange = (e, idx) => {
    const { target } = e;
    let targetValue = target.value.trim();
    const isValueOfCorrectType = REGEX_TYPE.test(targetValue);

    if (!isValueOfCorrectType && targetValue !== '') {
      return;
    }
    const stringOtp = `${otp}`;
    targetValue = isValueOfCorrectType ? targetValue : ' ';

    const targetValuelength = targetValue.length;

    if (targetValuelength) {
      const newValue =
        stringOtp.substring(0, idx) +
        targetValue +
        stringOtp.substring(idx + 1);

      setOtp(newValue);

      if (!isValueOfCorrectType) {
        return;
      }

      focusOnNextELement(target);
    } else if (targetValuelength === otpLength) {
      setOtp(targetValue);
      target.blur();
    }
  };

  const handleKeyDown = e => {
    const { target, key } = e;
    const targetValue = target.value;

    const { ARROW_LEFT, ARROW_RIGHT, BACKSPACE, ARROW_DOWN, ARROW_UP } =
      EVENT_NAMES;

    if ([ARROW_LEFT, ARROW_UP].includes(key)) {
      e.preventDefault();
      return focusOnPreviousElement(target);
    }

    if ([ARROW_RIGHT, ARROW_DOWN].includes(key)) {
      e.preventDefault();
      return focusOnNextELement(target);
    }

    target.setSelectionRange(0, targetValue.length);

    if (key !== BACKSPACE || targetValue !== '') {
      return;
    }

    focusOnPreviousElement(target);
  };

  const handleOnFocus = e => {
    if (showEnterOtp) {
      setShowEnterOtp(false);
    }
    const { target } = e;
    target.setSelectionRange(0, target.value.length);
  };

  const textColor = otpVerificationError ? 'red' : 'black';

  const inputMode = type === OTP_TYPE.ALPHA_NUMERIC ? 'text' : 'numeric';

  return (
    <>
      {!otp.trim().length && showEnterOtp && <div>Enter Code</div>}
      <div className={otpGroup} onBlur={() => setShowEnterOtp(true)}>
        {otpDigits.map((digit, idx) => (
          <CustomInput
            key={idx}
            type='text'
            inputMode={inputMode}
            autoComplete='one-time-code'
            pattern='\d{1}'
            maxLength={otpLength}
            value={digit}
            onChange={e => onInputChange(e, idx)}
            onKeyDown={handleKeyDown}
            onFocus={handleOnFocus}
            textColor={textColor}
            id={`otp-${idx}`}
          />
        ))}
      </div>
    </>
  );
};

export default OtpInput;
