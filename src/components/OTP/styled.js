import { makeStyles } from '@mui/styles';
import styled from 'styled-components';

export const styles = makeStyles(() => ({
  dialog: {
    '& .MuiPaper-root': {
      textAlign: 'center',
    },
    '& .MuiDialogActions-root': {
      display: 'flex',
      justifyContent: 'center !important',
      marginBottom: '10px',
    },
    '& .MuiTypography-h6': {
      fontWeight: 700,
      fontSize: '20px',
    },
    '& .MuiDialogTitle-root': {
      paddingBottom: 0,
    },
    '& .MuiButtonBase-root': {
      padding: '8px 24px',
    },
  },

  contactInfo: {
    fontSize: '12px',
    padding: '0 10px',
    color: '#008080',
  },

  enterOtp: {
    marginTop: '32px',
    opacity: 0.5,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
  },

  resendOtpWrapper: {
    marginTop: '20px',
    opacity: 0.7,
  },

  resendLink: {
    color: 'red',
    textDecoration: 'underline',
    cursor: 'pointer',
    marginLeft: '4px',
  },

  errorText: {
    color: 'red',
    fontWeight: 700,
    fontSize: '20px',
  },

  otpGroup: {
    display: 'flex',
    width: '35%',
    columnGap: '12px',
    justifyContent: 'center',
  },
}));

export const CustomInput = styled.input(({ textColor }) => ({
  width: '100%',
  height: '1rem',
  border: 'none',
  borderBottom: '2px solid',
  textAlign: 'center',
  fontSize: '0.7rem',
  fontWeight: 'bold',
  color: `${textColor} !important`,
  outline: 'none',
}));
