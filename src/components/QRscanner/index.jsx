import React, { useCallback, useState } from 'react';

import { mdiQrcodeScan } from '@mdi/js';
import Icon from '@mdi/react';
import { Button } from '@mui/material';
import { QrReader } from 'react-qr-reader';

import { ScannerWrapper } from './style';

const FACING_MODE_USER = 'user';
const FACING_MODE_ENVIRONMENT = 'environment';
const constraints = {
  facingMode: 'environment',
};

const QRscanner = ({ handleQRscanner, open, openQR }) => {
  const [facingMode, setFacingMode] = useState(FACING_MODE_ENVIRONMENT);

  const [error, setError] = useState('');
  const handleScan = data => {
    if (data) {
      handleQRscanner(data);
    }
  };

  const handleClick = useCallback(() => {
    setFacingMode(prevState =>
      prevState === FACING_MODE_USER
        ? FACING_MODE_ENVIRONMENT
        : FACING_MODE_USER
    );
  }, []);

  return (
    <div>
      <center>
        {!open ? (
          <Button
            variant='contained'
            size='large'
            color='inherit'
            onClick={() => openQR(true)}
          >
            <Icon
              style={{ padding: 10 }}
              path={mdiQrcodeScan}
              title='QR Scanner'
              size={10}
              color='black'
            />
          </Button>
        ) : (
          <ScannerWrapper>
            <QrReader
              onResult={(result, error) => {
                if (result) {
                  handleScan(result?.text);
                } else {
                  setError(error);
                }
              }}
              constraints={{ ...constraints, facingMode }}
            />
          </ScannerWrapper>
        )}
      </center>
    </div>
  );
};

export default QRscanner;
