import { Box, Typography } from '@mui/material';

const dotStyle = {
  display: 'inline-block',
  width: '10px',
  height: '10px',
  margin: '0 5px',
  backgroundColor: 'primary.main',
  borderRadius: '50%',
  animation: 'blink 1.4s infinite ease-in-out both',
};

const CustomLoader = ({ message = 'Loading...' }) => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100%',
      bgcolor: 'background.default',
      opacity: 1,
      transition: 'opacity 0.3s ease-in-out',
    }}
  >
    <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
      <Box sx={{ ...dotStyle, animationDelay: '0s' }} />
      <Box sx={{ ...dotStyle, animationDelay: '0.2s' }} />
      <Box sx={{ ...dotStyle, animationDelay: '0.4s' }} />
    </Box>
    <Typography mt={2} variant='h6' color='text.secondary'>
      {message}
    </Typography>
    <style>
      {`
          @keyframes blink {
            0%, 80%, 100% { opacity: 0; }
            40% { opacity: 1; }
          }
        `}
    </style>
  </Box>
);

export default CustomLoader;
