import React from 'react';

import FieldCombo from 'Components/FormFields/FieldCombo';
import useCustomerList from 'Hooks/useCustomerList';

const CustomerSelection = ({ name, otherParams = {}, ...props }) => {
  const [partners, getUpdatedPartnersList] = useCustomerList();

  const filterOptions = (options = [], state = {}) => {
    return options.filter(({ name }) =>
      name.toLowerCase().includes(state?.inputValue?.toLowerCase())
    );
  };

  return (
    <FieldCombo
      name={name}
      label='Vendor'
      placeholder='Select Customer'
      variant='outlined'
      options={partners}
      onChangeInput={query => getUpdatedPartnersList(query, otherParams)}
      filterOptions={filterOptions}
      {...props}
    />
  );
};

export default CustomerSelection;
