import React, { useState } from 'react';

import { Button } from '@mui/material';

import CustomModal from 'Components/Modal';

const FormPopUp = props => {
  const [open, setOpen] = useState(false);

  const openModal = () => {
    setOpen(true);
  };

  const closeModal = () => {
    setOpen(false);
  };
  return (
    <>
      <Button
        fullWidth
        color='primary'
        variant='contained'
        style={{ margin: '8px 0' }}
        onClick={openModal}
      >
        {props.buttonName}
      </Button>
      <CustomModal
        isLoading={false}
        title={props.title}
        open={open}
        onClose={() => closeModal()}
        halfScreen
      >
        {React.cloneElement(props.children, { closeModal })}
      </CustomModal>
    </>
  );
};

export default FormPopUp;
