import { Cancel as CancelIcon } from '@mui/icons-material';
import { Fade, Modal, Typography, styled } from '@mui/material';
import PropTypes from 'prop-types';

import AppLoader from 'Components/AppLoader';

const StyledModal = styled(Modal)(() => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
}));

const ModalPaper = styled('div')(({ theme }) => ({
  display: 'flex',
  padding: '10px',
  flexDirection: 'column',
  backgroundColor: theme.palette.background.paper,
  boxShadow: theme.shadows[5],
  maxWidth: '95%',
  maxHeight: 'calc(100vh - 15%)',
  minWidth: '300px',
}));

const ModalHeader = styled('div')(() => ({
  display: 'flex',
  justifyContent: 'space-between',
  position: 'static',
}));

const CloseIcon = styled(CancelIcon)(({ theme }) => ({
  margin: theme.spacing(1, 1),
  fontSize: '24px',
  cursor: 'pointer',
}));

const ModalBody = styled('div')(({ theme }) => ({
  flex: 1,
  display: 'flex',
  flexDirection: 'column',
  overflow: 'auto',
  padding: theme.spacing(1, 1),
  overflowX: 'hidden',
}));

const ModalTitle = styled(Typography)(({ theme }) => ({
  padding: theme.spacing(1, 1),
}));

const ModalFooter = styled('div')(({ theme }) => ({
  padding: theme.spacing(2, 1, 1, 0),
  textAlign: 'right',
  position: 'static',
}));

const EmptyTitle = styled('div')(() => ({
  display: 'flex',
  justifyContent: 'right',
}));

const CarouselModalBody = styled('div')(({ theme }) => ({
  flex: 1,
  display: 'flex',
  flexDirection: 'column',
  padding: theme.spacing(3, 1),
  overflow: 'auto',
}));

const CustomModal = ({
  isLoading,
  title,
  children,
  open,
  footerComponent,
  onClose,
  fullScreen = false,
  halfScreen = false,
  isCloseIcon = true,
  contentSize = false,
  carouselModalbody = false,
  screenHeight = '90%',
  screenWidth = '50%',
  modalBorderRadius = '0',
  dataCy = {},
  ...props
}) => {
  const extraProps = {};

  if (fullScreen) {
    extraProps.style = {
      height: screenHeight || '99%',
      width: screenWidth || '99%',
      maxWidth: 'unset',
      maxHeight: '90vh',
      borderRadius: modalBorderRadius,
    };
  }

  if (halfScreen) {
    extraProps.style = {
      height: screenHeight,
      width: screenWidth || '50%',
      maxWidth: 'unset',
      maxHeight: '90vh',
      borderRadius: modalBorderRadius,
    };
  }

  if (contentSize) {
    extraProps.style = {
      height: 'auto',
      width: screenWidth || '50%',
      maxWidth: 'unset',
      maxHeight: '90vh',
      borderRadius: modalBorderRadius,
    };
  }

  return (
    <StyledModal
      aria-labelledby='transition-modal-title'
      aria-describedby='transition-modal-description'
      open={open}
      closeAfterTransition
      onClose={onClose}
      slotProps={{
        backdrop: {
          timeout: 500,
        },
      }}
      {...props}
    >
      <Fade in={open}>
        <ModalPaper {...extraProps}>
          {title ? (
            <ModalHeader>
              <ModalTitle variant='h6' component='h2'>
                <b>{title}</b>
              </ModalTitle>
              {isCloseIcon && <CloseIcon onClick={onClose} {...dataCy} />}
            </ModalHeader>
          ) : (
            <EmptyTitle>
              {isCloseIcon && <CloseIcon onClick={onClose} {...dataCy} />}
            </EmptyTitle>
          )}
          {isLoading && <AppLoader />}
          {carouselModalbody ? (
            <CarouselModalBody>{children}</CarouselModalBody>
          ) : (
            <ModalBody>{children}</ModalBody>
          )}
          {footerComponent && (
            <ModalFooter>{!!footerComponent && footerComponent}</ModalFooter>
          )}
        </ModalPaper>
      </Fade>
    </StyledModal>
  );
};

CustomModal.propTypes = {
  isLoading: PropTypes.bool,
  title: PropTypes.string,
  children: PropTypes.node,
  open: PropTypes.bool.isRequired,
  footerComponent: PropTypes.node,
  onClose: PropTypes.func.isRequired,
  fullScreen: PropTypes.bool,
  halfScreen: PropTypes.bool,
  isCloseIcon: PropTypes.bool,
  contentSize: PropTypes.bool,
  carouselModalbody: PropTypes.bool,
  screenHeight: PropTypes.string,
  screenWidth: PropTypes.string,
  modalBorderRadius: PropTypes.string,
  dataCy: PropTypes.object,
};

export default CustomModal;
