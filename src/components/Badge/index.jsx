import React from 'react';

const TYPES = {
  default: opacity => `rgba(3, 77, 71, ${opacity})`,
  danger: opacity => `rgba(255, 86, 86, ${opacity})`,
};

const Badge = ({ text = '', type = 'default', style = {} }) => {
  return (
    <div
      style={{
        borderRadius: '0.3rem',
        padding: '0.3rem',
        color: TYPES[type](1),
        backgroundColor: TYPES[type](0.15),
        ...style,
      }}
    >
      {text}
    </div>
  );
};

export default Badge;
