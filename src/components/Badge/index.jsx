import React from 'react';

const TYPES = {
  default: opacity => `rgba(76, 175, 80, ${opacity})`,
  danger: opacity => `rgba(215, 38, 56, ${opacity})`,
};

const Badge = ({ text = '', type = 'default', style = {} }) => {
  return (
    <div
      style={{
        borderRadius: '0.3rem',
        padding: '0.3rem',
        color: TYPES[type](1),
        backgroundColor: TYPES[type](0.15),
        ...style,
      }}
    >
      {text}
    </div>
  );
};

export default Badge;
