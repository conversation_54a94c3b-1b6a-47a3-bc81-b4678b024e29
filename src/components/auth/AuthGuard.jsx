import React from 'react';

import { CircularProgress, Box } from '@mui/material';
import { Navigate, useLocation } from 'react-router-dom';

import routes from '../../config/routes';
import { useAuth } from '../../contexts/AuthContext';

/**
 * AuthGuard component to protect routes that require authentication
 * If user is not authenticated, redirects to login page
 *
 * @param {Object} props - Component props
 * @returns {JSX.Element} Protected route or redirect
 */
const AuthGuard = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
        }}
      >
        <CircularProgress color='primary' />
      </Box>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    // Save the current location to redirect back after login
    return <Navigate to={routes.login} state={{ from: location }} replace />;
  }

  // Render the protected route
  return children;
};

export default AuthGuard;
