import React from 'react';

import { Grid } from '@mui/material';
import { useFormikContext } from 'formik';

import ImageThumb from 'Components/ImageThumb';

const ImageList = ({ name = 'attachments', showClose = true }) => {
  const { values, setFieldValue } = useFormikContext();

  const removeAttachments = index => {
    const { [name]: attachments } = values;
    attachments.splice(index, 1);
    setFieldValue(name, attachments);
  };

  return (
    <Grid container direction='row' alignItems='center'>
      {values?.[name]?.map((file, index) => (
        <ImageThumb
          key={index}
          file={file}
          url={file}
          {...(showClose
            ? { removeAttachment: () => removeAttachments(index) }
            : {})}
        />
      ))}
    </Grid>
  );
};

export default ImageList;
