import {
  FormControl,
  FormControlLabel,
  FormHelperText,
  FormLabel,
  Radio,
  RadioGroup,
} from '@mui/material';
import { Field } from 'formik';
import _get from 'lodash/get';

const FieldRadio = ({
  options,
  name,
  label,
  helperText,
  validate,
  disabled = false,
  row = true,
  ...props
}) => (
  <Field name={name} label={label} validate={validate}>
    {({ field, form: { touched, errors, submitCount } }) => {
      const error =
        _get(touched, field.name) || submitCount
          ? _get(errors, field.name)
          : false;
      return (
        <FormControl component='fieldset' error={!!error}>
          {label && <FormLabel component='legend'>{label}</FormLabel>}
          <RadioGroup
            aria-label={name}
            name={name}
            {...field}
            {...props}
            row={row}
          >
            {options?.map(o => (
              <FormControlLabel
                key={o.value}
                value={o.value}
                label={o.label}
                disabled={disabled}
                control={
                  <Radio
                    color='primary'
                    inputProps={{
                      'data-cy': `mandi.radiogroup.${o.value}`,
                    }}
                  />
                }
              />
            ))}
          </RadioGroup>
          {(error || helperText) && (
            <FormHelperText>{error || helperText}</FormHelperText>
          )}
        </FormControl>
      );
    }}
  </Field>
);

export default FieldRadio;
