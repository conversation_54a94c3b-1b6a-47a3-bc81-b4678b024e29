import {
  Autocomplete,
  Checkbox,
  TextField,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import { makeStyles } from '@mui/styles';
import { Field } from 'formik';
import _get from 'lodash/get';
import _omit from 'lodash/omit';

import ImageIcons from 'Components/AppIcons/ImageIcons.jsx';
import { CASH_AND_CARRY } from 'Utilities/constants/dclRequest';

const useStyles = makeStyles(() => ({
  imgClass: {
    width: '50px',
    marginLeft: '2rem',
  },
  preImageClass: {
    width: '1rem',
    marginRight: '0.5rem',
    color: 'gray',
  },
}));

const FieldCombo = ({
  name,
  label,
  helperText,
  validate,
  options,
  optionLabel,
  onChange,
  onChangeInput,
  inputMinLength = 3,
  icon,
  checkedIcon,
  limitTags = 2,
  multiple = false,
  filterOptions,
  color,
  postImage = false,
  preImage = false,
  preImageName = 'date',
  inputProps = {},
  getOptionDisabled = option =>
    option.remaining_credit_limit !== null &&
    (option.remaining_credit_limit <= 0 || option.is_credit_days_exceeded) &&
    option.enable_credit_limit,
  showCashAndCarryDisabled = false,
  ...props
}) => {
  const theme = useTheme();
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));
  const { imgClass, preImageClass } = useStyles();

  return (
    <Field name={name} label={label} validate={validate}>
      {({
        field,
        form: { touched, errors, submitCount, setFieldValue, setFieldTouched },
      }) => {
        const error =
          _get(touched, field.name) || submitCount
            ? _get(errors, field.name)
            : false;

        return (
          <Autocomplete
            {...props}
            id={field.name}
            options={options}
            limitTags={limitTags}
            multiple={multiple}
            getOptionDisabled={getOptionDisabled}
            getOptionLabel={option =>
              optionLabel ? optionLabel(option) : option?.name || ''
            }
            filterOptions={filterOptions}
            isOptionEqualToValue={(option, val) => option.id === val?.id}
            value={field.value}
            {..._omit(field, 'value')}
            onChange={(_, opt) => {
              setFieldValue(name, opt);
              if (onChange) {
                onChange(opt);
              }
            }}
            onBlur={() => setFieldTouched(name, true)}
            renderOption={(props, option, { selected }) => {
              const label = optionLabel
                ? optionLabel(option)
                : option?.name || '';
              return (
                <li {...props} data-cy={`mandi.fieldcombo.${label}`}>
                  {multiple && (
                    <Checkbox
                      icon={icon}
                      checkedIcon={checkedIcon}
                      style={{ marginRight: 8 }}
                      checked={selected}
                      color='primary'
                    />
                  )}
                  {preImage && (
                    <ImageIcons
                      name={preImageName}
                      disabled={preImageName}
                      className={
                        preImageName === 'user' ? preImageClass : imgClass
                      }
                    />
                  )}
                  {label}
                  {option.is_credit_days_exceeded && postImage && (
                    <ImageIcons name='date' className={imgClass} />
                  )}
                  {option.remaining_credit_limit !== null &&
                    option.remaining_credit_limit <= 0 &&
                    postImage && (
                      <ImageIcons name='limit' className={imgClass} />
                    )}
                  {option.payment_mode === CASH_AND_CARRY &&
                    showCashAndCarryDisabled && (
                      <Typography
                        variant='caption'
                        sx={{ fontWeight: 'bold' }}
                        color='error'
                      >
                        - Cash & Carry Customer
                      </Typography>
                    )}
                </li>
              );
            }}
            ListboxProps={
              isTablet && {
                style: {
                  maxHeight: '160px',
                },
              }
            }
            renderInput={params => (
              <TextField
                {...params}
                type='text'
                label={label}
                error={!!error}
                onChange={e => {
                  if (
                    onChangeInput &&
                    e?.target?.value?.length > inputMinLength
                  ) {
                    onChangeInput(e?.target?.value);
                  }
                }}
                helperText={error || helperText || ''}
                inputProps={{ ...params.inputProps, ...inputProps }}
                {...props}
                style={{ background: color }}
                variant='outlined'
              />
            )}
          />
        );
      }}
    </Field>
  );
};

export default FieldCombo;
