import React from 'react';

import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { Field } from 'formik';
import _get from 'lodash/get';
import PropTypes from 'prop-types';

const FieldDatepicker = ({
  name,
  label,
  helperText,
  validate,
  onChange,
  textFieldProps,
  ...props
}) => {
  return (
    <Field name={name} label={label} validate={validate}>
      {({ field, form: { touched, errors, submitCount, setFieldValue } }) => {
        const error =
          _get(touched, field.name) || submitCount
            ? _get(errors, field.name)
            : false;
        return (
          <DatePicker
            label={label}
            value={field.value ? dayjs(field.value) : null}
            onChange={date => {
              setFieldValue(
                name,
                dayjs(
                  date?.set('hour', 0).set('minute', 0).set('second', 0)
                ).valueOf()
              );
              if (onChange) {
                onChange(
                  dayjs(
                    date?.set('hour', 0).set('minute', 0).set('second', 0)
                  ).valueOf()
                );
              }
            }}
            error={!!error}
            helperText={error || helperText || ''}
            slotProps={{
              textField: {
                size: 'small',
                ...textFieldProps,
              },
            }}
            {...props}
          />
        );
      }}
    </Field>
  );
};

FieldDatepicker.propTypes = {
  name: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
};

export default FieldDatepicker;
