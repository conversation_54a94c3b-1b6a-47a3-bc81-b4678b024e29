import { TextField } from '@mui/material';
import { Field } from 'formik';
import _get from 'lodash/get';

import theme from '../../../theme';

const fieldStyle = {
  '& .MuiOutlinedInput-root': {
    borderRadius: '4px',
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      border: `1px solid ${theme.colors.paleYellow}`,
    },
    '&:hover .MuiOutlinedInput-notchedOutline': {
      border: `1px solid ${theme.colors.paleYellow}`,
    },
  },
  '& .MuiOutlinedInput-notchedOutline': {
    border: `1px solid ${theme.colors.paleYellow}`,
  },
  '& .MuiInputLabel-root': {
    color: `${theme.colors.paleYellow}`,
  },
};
const FieldInput = ({
  name,
  type,
  label,
  helperText,
  validate,
  validationErrorsStyle = false,
  ...props
}) => (
  <Field name={name} label={label || ''} validate={validate}>
    {({ field, form: { touched, errors, submitCount } }) => {
      const error =
        _get(touched, field.name) || submitCount
          ? _get(errors, field.name)
          : false;

      return (
        <TextField
          type={type}
          label={label || ''}
          error={!!error}
          helperText={error || helperText || ''}
          className='fieldInput'
          sx={validationErrorsStyle ? fieldStyle : {}}
          {...field}
          {...props}
          //! TODO Write a better approach
        />
      );
    }}
  </Field>
);

export default FieldInput;
