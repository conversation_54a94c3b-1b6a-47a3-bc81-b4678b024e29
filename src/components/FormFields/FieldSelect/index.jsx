import {
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
  Typography,
} from '@mui/material';
import { makeStyles } from '@mui/styles';
import { Field } from 'formik';
import _get from 'lodash/get';

const useStyles = makeStyles({
  label: {
    background: '#fff',
  },
});

const FieldSelect = ({
  options,
  name,
  label,
  helperText,
  validate,
  InputLabelProps,
  showNone = true,
  style,
  fullWidth = true,
  inputProps = {},
  dataCY,
  ...props
}) => {
  const classes = useStyles();

  return (
    <Field name={name} label={label} validate={validate}>
      {({ field, form: { touched, errors, submitCount } }) => {
        const error =
          _get(touched, field.name) || submitCount
            ? _get(errors, field.name)
            : false;
        return (
          <FormControl
            key={name}
            error={!!error}
            data-cy={dataCY}
            fullWidth={fullWidth}
          >
            <InputLabel
              shrink
              className={style || classes.label}
              required={InputLabelProps?.required || false}
              id={`${name}-label`}
              htmlFor={name}
            >
              {label}
            </InputLabel>
            <Select
              labelId={name}
              label={label}
              id={name}
              name={name}
              error={!!error}
              {...field}
              {...props}
              value={field.value || ''}
              inputProps={{
                key: name,
                id: 'outlined-age-native-simple',
                size: 'small',
                className: 'MuiOutlinedInput-inputMarginDense',
                ...inputProps,
              }}
            >
              {showNone && (
                <MenuItem value=''>
                  <em>None</em>
                </MenuItem>
              )}
              {options.map(({ text, value, description }) => (
                <MenuItem key={value} value={value}>
                  <Typography style={{ marginRight: 10 }}>{text}</Typography>
                  {description && (
                    <Typography
                      component='div'
                      variant='caption'
                      className='disabled-text'
                      color='textPrimary'
                    >
                      {description}
                    </Typography>
                  )}
                </MenuItem>
              ))}
            </Select>
            {(error || helperText) && (
              <FormHelperText>{error || helperText}</FormHelperText>
            )}
          </FormControl>
        );
      }}
    </Field>
  );
};

export default FieldSelect;
