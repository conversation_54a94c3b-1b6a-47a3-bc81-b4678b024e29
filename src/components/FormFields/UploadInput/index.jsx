import React from 'react';

import { Button, Typography } from '@mui/material';
import { Field, useFormikContext } from 'formik';
import _get from 'lodash/get';
import PropTypes from 'prop-types';

const UploadInput = React.forwardRef(
  (
    {
      name,
      type = 'file',
      size = 'small',
      label,
      validate,
      accept,
      handleChange,
      buttonVariant,
      renderCustomButton,
      style = {},
      disabled = false,
      dataCy = {},
      ...props
    },
    ref
  ) => {
    const { values } = useFormikContext();

    return (
      <Field name={name} label={label} validate={validate}>
        {({ field, form: { touched, errors, submitCount, setFieldValue } }) => {
          const error =
            _get(touched, field.name) || submitCount
              ? _get(errors, field.name)
              : false;

          return (
            <label htmlFor={name}>
              {!renderCustomButton && (
                <Button
                  variant={buttonVariant || 'contained'}
                  color='primary'
                  component='span'
                  size={size}
                  error={error || ''}
                  style={style}
                  disabled={disabled}
                  {...dataCy}
                >
                  {label || 'Upload'}
                </Button>
              )}
              {renderCustomButton && renderCustomButton()}
              {!!error && (
                <Typography variant='body2' color='error'>
                  {error}
                </Typography>
              )}
              <input
                accept={accept}
                id={name}
                style={{ display: 'none' }}
                multiple
                type={type}
                ref={ref}
                disabled={disabled}
                data-cy='mandi.documentsUpload'
                {...props}
                onChange={event => {
                  setFieldValue(field.name, [
                    ...(values?.[name] || []),
                    ...Array.from(event.target.files),
                  ]);

                  if (handleChange) {
                    handleChange([
                      ...(values?.[name] || []),
                      ...Array.from(event.target.files),
                    ]);
                  }
                }}
              />
            </label>
          );
        }}
      </Field>
    );
  }
);

UploadInput.displayName = 'UploadInput';

UploadInput.propTypes = {
  name: PropTypes.string.isRequired,
};

export default UploadInput;
