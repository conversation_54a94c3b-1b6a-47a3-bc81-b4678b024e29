import React, { useCallback, useEffect, useRef, useState } from 'react';

import {
  AutorenewRounded as AutorenewRoundedIcon,
  CancelOutlined as CancelOutlinedIcon,
  Clear as ClearIcon,
  Done as TickIcon,
  Highlight as HighlightIcon,
} from '@mui/icons-material';
import Webcam from 'react-webcam';

import ImageThumb from 'Components/ImageThumb';
import { getImageLocalPath } from 'Utilities';

import { ImageListWrapper, WebCamWrapper } from './styled';

const FACING_MODE_USER = 'user';
const FACING_MODE_ENVIRONMENT = 'environment';
const videoConstraints = {
  facingMode: FACING_MODE_ENVIRONMENT,
};

const VIEW_TYPES = {
  FULL_SCREEN: 'fullScreen',
};

const STYLES = {
  fullScreen: {
    parent: {
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      background: 'black',
      zIndex: '100',
    },
    cam: {
      position: 'relative',
      top: '50%',
      transform: 'translateY(-50%)',
    },
  },
};

const WebCamCapture = ({
  showImagePreview = true,
  constraints = {},
  saveImages,
  Webstyle,
  handleUpload,
  setPhotos,
  upload,
  viewType,
  closeAfterClick,
}) => {
  const webcamRef = useRef(null);
  const [facingMode, setFacingMode] = useState(FACING_MODE_ENVIRONMENT);
  const [images, setImages] = useState([]);
  const [status, setStatus] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const didMount = useRef(false);

  const handleClick = useCallback(() => {
    setFacingMode(prevState =>
      prevState === FACING_MODE_USER
        ? FACING_MODE_ENVIRONMENT
        : FACING_MODE_USER
    );
  }, []);

  const capture = () => {
    const picture = webcamRef.current.getScreenshot();
    const img = dataURItoBlob(picture);
    const resultFile = new File([img], `${webcamRef.current.stream.id}.png`, {
      type: 'image/png',
    });
    if (viewType === VIEW_TYPES.FULL_SCREEN) {
      setShowPreview(resultFile);
      if (closeAfterClick) handleUpload();
    } else {
      saveImages(resultFile);
      setImages([...images, resultFile]);
    }
  };

  function dataURItoBlob(dataURI) {
    const byteString = atob(dataURI?.split(',')[1]);
    const ab = new ArrayBuffer(byteString?.length);
    //! create a view into the buffer
    const ia = new Uint8Array(ab);
    //! set the bytes of the buffer to the correct values
    for (let i = 0; i < byteString.length; i += 1) {
      ia[i] = byteString.charCodeAt(i);
    }
    //! write the ArrayBuffer to a blob
    const bb = new Blob([ab]);
    return bb;
  }

  const removeAttachments = index => {
    images.splice(index, 1);
    setImages([...images]);
    setPhotos(images);
  };

  useEffect(() => {
    if (!didMount.current) {
      didMount.current = true;
    } else {
      if (!('mediaDevices' in window.navigator)) {
        alert('Media Devices not available. Use HTTPS!');
      }

      const { mediaDevices } = window.navigator;

      mediaDevices.enumerateDevices().then(devices => {
        const cameras = devices.filter(device => device.kind === 'videoinput');
        if (cameras?.length === 0) {
          alert(
            'No camera found. If your device has camera available, check permissions.'
          );
          return;
        }

        const camera = cameras[cameras?.length - 1];
        mediaDevices
          .getUserMedia({
            video: {
              deviceId: camera?.deviceId,
            },
          })
          .then(stream => {
            const track = stream.getVideoTracks()[0];
            if (!track.getCapabilities().torch) {
              alert('Torch not available');
            } else {
              track.applyConstraints({
                advanced: [
                  {
                    torch: status,
                  },
                ],
              });
            }
          });
      });
    }
  }, [status]);

  const handleSetImages = image => {
    setShowPreview(null);
    if (!image && closeAfterClick) handleUpload();

    if (!image) return;

    saveImages(image);
    setImages([...images, image]);
  };

  if (showPreview) {
    const fileSrc = showPreview?.type ? getImageLocalPath(showPreview) : null;

    return (
      <WebCamWrapper>
        <div style={STYLES.fullScreen.parent}>
          <img
            src={fileSrc}
            alt='preview images'
            style={STYLES.fullScreen.cam}
          />
          <button
            type='button'
            className='btn btn-primary'
            onClick={() => handleSetImages(showPreview)}
            data-cy='mandi.webCamCapture.preview'
          >
            <TickIcon />
          </button>
          <button
            type='button'
            className='btn btn-primary cancel-btn'
            onClick={() => handleSetImages()}
            data-cy='mandi.webCamCapture.clear'
          >
            <ClearIcon />
          </button>
        </div>
      </WebCamWrapper>
    );
  }

  return (
    <>
      {!!showImagePreview &&
        images?.map((_file, index) => (
          <ImageListWrapper key={index} style={{ margin: '1rem' }}>
            <ImageThumb
              key={index}
              file={_file}
              url={_file}
              style={{ margin: '0.3rem' }}
              removeAttachment={() => removeAttachments(index)}
              dataCy={{ 'data-cy': 'mandi.webCam.removeAttachements' }}
            />
          </ImageListWrapper>
        ))}
      {upload && (
        <WebCamWrapper fullScreen={viewType === VIEW_TYPES.FULL_SCREEN}>
          <CancelOutlinedIcon
            className='cancel-icon'
            color='error'
            cursor='pointer'
            style={{ position: 'absolute', top: '0', right: '0' }}
            onClick={() => {
              handleUpload();
              setStatus(false);
            }}
            data-cy='mandi.webCam.close'
          />
          <div style={STYLES[viewType]?.parent || {}}>
            <Webcam
              audio={false}
              ref={webcamRef}
              screenshotFormat='image/png'
              height={Webstyle?.height}
              width={Webstyle?.width}
              videoConstraints={{
                ...videoConstraints,
                ...constraints,
                facingMode,
              }}
              style={STYLES[viewType]?.cam || {}}
            />
            <button
              onClick={e => {
                e.preventDefault();
                capture();
              }}
              type='button'
              className='btn-danger'
              data-cy='mandi.webCam.capture'
            />
            <button
              type='button'
              className='btn btn-primary'
              onClick={handleClick}
              data-cy='mandi.webCam.reload'
            >
              <AutorenewRoundedIcon />
            </button>
            <button
              type='button'
              className='btn btn-primary flash-btn'
              onClick={() => setStatus(!status)}
              data-cy='mandi.webCam.flash'
            >
              <HighlightIcon />
            </button>
            {viewType === VIEW_TYPES.FULL_SCREEN && (
              <button
                type='button'
                className='btn btn-primary cancel-btn'
                onClick={() => {
                  handleUpload();
                  setStatus(false);
                }}
                data-cy='mandi.webCam.cancelButton'
              >
                <CancelOutlinedIcon />
              </button>
            )}
          </div>
        </WebCamWrapper>
      )}
    </>
  );
};
export default WebCamCapture;
