import React, { forwardRef, useEffect, useState } from 'react';

import { PhoneInTalk as PhoneInTalkIcon } from '@mui/icons-material';
import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  Grid,
  TableBody,
  TableCell as MuiTableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { withStyles } from '@mui/styles';
import { Converter, hiIN } from 'any-number-to-words';
import { isEmpty, sumBy } from 'lodash';
import { getFarmerBill } from 'Services/bills';
import { getChargeableWeight } from 'Utilities/billManager';
import { CallerCenterConst, FarmerBillConst } from 'Utilities/constants/bills';
import { roundToTwoDecimal } from 'Utilities/mathExpert';

import AppButton from 'Components/AppButton';
import { capitalize, toFixedNumber } from 'Utilities';
import { ADMIN_CONFIG_UNIT, INWARD_TYPE } from 'Utilities/constants';
import { currency } from 'Utilities/currencyFormatter.jsx';

import useAtomicStyles from '../../theme/AtomicCss';
import ImageIcons from '../AppIcons/ImageIcons.jsx';

import FarmerBillConfig from './FarmerBillConfig';

const TableCell = withStyles({
  root: {
    borderBottom: 'none',
    padding: 5,
    fontSize: '12px',
  },
  borderRight: {
    borderRight: '1px solid',
  },
})(MuiTableCell);

const FarmerBill = forwardRef((props, ref) => {
  const {
    showDialog = false,
    toggleShowBill,
    handlePrintHelper,
    mandiId,
    auctionDate,
    inwardType,
    token,
  } = props;
  const {
    borderRight2,
    borderBottom2,
    marginLeft2,
    marginTop2,
    fontSize20,
    boldFont,
    border2,
    flexRow,
    centerAlign,
    justifySpaceBetween,
    font12,
    minHeight22,
    marginTop4,
    marginTop12,
    justifyCenter,
    textCenter,
    textRight,
    marginRight2,
    marginTop8,
    paddingLeft2,
    flexColumn,
    padding10,
  } = useAtomicStyles();

  const [data, setFarmerData] = useState();

  const {
    generalDetailsConfig = [],
    bankDetailsConfig = [],
    billDetails = [],
    deliveryDetailsConfig = [],
    signatureConfig = [],
    announcementConfig = [],
    lotsConfigCrates,
    lotsConfigTruck,
    expenseConfig = [],
  } = FarmerBillConfig;

  useEffect(() => {
    getFarmerBill({
      token,
      auctionDate,
      mandiId,
      isFarmerBill: true,
    })
      .then(({ responseData = {} }) => {
        if (responseData) {
          setFarmerData(
            responseData || { mandi_details: {}, bank_details: {}, lots: [] }
          );
        }
      })
      .catch(err => console.log('Error in api call getFarmerBill', err));
  }, []);

  if (isEmpty(data)) return <></>;

  const isInwardTypeTruck = inwardType === INWARD_TYPE.TRUCK;

  const lotsConfig = isInwardTypeTruck ? lotsConfigTruck : lotsConfigCrates;

  const totalAmount = data?.lots?.reduce(
    (total, { net_weight = 0, selling_price = 0, chute = 0 }) => {
      return (
        total +
        net_weight * selling_price * (isInwardTypeTruck ? 1 - chute / 100 : 1)
      );
    },
    0
  );

  const totalNetWeight = data?.lots?.reduce((total, { net_weight = 0 }) => {
    total += net_weight;
    return total;
  }, 0);

  const totalCrates = data?.lots?.reduce((total, { units = 0 }) => {
    total += units;
    return total;
  }, 0);

  const totalWarehousingCharge = () => {
    const {
      mandi_details: {
        warehouse_fee_for_truck = 0,
        warehouse_fee_for_crate = 0,
        unit_for_config,
      } = {},
    } = data;
    return isInwardTypeTruck
      ? warehouse_fee_for_truck * totalNetWeight
      : unit_for_config === ADMIN_CONFIG_UNIT.KG
        ? warehouse_fee_for_crate * totalNetWeight
        : warehouse_fee_for_crate * totalCrates;
  };

  const totalDiscount = () => {
    const {
      mandi_details: {
        discount_on_warehouse_fee_for_crate = 0,
        unit_for_config,
      } = {},
    } = data;
    return toFixedNumber(
      isInwardTypeTruck
        ? 0
        : unit_for_config === ADMIN_CONFIG_UNIT.KG
          ? discount_on_warehouse_fee_for_crate * totalNetWeight
          : discount_on_warehouse_fee_for_crate * totalCrates,
      2
    );
  };

  const totalExpense = totalWarehousingCharge() - totalDiscount();

  const engConverter = new Converter();
  const hinConverter = new Converter(hiIN);

  return (
    <Dialog
      maxWidth='md'
      fullWidth
      open={showDialog && !isEmpty(data)}
      onClose={toggleShowBill}
      id='dialog'
    >
      <DialogContent ref={ref}>
        <Box className={border2}>
          <Box
            className={[
              borderBottom2,
              flexRow,
              centerAlign,
              justifySpaceBetween,
            ]}
            sx={{ padding: '2px 0' }}
          >
            <Typography
              variant='body1'
              className={[fontSize20, boldFont, marginLeft2]}
            >
              Bill of Supply
            </Typography>
            <ImageIcons name='fruitXLogo' width='100' />
          </Box>
          <Box className={[borderBottom2, flexRow]}>
            <Grid xs={5} className={[borderRight2, marginLeft2]}>
              {generalDetailsConfig.map((conf, index) => {
                return (
                  <Typography
                    component='div'
                    className={[flexRow, font12]}
                    key={index}
                  >
                    <Grid xs={3} className={boldFont}>
                      {conf.key}
                    </Grid>
                    <Grid xs={9}>{data[conf.value]}</Grid>
                  </Typography>
                );
              })}
            </Grid>
            <Grid xs={3} className={[borderRight2, marginLeft2]}>
              {bankDetailsConfig.map((conf, index) => {
                return (
                  <Typography
                    component='div'
                    className={[flexRow, font12]}
                    key={index}
                  >
                    <Grid
                      xs={6}
                      className={[boldFont, conf.key ? '' : minHeight22]}
                    >
                      {conf.key}
                    </Grid>
                    <Grid xs={6} className={[textRight, marginRight2]}>
                      {data?.bank_details[conf.value]}
                    </Grid>
                  </Typography>
                );
              })}
            </Grid>
            <Grid xs={4} className={marginLeft2}>
              {billDetails.map((conf, index) => {
                return (
                  <Typography
                    component='div'
                    className={[flexRow, font12]}
                    key={index}
                  >
                    <Grid
                      xs={6}
                      className={[boldFont, conf.key ? '' : minHeight22]}
                    >
                      {conf.key}
                    </Grid>
                    <Grid xs={6} className={[textRight, marginRight2]}>
                      {conf.helper
                        ? conf.helper(data[conf.value])
                        : data[conf.value]}
                    </Grid>
                  </Typography>
                );
              })}
            </Grid>
          </Box>
          <Box className={[marginLeft2, borderBottom2]}>
            {deliveryDetailsConfig.map((conf, index) => {
              return (
                <Typography
                  component={conf.singleLine ? 'span' : 'div'}
                  style={conf.color ? { color: conf.color } : {}}
                  key={index}
                >
                  {!Array.isArray(conf.value) && (
                    <>
                      <Typography component='span' className={font12}>
                        {conf.key}{' '}
                      </Typography>
                      <Typography component='span' className={font12}>
                        {conf.isHardCoded
                          ? conf.value
                          : conf.isGst
                            ? data?.gst_number
                            : data?.mandi_details[conf.value]}
                      </Typography>
                    </>
                  )}
                  {Array.isArray(conf.value) && (
                    <>
                      <Typography component='span' className={font12}>
                        {conf.key}{' '}
                      </Typography>
                      <Typography component='span' className={font12}>
                        {conf.value.map((val, idx) => {
                          return `${data?.mandi_details[val]}${
                            idx === conf.value.length - 1
                              ? conf.endsWith
                              : conf.seperator
                                ? conf.seperator
                                : ''
                          }`;
                        })}
                      </Typography>
                    </>
                  )}
                </Typography>
              );
            })}
          </Box>
          <Box className={[borderBottom2, flexRow]}>
            <Grid xs={8} className={[borderRight2, flexRow]}>
              <TableContainer>
                <TableHead
                  style={{ height: isInwardTypeTruck ? '145.5px' : '100px' }}
                >
                  <TableRow>
                    {lotsConfig.map((conf, idx) => {
                      return (
                        <TableCell
                          key={idx}
                          className={[
                            idx !== lotsConfig.length - 1 ? borderRight2 : '',
                            justifyCenter,
                          ]}
                          style={{ borderBottom: '2px solid black' }}
                        >
                          {conf}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data?.lots?.map((lotsData, index) => {
                    return (
                      <TableRow key={index}>
                        <TableCell>{lotsData?.product_name}</TableCell>
                        <TableCell className={textCenter}>
                          {lotsData?.hsn_number}
                        </TableCell>
                        <TableCell className={textCenter}>
                          {isInwardTypeTruck
                            ? `${lotsData?.chute}%`
                            : lotsData?.units}
                        </TableCell>
                        <TableCell className={textCenter}>
                          {lotsData?.net_weight}
                        </TableCell>
                        {isInwardTypeTruck && (
                          <TableCell className={textCenter}>
                            {getChargeableWeight(
                              lotsData?.net_weight,
                              lotsData?.chute
                            )}
                          </TableCell>
                        )}
                        <TableCell className={textCenter}>
                          {lotsData?.selling_price}
                        </TableCell>
                        <TableCell className={textRight}>
                          {roundToTwoDecimal(
                            (lotsData?.net_weight || 0) *
                              (lotsData?.selling_price || 0) *
                              (isInwardTypeTruck
                                ? 1 - (lotsData?.chute || 0) / 100
                                : 1)
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                  <br />
                  <br />
                  <br />
                  <TableRow>
                    <TableCell colSpan={2}>Total</TableCell>
                    <TableCell className={textCenter}>
                      {sumBy(data?.lots, isInwardTypeTruck ? 'chute' : 'units')}
                      {isInwardTypeTruck ? '%' : ''}
                    </TableCell>
                    <TableCell colSpan={1} className={textCenter}>
                      {sumBy(data?.lots, 'net_weight')}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell colSpan={3} />
                    <TableCell colSpan={2} align='left'>
                      Total Amount({FarmerBillConst.TOTAL_AMOUNT})
                    </TableCell>
                    <TableCell className={textRight} colSpan={3}>
                      {currency(totalAmount)}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell colSpan={3} />
                    <TableCell colSpan={2} align='left'>
                      Less Expense({FarmerBillConst.EXPENSES_BALANCE})
                    </TableCell>
                    <TableCell className={textRight} colSpan={3}>
                      {currency(totalExpense)}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell colSpan={3} />
                    <TableCell
                      colSpan={2}
                      style={{ fontSize: '14px', fontWeight: 'bold' }}
                      align='left'
                    >
                      Net Amount({FarmerBillConst.NET_AMOUNT})
                    </TableCell>
                    <TableCell
                      colSpan={3}
                      style={{ fontSize: '14px' }}
                      className={textRight}
                    >
                      {currency(totalAmount - totalExpense)}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </TableContainer>
            </Grid>
            <Grid xs={4} className={flexRow}>
              <TableContainer>
                <TableHead
                  style={{ height: isInwardTypeTruck ? '145.5px' : '100px' }}
                >
                  <TableRow>
                    {expenseConfig.map((conf, idx) => {
                      return (
                        <TableCell
                          key={idx}
                          style={{ borderBottom: '2px solid black' }}
                          className={[
                            idx !== expenseConfig.length - 1
                              ? borderRight2
                              : '',
                            justifyCenter,
                          ]}
                        >
                          {conf}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow>
                    <TableCell
                      className={[borderRight2, borderBottom2, padding10]}
                    >
                      Warehousing({FarmerBillConst.WAREHOUSING})
                    </TableCell>
                    <TableCell className={[textRight, borderBottom2]}>
                      {totalWarehousingCharge()}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell
                      className={[borderRight2, borderBottom2, padding10]}
                    >
                      Discount({FarmerBillConst.DISCOUNT})
                    </TableCell>
                    {/* Should not appear for truck type */}
                    <TableCell className={[textRight, borderBottom2]}>
                      {totalDiscount() ? `- ${totalDiscount()}` : '-'}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell />
                  </TableRow>
                  <TableRow>
                    <TableCell />
                  </TableRow>
                  <TableRow>
                    <TableCell />
                  </TableRow>
                  <TableRow>
                    <TableCell
                      style={{
                        padding: '22px 22px 0 5px',
                        position: 'relative',
                        bottom: '-10px',
                      }}
                    >
                      Total Expenses({FarmerBillConst.EXPENSES_BALANCE})
                    </TableCell>
                    <TableCell
                      className={textRight}
                      style={{ position: 'relative', bottom: '-10px' }}
                    >
                      {currency(totalExpense)}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </TableContainer>
            </Grid>
          </Box>
          <Grid container>
            <Grid item xs={8} className={[borderRight2]}>
              <Box className={[borderBottom2, paddingLeft2]}>
                <Box
                  className={[flexRow, marginLeft2, marginRight2, marginTop2]}
                >
                  <Typography
                    component='div'
                    style={{ width: '30%' }}
                    className={font12}
                  >
                    Amount in Words:
                  </Typography>
                  <Typography component='div' className={font12}>
                    <b>
                      {capitalize(
                        engConverter.toWords(totalAmount - totalExpense)
                      )}
                    </b>
                  </Typography>
                </Box>
                <Box className={[flexRow, marginLeft2, marginTop8]}>
                  <Typography
                    component='div'
                    style={{ width: '30%' }}
                    className={font12}
                  >
                    राशि शब्दों में:
                  </Typography>
                  <Typography component='div' className={font12}>
                    <b>{hinConverter.toWords(totalAmount - totalExpense)}</b>
                  </Typography>
                </Box>
              </Box>
              <Grid className={[marginTop12, paddingLeft2]}>
                {announcementConfig.map((conf, index) => {
                  return (
                    <Typography component='div' className={font12} key={index}>
                      {conf}
                    </Typography>
                  );
                })}
              </Grid>
              <Grid
                className={[flexRow, marginTop12, paddingLeft2, centerAlign]}
              >
                <Typography className={[fontSize20, marginRight2]}>
                  {CallerCenterConst.TITLE}
                </Typography>
                <Typography className={[fontSize20]}>
                  <PhoneInTalkIcon className={[flexRow]} />
                </Typography>
                <Typography className={[fontSize20]}>
                  {CallerCenterConst.FARMER_CONTACT}
                </Typography>
              </Grid>
              <Grid className={[flexRow, paddingLeft2]}>
                <Typography component='div' className={[font12]}>
                  Generated on {new Date().toISOString().split('T')[0]} at{' '}
                  {new Date().toLocaleTimeString()}
                </Typography>
              </Grid>
            </Grid>
            <Grid
              item
              xs={4}
              className={[flexColumn, justifySpaceBetween, paddingLeft2]}
            >
              <div>
                {signatureConfig.map((signature, idx) => {
                  return (
                    <Typography
                      component='div'
                      className={[boldFont, font12]}
                      key={idx}
                    >
                      {signature}
                    </Typography>
                  );
                })}
              </div>
              <Typography component='div' className={[marginTop4, font12]}>
                <Typography
                  component='div'
                  className={borderBottom2}
                  style={{ marginTop: '40px', width: '85%' }}
                />
                Place of Supply:{' '}
                <Typography component='span' className={font12}>
                  {data?.mandi_details?.state}
                </Typography>
              </Typography>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
      <DialogActions>
        <AppButton
          color='primary'
          size='small'
          onClick={handlePrintHelper}
          variant='contained'
          className={marginTop2}
        >
          Print
        </AppButton>
      </DialogActions>
    </Dialog>
  );
});

export default FarmerBill;
