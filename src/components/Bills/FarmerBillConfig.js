import {
  BankDetailsConst,
  FarmerBillConst,
  SignatureConst,
} from 'Utilities/constants/bills';

import { getFormattedDate } from 'Utilities/dateUtils';

const FarmerBillConfig = {
  generalDetailsConfig: [
    { key: 'From', value: '' },
    { key: FarmerBillConst.NAME, value: 'farmer_name' },
    { key: FarmerBillConst.PHONE_NUMBER, value: 'phone_number' },
    { key: FarmerBillConst.ADDRESS, value: 'address' },
    { key: 'GSTIN:', value: '' },
    { key: FarmerBillConst.TRANSPORT, value: '' },
    { key: FarmerBillConst.VEHICLE_NO, value: 'vehicle_number' },
  ],
  bankDetailsConfig: [
    { key: '', value: '' },
    { key: BankDetailsConst.BANK_NAME, value: 'bank_name' },
    { key: BankDetailsConst.ACCOUNTS_NO, value: 'account_number' },
    { key: BankDetailsConst.IFSC, value: 'ifsc' },
  ],
  billDetails: [
    { key: '', value: '' },
    {
      key: FarmerBillConst.DATE,
      value: 'auction_date',
      helper: getFormattedDate,
    },
    { key: FarmerBillConst.BILL_NO, value: 'bill_no' },
    { key: FarmerBillConst.INCOMING_CRATES, value: 'incomming_crates' },
    { key: FarmerBillConst.FILLED_CRATES, value: 'filled_crates' },
  ],
  deliveryDetailsConfig: [
    { key: 'To,', value: '' },
    { key: 'Chifu Agritech Private Limited,', value: '', singleLine: true },
    {
      key: '',
      value: ['village', 'mandal'],
      seperator: ', ',
      endsWith: ',',
      singleLine: true,
    },
    {
      key: 'Dist.',
      value: ['district', 'zipcode'],
      seperator: '-',
      endsWith: '',
      singleLine: true,
    },
    { key: '', value: '' },
    {
      key: 'GST:',
      value: 'Yet to receive',
      singleLine: true,
      isGst: true,
    },
    {
      key: ' | CIN:',
      value: 'U52202TG2020PTC139550',
      isHardCoded: true,
      singleLine: true,
    },
  ],
  remarksConfig: data => [
    'टिप्पणी:',
    '1. समय पर भुगतान प्राप्त करने के लिए कृपया केवाईसी डेस्क पर अपना केवाईसी और बैंक विवरण पूरा करें। यदि आपने इसे पूरा कर लिया है तो कृपया इस संदेश को अनदेखा करें।',
    `2. आपूर्ति की तारीख से ${data.mandi_details.payment_term_days} दिनों के भीतर राशि आपके पंजीकृत बैंक खाते में जमा कर दी जाएगी`,
  ],
  signatureConfig: [SignatureConst.TITLE],
  announcementConfig: [
    'सूचना:',
    '1. समय पर भुगतान प्राप्त करने के लिए कृपया केवाईसी डेस्क पर अपना केवाईसी और बैंक विवरण पूरा करें। यदि आपने इसे पूरा कर लिया है तो कृपया इस संदेश को अनदेखा करें।',
    '2. वजन या कीमत में किसी भी तरह की विसंगति के मामले में, कृपया उसी दिन हमसे तुरंत संपर्क करें',
    '3. रविवार को मंडी बंद रहती है',
  ],
  lotsConfigCrates: [
    `Product/${FarmerBillConst.PRODUCT}`,
    'HSN Code',
    `Units/${FarmerBillConst.UNITS}`,
    `Weight/${FarmerBillConst.WEIGHT}(kgs/${FarmerBillConst.KILO})`,
    `Rate per kg/${FarmerBillConst.RATE_PER_KG} (${FarmerBillConst.RUPEE_SYMBOL})`,
    `Total Amount/${FarmerBillConst.TOTAL_AMOUNT} (${FarmerBillConst.RUPEE_SYMBOL})`,
  ],
  lotsConfigTruck: [
    `Product/${FarmerBillConst.PRODUCT}`,
    'HSN Code',
    `Chute % (${FarmerBillConst.CHUTE})`,
    `Weight/${FarmerBillConst.WEIGHT}(kgs/${FarmerBillConst.KILO})`,
    'Chargeable Weight / बिल योग्य वजन(kgs/किलो)',
    `Rate per kg/${FarmerBillConst.RATE_PER_KG} (${FarmerBillConst.RUPEE_SYMBOL})`,
    `Total Amount/${FarmerBillConst.TOTAL_AMOUNT} (${FarmerBillConst.RUPEE_SYMBOL})`,
  ],
  expenseConfig: [
    `Expense/${FarmerBillConst.EXPENSE}`,
    `Amount/${FarmerBillConst.EXPENSES_BALANCE} (${FarmerBillConst.RUPEE_SYMBOL})`,
  ],
};

export default FarmerBillConfig;
