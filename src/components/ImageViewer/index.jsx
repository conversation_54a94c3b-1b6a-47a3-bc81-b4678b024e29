import React, { useEffect, useRef, useState } from 'react';

import {
  PictureAsPdf as PictureAsPdfIcon,
  KeyboardArrowLeft,
  KeyboardArrowRight,
} from '@mui/icons-material';
import { Button, Paper, Typography } from '@mui/material';
import { makeStyles } from '@mui/styles';

import ImageThumb from 'Components/ImageThumb';
import CustomModal from 'Components/Modal';

import { ImageList, ImageListWrapper, ImageWrapper } from './styled';

const useStyles = makeStyles(() => ({
  rightButton: {
    top: '11rem',
    position: 'absolute',
    right: '-0.5rem',
  },
  leftButton: {
    top: '11rem',
    position: 'absolute',
    left: '-0.5rem',
  },
  carousel: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
}));

const ImageViewer = ({ images, title, open, toggleModal }) => {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [selectedImage, setSelectedImage] = useState([]);
  const carouselItemsRef = useRef(images);
  const classes = useStyles();

  useEffect(() => {
    if (images) {
      carouselItemsRef.current = carouselItemsRef.current?.slice(
        0,
        images?.length
      );
      setSelectedImageIndex(0);
      setSelectedImage(images[0]);
    }
  }, [images]);

  const handleSelectedImageChange = newIdx => {
    if (images?.length > 0) {
      setSelectedImage(images[newIdx]);
      setSelectedImageIndex(newIdx);
      if (carouselItemsRef?.current[newIdx]) {
        carouselItemsRef?.current[newIdx]?.scrollIntoView({
          inline: 'center',
          behavior: 'smooth',
        });
      }
    }
  };

  const handleRightClick = () => {
    if (images?.length > 0) {
      let newIdx = selectedImageIndex + 1;
      if (newIdx >= images?.length) {
        newIdx = 0;
      }
      handleSelectedImageChange(newIdx);
    }
  };

  const handleLeftClick = () => {
    if (images?.length > 0) {
      let newIdx = selectedImageIndex - 1;
      if (newIdx < 0) {
        newIdx = images?.length - 1;
      }
      handleSelectedImageChange(newIdx);
    }
  };

  const openUrl = () => {
    window.open(selectedImage?.file, '_blank');
  };

  const isPdf = file => file?.endsWith('.pdf');

  return (
    <CustomModal
      isLoading={false}
      title={title}
      open={open}
      onClose={toggleModal}
      carouselModalbody
      fullScreen
    >
      <div className={classes.container}>
        <ImageListWrapper>
          {isPdf(selectedImage?.file) ? (
            <PictureAsPdfIcon
              color='primary'
              className='pdf-icon'
              onClick={openUrl}
            />
          ) : (
            <ImageWrapper>
              <ImageThumb
                file={selectedImage?.file}
                url={selectedImage?.file}
              />
            </ImageWrapper>
          )}
          <Button className={classes.rightButton} onClick={handleRightClick}>
            <KeyboardArrowRight />
          </Button>
          <Button className={classes.leftButton} onClick={handleLeftClick}>
            <KeyboardArrowLeft />
          </Button>
        </ImageListWrapper>
        <Paper square elevation={0}>
          <Typography
            style={{
              margin: '1rem',
              fontSize: '1rem',
              fontWeight: 'bold',
              textAlign: 'center',
            }}
          >
            {selectedImage?.label}
          </Typography>
        </Paper>
        <div className={classes.carousel}>
          <ImageList>
            {images &&
              images?.map((image, id) => {
                return isPdf(image?.file) ? (
                  <div className='carousel__bottom__list'>
                    <PictureAsPdfIcon
                      color='primary'
                      style={{
                        height: '100px',
                        width: '100px',
                      }}
                      onClick={() => handleSelectedImageChange(id)}
                      key={image.id}
                      className={`carousel__image pdf-icon ${
                        selectedImageIndex === id && 'carousel__image-selected'
                      }`}
                      ref={el => (carouselItemsRef.current[id] = el)}
                    />
                    <Typography>{image?.label}</Typography>
                  </div>
                ) : (
                  <div className='carousel__bottom__list'>
                    <img
                      alt=''
                      src={image?.file}
                      url={image?.file}
                      style={{
                        height: '100px',
                        width: '100px',
                      }}
                      onClick={() => handleSelectedImageChange(id)}
                      key={image.id}
                      className={`carousel__image ${
                        selectedImageIndex === id && 'carousel__image-selected'
                      }`}
                      ref={el => (carouselItemsRef.current[id] = el)}
                    />
                    <Typography>{image?.label}</Typography>
                  </div>
                );
              })}
          </ImageList>
        </div>
      </div>
    </CustomModal>
  );
};

export default ImageViewer;
