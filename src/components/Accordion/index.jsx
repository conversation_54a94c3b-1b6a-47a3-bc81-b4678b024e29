import React, { useState } from 'react';

import {
  Delete as DeleteIcon,
  Edit as EditIcon,
  ExpandMore as ExpandMoreIcon,
} from '@mui/icons-material';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Grid,
  Typography,
} from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles(() => ({
  footer: {
    padding: '10px',
    justifyContent: 'space-between',
  },
  title: {
    fontWeight: 'bold',
    fontSize: '1.13rem',
  },
  content: {
    border: '1px solid #F3F3F3',
    width: '98% ',
    borderRadius: '5px',
    padding: '20px',
    margin: '0 auto',
  },
}));

const AccordionDropDown = ({
  data = [],
  title,
  handleDelete = () => {},
  handleEdit = () => {},
  openConfirmationPopup = () => {},
}) => {
  const classes = useStyles();
  const [expanded, setExpanded] = useState(false);

  const handleChange = panel => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };

  return (
    <>
      {data?.map((item, index) => {
        const { line_token_mappings, line_id, name } = item || {};
        const firstToken = `${line_token_mappings?.[0]?.token}`;
        const lastToken = `${
          line_token_mappings?.[line_token_mappings?.length - 1]?.token
        }`;

        return (
          <Accordion
            expanded={expanded === index}
            onChange={handleChange(index)}
            key={index}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              aria-controls='panel1bh-content'
              id='panel1bh-header'
            >
              <Typography className={classes.title}>
                {title ? `${title - `${[data?.length]}`}` : name}
                &nbsp;&nbsp;&nbsp;
              </Typography>
              <Typography>
                {`${firstToken}...${lastToken}  [
                    ${line_token_mappings?.length}
                  ]`}
              </Typography>
            </AccordionSummary>
            <AccordionDetails className={classes.content}>
              {line_token_mappings?.map((item, index) => (
                <Typography key={index} style={{ marginRight: '1.5rem' }}>
                  {item?.token}
                </Typography>
              ))}
            </AccordionDetails>
            <AccordionAction
              handleEdit={handleEdit}
              handleDelete={handleDelete}
              openConfirmationPopup={openConfirmationPopup}
              index={index}
              sequence_line_id={line_id}
              line_name={name}
            />
          </Accordion>
        );
      })}
    </>
  );
};

const AccordionAction = ({
  openConfirmationPopup,
  handleEdit,
  sequence_line_id,
  line_name,
}) => {
  const classes = useStyles();

  return (
    <Grid container className={classes.footer}>
      <Grid item lg={6} md={6}>
        <EditIcon
          color='primary'
          cursor='pointer'
          onClick={() => handleEdit(sequence_line_id)}
        />
      </Grid>
      <Grid
        item
        lg={6}
        md={6}
        style={{ display: 'flex', justifyContent: 'end' }}
      >
        <DeleteIcon
          onClick={() => openConfirmationPopup(sequence_line_id, line_name)}
          color='error'
          cursor='pointer'
        />
      </Grid>
    </Grid>
  );
};

export default AccordionDropDown;
