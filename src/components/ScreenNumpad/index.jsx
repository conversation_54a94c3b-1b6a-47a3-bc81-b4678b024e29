import { Box, Button, CircularProgress, Grid } from '@mui/material';
import { styled } from '@mui/material/styles';

const NumButton = styled(Button)({
  width: '80px',
  height: '80px',
  margin: '8px',
  fontSize: '24px'
});

const ClearButton = styled(Button)({
  width: '80px',
  height: '80px',
  margin: '8px',
  fontSize: '18px',
  backgroundColor: 'rgba(253, 165, 165, 1)',
  color: 'rgba(29, 36, 54, 0.6)',
  fontWeight: 400,
  textTransform: 'none',
  '&:hover': {
    backgroundColor: 'rgba(253, 165, 165, 0.8)'
  }
});

const ScreenNumpad = ({
  onSubmit,
  onClear,
  onChange,
  disabled,
  loading = false,
  showClear = true,
  showSubmit = true
}) => {
  // Build buttons array based on props
  const buttons = ['1', '2', '3', '4', '5', '6', '7', '8', '9'];
  if (showClear) buttons.push('X');
  buttons.push('0');
  if (showSubmit) buttons.push('✔');

  return (
    <Box sx={{ maxWidth: 320, p: 2 }}>
      <Grid container>
        {buttons.map((btn) => (
          <Grid item xs={4} key={btn}>
            {btn === 'X' ? (
              <ClearButton
                variant="contained"
                onClick={onClear}
                disabled={disabled}
              >
                X
              </ClearButton>
            ) : (
              <NumButton
                variant="contained"
                color={btn === '✔' ? 'primary' : 'inherit'}
                onClick={() => {
                  if (btn === '✔') onSubmit();
                  else onChange(btn);
                }}
                disabled={disabled || (btn === '✔' && loading)}
              >
                {btn === '✔' && loading ? (
                  <CircularProgress size={20} color="inherit" />
                ) : (
                  btn
                )}
              </NumButton>
            )}
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default ScreenNumpad;
