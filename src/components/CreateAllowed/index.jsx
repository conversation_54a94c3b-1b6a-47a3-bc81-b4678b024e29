import React from 'react';

import { Add as AddIcon } from '@mui/icons-material';
import { Button } from '@mui/material';

import { getUserData } from 'Utilities/localStorage';

const DEFAULT_PROPS = {
  variant: 'contained',
  size: 'small',
  color: 'primary',
  startIcon: <AddIcon />,
};

const CreateAllowed = ({
  children,
  resource,
  action = 'create',
  label,
  buttonProps,
}) => {
  const { permission_set = [] } = getUserData() || {};

  const canCreate =
    !!permission_set['*']?.includes(action) ||
    !!permission_set[resource]?.includes(action);

  if (children) {
    return children({ canCreate });
  }

  return (
    canCreate && (
      <Button {...DEFAULT_PROPS} {...buttonProps}>
        {label}
      </Button>
    )
  );
};

export default CreateAllowed;
