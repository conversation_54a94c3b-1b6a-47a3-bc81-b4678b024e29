import { useState } from 'react';

import { Info } from '@mui/icons-material';
import { IconButton, Popover, Typography } from '@mui/material';

const PopupButton = ({ content, ...props }) => {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleClick = event => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? 'simple-popover' : undefined;

  return (
    <>
      <IconButton
        aria-describedby={id}
        variant='contained'
        onClick={handleClick}
        {...props}
      >
        <Info fontSize='inherit' />
      </IconButton>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <Typography sx={{ p: 1 }}>{content}</Typography>
      </Popover>
    </>
  );
};

export default PopupButton;
