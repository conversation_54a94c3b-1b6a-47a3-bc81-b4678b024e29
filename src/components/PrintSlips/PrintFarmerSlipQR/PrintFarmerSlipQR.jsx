/* eslint-disable jsx-a11y/label-has-associated-control */
import React, { useEffect, useState } from 'react';

import QRCode from 'react-qr-code';

import { numberMasking } from 'Utilities';
import { getDate, getTimeHours } from 'Utilities/dateUtils';

import { Container } from './style';

const PrintFarmerSlipQR = ({
  data,
  auctionDate,
  brandLogo,
  createdAt,
  mandiName,
}) => {
  const [lotsData, setLotsData] = useState([]);
  useEffect(() => {
    setLotsData(data || {});
  }, []);

  const totalCrates = lots => {
    return lots?.items?.reduce((total, { units = 0 }) => {
      total += units;
      return total;
    }, 0);
  };

  const midArray = [
    '1.',
    '2.',
    '3.',
    '4.',
    '5.',
    '6.',
    '7.',
    'B1(खरदा)',
    'B1(खरदा)',
    'C(बदला)',
  ];

  return (
    <>
      <Container>
        <div className='head'>
          <div className='c1'>
            <div className='style1'>
              <table>
                <tr>
                  <td>Token No.(टोकन संख्या)</td>
                </tr>
                <tr>
                  <div className='tno'>{lotsData?.token}</div>
                </tr>
                <QRCode value={lotsData?.token || ''} className='qr' />
              </table>
            </div>
          </div>
          <div className='c1'>
            <div className='style2'>
              <table>
                <tr>
                  <td>
                    Farmer Price Card
                    <img src={brandLogo} alt='This is logo' className='logo' />
                    <br />
                    <div className='locallan'>(किसान मूल्य कार्ड)</div>
                    <div className='date'>
                      Date:{' '}
                      <div className='dateans'>
                        {auctionDate && getDate(auctionDate)}{' '}
                        {createdAt && getTimeHours(createdAt)}
                      </div>
                    </div>
                    <div className='mandiname'>{mandiName}</div>
                  </td>
                </tr>
                <tr>
                  <div className='details'>
                    <table>
                      <tr>
                        <td>Farmer Name</td>
                        <td>{lotsData?.farmer?.name}</td>
                      </tr>
                      <tr>
                        <td>Phone</td>
                        <td>
                          {lotsData?.farmer?.phone_number &&
                            numberMasking(lotsData?.farmer?.phone_number)}
                        </td>
                      </tr>
                    </table>
                  </div>
                </tr>
                <tr>
                  <div className='cratedetail'>
                    <table>
                      <tr>
                        <td>
                          Crates Brought
                          <br /> (आवक क्रेट)
                          <br />
                          <div className='cratebox'>
                            {totalCrates(lotsData)}
                          </div>
                        </td>
                        <td style={{ paddingLeft: '21px' }}>
                          Crates Filled
                          <br /> (मंडी के क्रेट)
                          <br />
                          <div className='cratebox' />
                        </td>
                        <td style={{ paddingLeft: '21px' }}>
                          Crates Graded
                          <br />
                          (ग्रेड किये क्रेट)
                          <br />
                          <div className='cratebox' />
                        </td>
                      </tr>
                    </table>
                  </div>
                </tr>
              </table>
            </div>
          </div>
        </div>
        <div className='mid'>
          <table>
            <tr>
              <th>
                Grades
                <br />
                (ग्रेड)
              </th>
              <th>
                Crates
                <br />
                (क्रेट)
              </th>
              <th>
                Gross Weight
                <br />
                (कुल वजन)
              </th>
              <th>
                Net Weight
                <br />
                (पक्का वजन)
              </th>
              <th style={{ paddingLeft: '10px', paddingRight: '10px' }}>
                {' '}
                Price
                <br />
                (मूल्य)
              </th>
              <th>
                Loader Name
                <br />
                (लोडर का नाम)
              </th>
            </tr>
            {midArray.map(item => (
              <tr key={item}>
                <td>{item}</td>
                <td />
                <td />
                <td />
                <td />
                <td />
              </tr>
            ))}
          </table>
          <label style={{ paddingLeft: '10px' }}>हस्ताक्षर:</label>
        </div>
        <div className='foot'>
          <footer>
            <table>
              <tr>
                <th>
                  <br />
                </th>
                <th>
                  <br />
                </th>
                <th>
                  <br />
                </th>
              </tr>
              <tr>
                <td>
                  Weight Supervisor
                  <br />
                  (वजन पर्यवेक्षक)
                </td>
                <td>
                  Grader Sign
                  <br />
                  (ग्रेडर)
                </td>
                <td>
                  Supervisor Sign
                  <br />
                  (पर्यवेक्षक)
                </td>
              </tr>
            </table>
          </footer>
        </div>
      </Container>
    </>
  );
};

export default PrintFarmerSlipQR;
