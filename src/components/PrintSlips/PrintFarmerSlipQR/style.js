import styled, { css } from 'styled-components';

export const Container = styled.div`
  border: 1.5px solid black;
  margin-top: 4px;
  height: 770px;
  width: 515px;

  .head {
    display: flex;
  }

  .style1 table {
    width: 35%;
    padding-left: 5px;
  }

  .style1 td {
    font-weight: 500;
    font-size: 14px;
  }

  .tno {
    width: 170px;
    color: red;
    border: 2px solid black;
    text-align: center;
    font-weight: 700;
    font-size: 35px;
    padding-left: 15px;
    padding-top: 7px;
    padding-bottom: 7px;
    padding-right: 15px;
  }

  .qr {
    width: 150px;
    height: 150px;
    padding-top: 3px;
  }

  .style2 table {
    width: 100%;
    text-align: left;
    font-weight: bold;
    font-size: 17px;
    padding-top: 2px;
    padding-left: 20px;
  }

  .logo {
    height: 70px;
    width: 95px;
    float: right;
  }

  .locallan {
    padding-left: 12px;
  }

  .date {
    font-weight: 600;
    font-size: 14px;
    padding-left: 15px;
    display: flex;
  }

  .dateans {
    font-weight: 600;
    font-size: 14px;
    text-decoration-line: underline;
    padding-left: 3px;
    color: red;
  }

  .details {
    padding-top: 4px;
    padding-bottom: 4px;
  }

  .details table {
    width: 100%;
    font-weight: 400;
    font-size: 12px;
    border-collapse: collapse;
    white-space: nowrap;
  }

  .details td:nth-child(1) {
    text-align: left;
    padding-top: 4px;
    padding-bottom: 4px;
    padding-left: 3px;
    width: 30%;
    border: 1.5px solid black;
  }

  .details td:nth-child(2) {
    text-align: left;
    color: red;
    padding-top: 4px;
    padding-bottom: 4px;
    padding-left: 3px;
    width: 100%;
    border: 1.5px solid black;
  }

  .cratedetail table {
    text-align: center;
    width: 100%;
  }

  .cratedetail td {
    text-align: center;
    font-size: 12px;
    font-weight: 400;
  }

  .cratebox {
    width: 60px;
    height: 50px;
    color: red;
    border: 1.5px solid black;
    text-align: center;
    font-weight: 650;
    font-size: 30px;
    display: inline-block;
    vertical-align: middle;
  }

  .mid {
    padding-top: 5px;
  }

  .mid table {
    border-collapse: collapse;
    width: 99%;
    margin-left: auto;
    margin-right: auto;
  }

  .mid th {
    text-align: center;
    padding: 1.35%;
    border: 1.5px solid black;
  }

  .mid td {
    padding: 1.35%;
    border: 1.5px solid black;
    text-align: left;
  }

  .foot {
    padding-top: 5px;
  }

  .foot table {
    width: 97%;
    border-collapse: separate;
    border-spacing: 15px 0;
    margin-left: auto;
    margin-right: auto;
  }

  .foot th {
    text-align: center;
    padding: 8px;
    border-bottom: 1px solid black;
    border-right: 1px solid black;
  }

  .foot th:last-child {
    border-right: none;
  }

  .foot td {
    text-align: center;
    padding: 5px;
    border-right: 1px solid black;
  }

  .foot td:last-child {
    border-right: none;
  }

  .mid table {
    font-size: 12px;
  }
`;
