import React, { useEffect, useState } from 'react';

import { INWARD_TYPE_PREFIX } from 'Utilities/constants';
import { getFormattedDate } from 'Utilities/dateUtils';

import token from './tokenSlip.jpg';

const PrintToken = ({ data, auctionDate, vehicleNumber }) => {
  const [lotsData, setLotsData] = useState([]);
  useEffect(() => {
    setLotsData(data || {});
  }, []);

  const totalCrates = lots => {
    return lots?.items?.reduce((total, { units = 0 }) => {
      total += units;
      return total;
    }, 0);
  };

  const { token_prefix } = lotsData;

  return (
    <>
      <div style={{ position: 'absolute', top: 5, right: 0, zIndex: 1 }}>
        Token slip
      </div>
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: '-50px',
          width: '930px',
          overflow: 'hidden',
          height: '1000px',
        }}
      >
        <img
          className='noprint'
          alt='token'
          src={token}
          style={{
            position: 'absolute',
            top: '10px',
            left: '-8px',
            width: '1000px',
            height: '590px',
          }}
        />
      </div>
      <table
        style={{
          paddingTop: '24px',
          paddingLeft: '70px',
          paddingRight: '0',
          paddingBottom: '0',
          pageBreakAfter: 'always',
          position: 'relative',
          height: '1000px',
          width: '780px',
        }}
        border='0'
      >
        <tr style={{ height: '80px', fontSize: '20px' }}>
          <td
            colSpan={2}
            style={{
              paddingLeft: '35px',
              verticalAlign: 'top',
              paddingTop: '8px',
              fontWeight: 'bold',
            }}
          >
            {auctionDate && getFormattedDate(auctionDate)}
          </td>
          <td />
          <td />
          <td />
        </tr>
        <tr
          style={{
            height: '100px',
            fontSize: '30px',
            fontWeight: 'bold',
          }}
        >
          <td
            style={{
              width: '138px',
              paddingLeft: 0,
              fontSize: '40px',
              verticalAlign: 'top',
            }}
          >
            {lotsData?.token}
          </td>
          <td style={{ paddingLeft: '3px', width: '152px' }} />
          <td
            style={{ paddingLeft: '3px', width: '120px', textAlign: 'center' }}
          >
            {lotsData?.token_prefix}
          </td>
          <td
            style={{ paddingLeft: '9px', width: '120px', textAlign: 'center' }}
          >
            {INWARD_TYPE_PREFIX.TRUCK !== token_prefix && totalCrates(lotsData)}
          </td>
          <td />
        </tr>
        <tr style={{ height: '89px', fontSize: '25px', fontWeight: 'bold' }}>
          <td />
          <td colSpan={3} style={{ paddingTop: '2px' }}>
            {lotsData?.farmer?.name}
          </td>
          <td />
        </tr>
        <tr style={{ height: '79px', fontSize: '25px', fontWeight: 'bold' }}>
          <td />
          <td colSpan={3} style={{ verticalAlign: 'top', paddingTop: '2px' }} />
          <td />
        </tr>
        <tr style={{ height: '79px', fontSize: '25px', fontWeight: 'bold' }}>
          <td />
          <td colSpan={3} style={{ paddingTop: '2px' }}>
            {lotsData?.farmer?.phone_number}
          </td>
          <td style={{ width: '182px' }}>
            <div
              style={{
                height: '79px',
                overflow: 'hidden',
                fontSize: '12px',
                paddingTop: '18px',
              }}
            >
              {lotsData?.farmer?.location?.full_address}
            </div>
          </td>
        </tr>
        <tr style={{ fontSize: '25px', fontWeight: 'bold' }}>
          <td />
          <td colSpan={3} style={{ paddingTop: '18px', verticalAlign: 'top' }}>
            {vehicleNumber}
          </td>
          <td />
        </tr>
      </table>
    </>
  );
};

export default PrintToken;
