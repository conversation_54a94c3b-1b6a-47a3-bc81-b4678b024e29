import React, { forwardRef } from 'react';

import { Button, Dialog, DialogActions, DialogContent } from '@mui/material';

import { useSiteValue } from 'App/SiteContext';
import { getBrandLogo } from 'Utilities';
import { INWARD_TYPE_PREFIX } from 'Utilities/constants';

import PrintFarmerSlipQR from './PrintFarmerSlipQR/PrintFarmerSlipQR';
import PrintGraderSlip from './PrintGraderSlip/PrintGraderSlip';
import PrintTokenSlip from './PrintTokenSlip/PrintTokenSlip';
import { Container } from './style';

const GraderSlip = forwardRef(
  (
    {
      handlePrint,
      data,
      auctionDate,
      vehicleNumber,
      driverPhoneNumber,
      closePrintPreview,
      printPreview = null,
      createdAt,
    },
    ref
  ) => {
    const { token_prefix } = data;
    const { mandiId, mandiList, dcId } = useSiteValue();
    const brandLogo = getBrandLogo(mandiList, mandiId);

    const mandiName = mandiList.find(mandi => mandi.id === mandiId)?.name;
    const WARUD_MANDI_ID = 184;
    return (
      <Dialog
        maxWidth='md'
        fullWidth
        open={printPreview}
        onClose={closePrintPreview}
      >
        <DialogContent ref={ref}>
          <div>
            <PrintTokenSlip
              data={data}
              auctionDate={auctionDate}
              vehicleNumber={vehicleNumber}
              driverPhoneNumber={driverPhoneNumber}
              brandLogo={brandLogo}
              createdAt={createdAt}
              mandiName={mandiName}
            />
          </div>
          {INWARD_TYPE_PREFIX.TRUCK !== token_prefix && (
            <>
              {+dcId !== WARUD_MANDI_ID && (
                <>
                  <div>
                    <PrintGraderSlip
                      data={data}
                      auctionDate={auctionDate}
                      vehicleNumber={vehicleNumber}
                      brandLogo={brandLogo}
                      createdAt={createdAt}
                      mandiName={mandiName}
                    />
                  </div>
                  <div>
                    <PrintGraderSlip
                      data={data}
                      auctionDate={auctionDate}
                      vehicleNumber={vehicleNumber}
                      brandLogo={brandLogo}
                      createdAt={createdAt}
                      mandiName={mandiName}
                    />
                  </div>
                </>
              )}
              <Container>
                <div className='rotate'>
                  <PrintFarmerSlipQR
                    data={data}
                    auctionDate={auctionDate}
                    vehicleNumber={vehicleNumber}
                    brandLogo={brandLogo}
                    createdAt={createdAt}
                    mandiName={mandiName}
                  />
                </div>
              </Container>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            color='primary'
            size='small'
            onClick={handlePrint}
            variant='contained'
            data-cy='mandi.printSlips.printButton'
          >
            Print
          </Button>
        </DialogActions>
      </Dialog>
    );
  }
);

GraderSlip.displayName = 'PrintSlips';

export default GraderSlip;
