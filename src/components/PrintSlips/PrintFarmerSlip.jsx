import React, { useEffect, useState } from 'react';

import { getFormattedDate } from 'Utilities/dateUtils';

import token from './farmerSlip.jpg';

const PrintFarmerSlip = ({ data, auctionDate }) => {
  const [lotsData, setLotsData] = useState([]);
  useEffect(() => {
    setLotsData(data || {});
  }, []);

  const totalCrates = lots => {
    return lots?.items?.reduce((total, { units = 0 }) => {
      total += units;
      return total;
    }, 0);
  };

  return (
    <>
      <div style={{ position: 'absolute', top: 5 }}>Farmer price card</div>
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: '-50px',
          width: '930px',
          overflow: 'hidden',
          height: '1000px',
        }}
      >
        <img
          className='noprint'
          alt='token'
          src={token}
          style={{
            position: 'absolute',
            top: '22px',
            left: '-18px',
            width: '1000px',
            height: '585px',
          }}
        />
      </div>
      <table
        style={{
          paddingTop: '0',
          paddingLeft: '0',
          paddingRight: '0',
          paddingBottom: '0',
          pageBreakAfter: 'always',
          position: 'relative',
          transform: 'rotate(-90deg)',
          height: '749px',
          top: '-79px',
          left: '118px',
          width: '534px',
        }}
        border='0'
      >
        <tr>
          <td
            style={{
              height: '58px',
              width: '249px',
              paddingLeft: '67px',
              fontSize: '18px',
              fontWeight: 'bold',
              verticalAlign: 'top',
              paddingTop: '8px',
            }}
          >
            {auctionDate && getFormattedDate(auctionDate)}
          </td>
          <td />
        </tr>
        <tr>
          <td
            style={{
              height: '76px',
              paddingLeft: '38px',
              fontWeight: 'bold',
              fontSize: '28px',
              paddingTop: '18px',
            }}
          >
            {lotsData?.token}
          </td>
          <td
            style={{
              paddingLeft: '22px',
              paddingTop: '18px',
              fontSize: '17px',
              fontWeight: 600,
            }}
          >
            {lotsData?.farmer?.name}
          </td>
        </tr>
        <tr>
          <td
            style={{ height: '43px', paddingLeft: '93px', fontWeight: 'bold' }}
          >
            {lotsData?.farmer?.phone_number}
          </td>
          <td />
        </tr>
        <tr>
          <td
            style={{
              height: '65px',
              verticalAlign: 'top',
              paddingLeft: '81px',
            }}
          >
            {lotsData?.farmer?.location?.full_address}
          </td>
          <td
            style={{
              fontSize: '28px',
              paddingLeft: '10px',
              fontWeight: 'bold',
            }}
          >
            {totalCrates(lotsData)}
          </td>
        </tr>
        <tr>
          <td />
          <td />
        </tr>
      </table>
    </>
  );
};

export default PrintFarmerSlip;
