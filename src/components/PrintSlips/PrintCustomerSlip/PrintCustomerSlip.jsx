import React, { forwardRef } from 'react';

import { getDate } from '../../../utilities/dateUtils';

import {
  Container,
  HeaderSection,
  DateField,
  DateBox,
  DateText,
  TitleSection,
  MainTitle,
  LogoSection,
  BrandLogo,
  BrandText,
  FruitXText,
  XText,
  CompanyText,
  InfoGrid,
  InfoBox,
  InfoLabel,
  InfoValue,
  SignaturesSection,
  SignatureBox,
  SignatureLabel,
  SignatureLine,
  TimestampSection,
} from './style';

const PrintCustomerSlip = forwardRef(({
  tokenNumber,
  farmerName,
  mandiNo,
  date,
  brandLogo,
  lotData,
  pageData, // New prop for multiple pages
}, ref) => {
  // Detailed logging for PDF component data verification
  // Function to format current timestamp
  const getCurrentTimestamp = () => {
    const now = new Date();
    const options = {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true,
      timeZone: 'Asia/Calcutta',
    };
    return now.toLocaleString('en-IN', options);
  };

  const renderDataRows = (lots) => {
    if (!lots) return null;

    const rows = [];

    lots.forEach((lot, index) => {
      // Quantity row
      rows.push(
        <tr key={`quantity-row-${index}`}>
          <td className='variety' rowSpan={2}>
            {lot.variety || ''}
          </td>
          <td className='lot' rowSpan={2}>
            {lot.lot || ''}
          </td>
          <td>Qty</td>
          <td className='grade-data'>{lot.grades?.EL_80?.quantity || '-'}</td>
          <td className='grade-data'>{lot.grades?.L_100?.quantity || '-'}</td>
          <td className='grade-data'>{lot.grades?.M_125?.quantity || '-'}</td>
          <td className='grade-data'>{lot.grades?.S_150?.quantity || '-'}</td>
          <td className='grade-data'>{lot.grades?.ES_175?.quantity || '-'}</td>
          <td className='grade-data'>{lot.grades?.EES_200?.quantity || '-'}</td>
          <td className='grade-data'>{lot.grades?.Pitto?.quantity || '-'}</td>
          <td className='grade-data'>{lot.grades?.Mix_6L?.quantity || '-'}</td>
          <td className='grade-data'>{lot.grades?.Mix_7L?.quantity || '-'}</td>
          <td className='grade-data'>{lot.grades?.Mix?.quantity || '-'}</td>
        </tr>
      );

      // Price row
      rows.push(
        <tr key={`price-row-${index}`}>
          <td>Price-</td>
          <td className='grade-data'>{lot.grades?.EL_80?.price || '-'}</td>
          <td className='grade-data'>{lot.grades?.L_100?.price || '-'}</td>
          <td className='grade-data'>{lot.grades?.M_125?.price || '-'}</td>
          <td className='grade-data'>{lot.grades?.S_150?.price || '-'}</td>
          <td className='grade-data'>{lot.grades?.ES_175?.price || '-'}</td>
          <td className='grade-data'>{lot.grades?.EES_200?.price || '-'}</td>
          <td className='grade-data'>{lot.grades?.Pitto?.price || '-'}</td>
          <td className='grade-data'>{lot.grades?.Mix_6L?.price || '-'}</td>
          <td className='grade-data'>{lot.grades?.Mix_7L?.price || '-'}</td>
          <td className='grade-data'>{lot.grades?.Mix?.price || '-'}</td>
        </tr>
      );
    });
    return rows;
  };

  const renderSinglePage = (pageInfo, pageIndex, isLastPage) => (
    <Container key={`page-${pageIndex}`} style={{ pageBreakAfter: isLastPage ? 'auto' : 'always' }}>
      {/* Header Section */}
      <HeaderSection>
        {/* Date Field */}
        <DateField>
          <DateBox>
            <DateText>Date: {date ? getDate(date) : ''}</DateText>
          </DateBox>
        </DateField>

        {/* Title */}
        <TitleSection>
          <MainTitle>Customer Slip</MainTitle>
        </TitleSection>

        {/* Logo */}
        <LogoSection>
          {brandLogo ? (
            <BrandLogo src={brandLogo} alt='Brand Logo' />
          ) : (
            <>
              <BrandText>
                <FruitXText>FRUIT</FruitXText>
                <XText>X</XText>
              </BrandText>
              <CompanyText>Chifu Agritech Pvt. Ltd.</CompanyText>
            </>
          )}
        </LogoSection>
      </HeaderSection>

      {/* Info Section */}
      <InfoGrid>
        <InfoBox>
          <InfoLabel>Token No. (टोकन संख्या)</InfoLabel>
          <InfoValue>{tokenNumber || ''}</InfoValue>
        </InfoBox>

        <InfoBox>
          <InfoLabel>Farmer Name (किसान का नाम)</InfoLabel>
          <InfoValue>{farmerName || ''}</InfoValue>
        </InfoBox>

        <InfoBox>
          <InfoLabel>Mandi No.</InfoLabel>
          <InfoValue>{pageInfo.mandiNo || ''}</InfoValue>
        </InfoBox>
      </InfoGrid>

      {/* Main Table */}
      <div className='main-table'>
        <div className='watermark'>FRUITX</div>
        <table className='customer-table'>
          <thead>
            <tr>
              <th rowSpan='3'>Variety</th>
              <th rowSpan='3'>Lot</th>
              <th></th>
              <th>
                EL
                <br />
                (80)
              </th>
              <th>
                L
                <br />
                (100)
              </th>
              <th>
                M
                <br />
                (125)
              </th>
              <th>
                S
                <br />
                (150)
              </th>
              <th>
                ES
                <br />
                (175)
              </th>
              <th>
                EES
                <br />
                (200)
              </th>
              <th>Pitto</th>
              <th>6L</th>
              <th>7L</th>
              <th>Mix</th>
            </tr>
          </thead>
          <tbody>{renderDataRows(pageInfo.lotData)}</tbody>
          <tfoot>
          </tfoot>
        </table>
      </div>

      {/* Footer Section */}
      <SignaturesSection>
        <SignatureBox>
          <SignatureLabel>
            Loader Name and Sign
            <br />
            (हस्ताक्षर)
          </SignatureLabel>
          <SignatureLine></SignatureLine>
        </SignatureBox>

        <SignatureBox>
          <SignatureLabel>
            Supervisor Name and Sign
            <br />
            (हस्ताक्षर)
          </SignatureLabel>
          <SignatureLine></SignatureLine>
        </SignatureBox>
      </SignaturesSection>

      {/* Timestamp Section */}
      <TimestampSection>Generated on {getCurrentTimestamp()}</TimestampSection>
    </Container>
  );

  // If pageData is provided, render multiple pages; otherwise, render single page (backward compatibility)
  if (pageData && pageData.length > 0) {
    return (
      <div ref={ref}>
        {pageData.map((pageInfo, index) => 
          renderSinglePage(pageInfo, index, index === pageData.length - 1)
        )}
      </div>
    );
  }

  // Single page rendering (new implementation and backward compatibility)
  return (
    <div ref={ref}>
      {renderSinglePage({ mandiNo, lotData }, 0, true)}
    </div>
  );
});

PrintCustomerSlip.displayName = 'PrintCustomerSlip';

export default PrintCustomerSlip;
