import styled from 'styled-components';

export const Container = styled.div`
  border: 1.5px solid black;
  margin-top: 4px;
  margin-bottom: 20px;
  font-weight: 500;
  font-size: 13px;
  page-break-after: always;

  .date {
    font-weight: 600;
    font-size: 15px;
    padding-left: 5px;
    padding-top: 14px;
    display: flex;
  }

  .dateans {
    font-weight: 800;
    font-size: 19px;
    text-decoration-line: underline;
    padding-left: 3px;
    padding-top: 10px;
    color: red;
  }

  .title {
    font-weight: bold;
    font-size: 23px;
    padding-left: 10px;
    padding-top: 10px;
  }

  .boxes table {
    text-align: center;
    font-size: 15px;
  }

  .logo {
    height: 170px;
    width: 200px;
    padding-bottom: 15px;
    float: right;
  }

  .bigbox {
    width: 93%;
    height: 95px;
    border: 1.5px solid black;
    text-align: center;
    font-weight: 650;
    font-size: 40px;
    padding-left: 15px;
    padding-top: 20px;
    padding-bottom: 5px;
    padding-right: 10px;
    display: inline-block;
    vertical-align: middle;
    color: red;
  }

  .smallbox {
    width: 93%;
    height: 60px;
    border: 1.5px solid black;
    text-align: center;
    font-weight: 650;
    font-size: 30px;
    padding-left: 15px;
    padding-top: 7px;
    padding-bottom: 7px;
    padding-right: 10px;
    display: inline-block;
    vertical-align: middle;
    color: red;
  }

  .mid {
    padding-top: 10px;
  }

  .mid table {
    text-align: center;
    border-collapse: collapse;
    width: 98%;
    margin-left: auto;
    margin-right: auto;
  }

  .mid td {
    padding: 1%;
    border: 1.5px solid black;
  }

  .enlarge {
    width: 565px;
    font-weight: 700;
    font-size: 20px;
    color: red;
  }

  .foot {
    padding-top: 10px;
    padding-bottom: 20px;
    display: flex;
  }

  .foot table {
    text-align: center;
    border-collapse: separate;
    border-spacing: 0 25px;
    width: 48%;
    margin-left: auto;
    margin-right: auto;
  }

  .foot td:nth-child(1) {
    padding: 2%;
    border: 1.5px solid black;
    width: 46%;
  }

  .foot td:nth-child(2) {
    width: 54%;
    font-size: 15px;
    font-weight: 700;
    padding: 2%;
    border: 1.5px solid black;
    color: red;
  }

  .address {
    text-align: left;
    font-size: 14px;
    font-weight: 700;
    color: red;
  }

  .separate {
    width: 900px;
    border: 1.5px solid black;
  }

  .mandiname {
    font-weight: 600;
    font-size: 15px;
    padding-left: 5px;
    display: flex;
  }
`;
