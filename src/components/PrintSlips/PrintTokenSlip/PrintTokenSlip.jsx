import React, { useEffect, useState } from 'react';

import { numberMasking } from 'Utilities';
import { INWARD_TYPE_PREFIX } from 'Utilities/constants';
import { getDate, getTimeHours } from 'Utilities/dateUtils';

import { Container } from './style';

const PrintTokenSlip = ({
  data,
  auctionDate,
  vehicleNumber,
  driverPhoneNumber,
  brandLogo,
  createdAt,
  mandiName,
}) => {
  const [lotsData, setLotsData] = useState([]);
  useEffect(() => {
    setLotsData(data || {});
  }, []);

  const totalCrates = lots =>
    lots?.items?.reduce((total, { units = 0 }) => {
      total += units;
      return total;
    }, 0);

  const { token_prefix } = lotsData;

  return (
    <Container>
      <div className='head'>
        <img src={brandLogo} alt='This is logo' className='logo' />
        <table>
          <tr>
            <td style={{ display: 'flex' }}>
              <div className='date'>Date: </div>
              <div className='dateans'>
                {auctionDate && getDate(auctionDate)}{' '}
                {createdAt && getTimeHours(createdAt)}
              </div>
            </td>
            <td>
              <div className='title'>Token Slip (टोकन पर्ची)</div>
              <div className='mandiname'>{mandiName}</div>
            </td>
          </tr>
        </table>

        <div className='boxes'>
          <table>
            <tr>
              <td>
                Token No. (टोकन संख्या)
                <br />
                <div className='bigbox'>{lotsData?.token}</div>
              </td>
              <td style={{ paddingLeft: '30px' }}>
                <br />
                Slot (Y/N)
                <br />
                (स्लॉट (Y/N))
                <br />
                <div className='smallbox' />
              </td>
              <td style={{ paddingLeft: '30px' }}>
                <br />
                Crates/Truck
                <br />
                (क्रेट / ट्रक)
                <br />
                <div className='smallbox'>{lotsData?.token_prefix}</div>
              </td>
              <td style={{ paddingLeft: '30px' }}>
                <br />
                Crates Brought
                <br />
                (आवक क्रेट)
                <br />
                <div className='smallbox'>
                  {' '}
                  {INWARD_TYPE_PREFIX.TRUCK !== token_prefix &&
                    totalCrates(lotsData)}
                </div>
              </td>
            </tr>
          </table>
        </div>
      </div>
      <div className='mid'>
        <table>
          <tr>
            <td>
              Farmer Name
              <br />
              (किसान का नाम)
            </td>
            <td className='enlarge'>
              {lotsData?.farmer?.name} -{' '}
              {numberMasking(lotsData?.farmer?.phone_number)}
            </td>
          </tr>
          <tr>
            <td>
              Transporter/Driver Name
              <br />
              (ट्रांसपोर्टर/ड्राइवर नाम)
            </td>
            <td className='enlarge' />
          </tr>
        </table>
      </div>
      <div className='foot'>
        <table>
          <tr>
            <td>
              Driver Mobile Number
              <br />
              (चालक मोबाइल नंबर)
            </td>
            <td>{driverPhoneNumber}</td>
          </tr>
          <tr>
            <td>
              Vehicle Number
              <br />
              (गाडी नंबर)
            </td>
            <td> {vehicleNumber}</td>
          </tr>
        </table>
        <table>
          <tr>
            <td>
              Address
              <br />
              (पता)
            </td>
            <td colSpan={2} />
          </tr>
          <tr>
            <td>
              Parking Number
              <br />
              (पार्किंग नंबर)
            </td>
            <th className='separate' />
            <th className='separate' />
          </tr>
        </table>
      </div>
    </Container>
  );
};

export default PrintTokenSlip;
