import styled from 'styled-components';

export const Container = styled.div`
  border: 1.5px solid black;
  margin-top: 10px;
  font-weight: 500;
  font-size: 13px;
  page-break-after: always;

  .date {
    font-weight: 600;
    font-size: 15px;
    padding-left: 5px;
    padding-top: 14px;
    display: flex;
  }

  .dateans {
    font-weight: 800;
    font-size: 19px;
    text-decoration-line: underline;
    padding-left: 3px;
    padding-top: 10px;
    color: red;
  }

  .title {
    font-weight: bold;
    font-size: 23px;
    padding-left: 10px;
    padding-top: 10px;
  }

  .boxes table {
    text-align: center;
    font-size: 15px;
    padding-top: -5px;
  }

  .logo {
    height: 170px;
    width: 200px;
    padding-bottom: 15px;
    float: right;
  }

  .bigbox {
    width: 93%;
    height: 95px;
    border: 1.5px solid black;
    text-align: center;
    font-weight: 650;
    font-size: 40px;
    padding-left: 15px;
    padding-top: 20px;
    padding-bottom: 5px;
    padding-right: 10px;
    display: inline-block;
    vertical-align: middle;
    color: red;
  }

  .smallbox {
    width: 93%;
    height: 60px;
    border: 1.5px solid black;
    text-align: center;
    font-weight: 650;
    font-size: 30px;
    padding-left: 15px;
    padding-top: 7px;
    padding-bottom: 7px;
    padding-right: 10px;
    display: inline-block;
    vertical-align: middle;
    color: red;
  }

  .mid {
    padding-top: 15px;
  }

  .mid table {
    text-align: center;
    border-collapse: collapse;
    width: 98%;
    margin-left: auto;
    margin-right: auto;
  }

  .mid td {
    padding: 2%;
    border: 1.5px solid black;
  }

  .enlarge {
    width: 565px;
    font-weight: 700;
    font-size: 20px;
    color: red;
  }

  .foot {
    padding-top: 10px;
    padding-bottom: 20px;
    display: flex;
  }

  .col1 table {
    text-align: center;
    width: 100%;
  }

  .col1 td {
    padding-left: 30px;
    width: 130px;
  }

  .col1 td:nth-child(1) {
    padding-left: 0px;
    width: 100px;
  }

  .col2 {
    padding-left: 10px;
    display: right;
  }

  .col2 table {
    width: 225px;
    border-collapse: collapse;
    text-align: center;
  }

  .col2 tr:nth-child(1) {
    border: 1.5px solid black;
    padding: 2%;
  }

  .col2 tr:nth-child(2) {
    height: 160px;
    border: 1.5px solid black;
  }

  .mandiname {
    font-weight: 600;
    font-size: 15px;
    padding-left: 5px;
    display: flex;
  }
`;
