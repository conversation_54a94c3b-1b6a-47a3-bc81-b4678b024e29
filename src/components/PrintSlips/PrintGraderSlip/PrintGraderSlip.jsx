import React, { useEffect, useState } from 'react';

import { getDate, getTimeHours } from 'Utilities/dateUtils';

import { Container } from './style';

const PrintGraderSlip = ({
  data,
  auctionDate,
  brandLogo,
  createdAt,
  mandiName,
}) => {
  const [lotsData, setLotsData] = useState([]);
  useEffect(() => {
    setLotsData(data || {});
  }, []);

  const totalCrates = lots => {
    return lots?.items?.reduce((total, { units = 0 }) => {
      total += units;
      return total;
    }, 0);
  };

  return (
    <>
      <Container>
        <div className='head'>
          <img src={brandLogo} alt='This is logo' className='logo' />
          <table>
            <tr>
              <td style={{ display: 'flex' }}>
                <div className='date'>Date: </div>
                <div className='dateans'>
                  {auctionDate && getDate(auctionDate)}{' '}
                  {createdAt && getTimeHours(createdAt)}
                </div>
              </td>
              <td>
                <div className='title'>Grader Slip (ग्रेडर पर्ची)</div>
                <div className='mandiname'>{mandiName}</div>
              </td>
            </tr>
          </table>
          <div className='boxes'>
            <table>
              <tr>
                <td>
                  Token No. (टोकन संख्या)
                  <br />
                  <div className='bigbox'>{lotsData?.token}</div>
                </td>
                <td style={{ paddingLeft: '30px' }}>
                  <br />
                  Crates Brought
                  <br />
                  (आवक क्रेट)
                  <br />
                  <div className='smallbox'> {totalCrates(lotsData)}</div>
                </td>
                <td style={{ paddingLeft: '30px' }}>
                  <br />
                  Crates Filled
                  <br />
                  (मंडी के क्रेट)
                  <br />
                  <div className='smallbox' />
                </td>
                <td style={{ paddingLeft: '30px' }}>
                  <br />
                  Crates Graded
                  <br />
                  (ग्रेड किये क्रेट)
                  <br />
                  <div className='smallbox' />
                </td>
              </tr>
            </table>
          </div>
        </div>
        <div className='mid'>
          <table>
            <tr>
              <td>
                Farmer Name
                <br />
                (किसान का नाम)
              </td>
              <td className='enlarge'>{lotsData?.farmer?.name}</td>
            </tr>
          </table>
        </div>
        <div className='foot'>
          <div className='col1'>
            <table>
              <tr>
                <td>
                  Grade 1<br />
                  (ग्रेड 1)
                  <br />
                  <div className='smallbox' />
                </td>
                <td>
                  Grade 2<br />
                  (ग्रेड 2)
                  <br />
                  <div className='smallbox' />
                </td>
                <td>
                  Grade 3<br />
                  (ग्रेड 3)
                  <br />
                  <div className='smallbox' />
                </td>
                <td>
                  Grade 4<br />
                  (ग्रेड 4)
                  <br />
                  <div className='smallbox' />
                </td>
              </tr>
              <tr>
                <td>
                  Grade 5<br />
                  (ग्रेड 5)
                  <br />
                  <div className='smallbox' />
                </td>
                <td>
                  Grade B1
                  <br />
                  (खरदा)
                  <br />
                  <div className='smallbox' />
                </td>
                <td>
                  Grade B2
                  <br />
                  (खरदा)
                  <br />
                  <div className='smallbox' />
                </td>
                <td>
                  Grade C1
                  <br />
                  (बदला)
                  <br />
                  <div className='smallbox' />
                </td>
              </tr>
            </table>
          </div>
          <div className='col2'>
            <table>
              <tr>
                <td>
                  Grader Name & Sign <br /> (ग्रेडर का नाम और हस्ताक्षर)
                </td>
              </tr>
              <tr>
                <td />
              </tr>
            </table>
          </div>
        </div>
      </Container>
    </>
  );
};

export default PrintGraderSlip;
