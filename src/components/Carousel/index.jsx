import React, { useState } from 'react';

import {
  Image as ImageIcon,
  KeyboardArrowLeft,
  KeyboardArrowRight,
} from '@mui/icons-material';
import { Button, IconButton, Paper, Typography } from '@mui/material';
import { makeStyles, useTheme } from '@mui/styles';
import SwipeableViews from 'react-swipeable-views';
import { autoPlay } from 'react-swipeable-views-utils';

import ImageThumb from 'Components/ImageThumb';
import ImageViewer from 'Components/ImageViewer';

import { ImageListWrapper } from './style';

const AutoPlaySwipeableViews = autoPlay(SwipeableViews);
const useStyles = makeStyles(() => ({
  root: {
    maxWidth: 150,
    borderRadius: '5px',
    flexGrow: 1,
    position: 'relative',
  },
  header: {
    backgroundColor: 'transparent',
    textAlign: 'center',
    width: '100%',
    marginTop: '0.3rem',
  },
  img: {
    height: '100%',
    display: 'block',
    maxWidth: '100%',
    overflow: 'hidden',
    width: '100%',
    marginBottom: 0,
    marginRight: 'auto',
    marginLeft: 'auto',
  },
  rightButton: {
    top: '2rem',
    position: 'absolute',
    right: '-2.5rem',
  },
  leftButton: {
    top: '2rem',
    position: 'absolute',
    left: '-2.5rem',
  },
}));

const Carousel = ({
  imageData,
  showPreviewIcon = false,
  showImagePreview = false,
  showPreviousNext = true,
  openModal = false,
  onClose = () => {},
}) => {
  const classes = useStyles();

  const theme = useTheme();
  const [activeStep, setActiveStep] = useState(0);
  const maxSteps = imageData?.length;
  const [open, setOpen] = useState(openModal);

  const handleNext = () => {
    setActiveStep(prevActiveStep => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep(prevActiveStep => prevActiveStep - 1);
  };

  const handleStepChange = item => {
    setActiveStep(item);
  };

  const toggleModal = () => {
    setOpen(!open);
    if (open) onClose();
  };

  return (
    imageData?.length > 0 && (
      <>
        {showPreviewIcon && (
          <IconButton
            edge='start'
            color='inherit'
            aria-label='open drawer'
            onClick={toggleModal}
          >
            <ImageIcon />
          </IconButton>
        )}
        {showImagePreview && (
          <div className={classes.root}>
            <AutoPlaySwipeableViews
              axis={theme.direction === 'rtl' ? 'x-reverse' : 'x'}
              index={activeStep}
              onChangeIndex={handleStepChange}
              enableMouseEvents
            >
              {imageData?.map((item, index) => (
                <div key={item.label}>
                  {Math.abs(activeStep - index) <= 2 ? (
                    <ImageListWrapper>
                      <ImageThumb
                        file={item?.file}
                        url={item?.file}
                        style={{
                          height: '100px',
                          width: '100px',
                          overflow: 'hidden',
                        }}
                        onClick={toggleModal}
                      />
                    </ImageListWrapper>
                  ) : null}
                </div>
              ))}
            </AutoPlaySwipeableViews>
            <Paper square elevation={0} className={classes.header}>
              <Typography>{imageData[activeStep]?.label}</Typography>
            </Paper>
            {showPreviousNext && (
              <>
                <Button
                  size='small'
                  className={classes.rightButton}
                  onClick={handleNext}
                  disabled={activeStep === maxSteps - 1}
                >
                  {theme.direction === 'rtl' ? (
                    <KeyboardArrowLeft />
                  ) : (
                    <KeyboardArrowRight />
                  )}
                </Button>
                <Button
                  size='small'
                  className={classes.leftButton}
                  onClick={handleBack}
                  disabled={activeStep === 0}
                >
                  {theme.direction === 'rtl' ? (
                    <KeyboardArrowRight />
                  ) : (
                    <KeyboardArrowLeft />
                  )}
                </Button>
              </>
            )}
          </div>
        )}
        {open && (
          <ImageViewer
            open={open}
            images={imageData}
            toggleModal={toggleModal}
          />
        )}
      </>
    )
  );
};

export default Carousel;
