const ProgressBar = ({ value }) => {
  const getProgressColor = () => {
    if (value < 30) return '#D72638';
    if (value < 70) return '#F59E0B';
    return '#4CAF50';
  };

  return (
    <div style={{ width: '100%', padding: '10px', display: 'flex' }}>
      <progress
        value={value}
        max='100'
        style={{
          width: '100%',
          height: '5px',
          appearance: 'none',
          WebkitAppearance: 'none',
          backgroundColor: '#E5E7EB',
          '--progress-color': getProgressColor(),
        }}
      />
      <style>{`
        progress::-webkit-progress-bar {
          background-color: #E5E7EB;
          border-radius: 10px;
        }
        progress::-webkit-progress-value {
          background-color: var(--progress-color); /* Use CSS variable */
          border-radius: 10px;
          transition: background-color 0.3s ease;
        }
      `}</style>
    </div>
  );
};

export default ProgressBar;
