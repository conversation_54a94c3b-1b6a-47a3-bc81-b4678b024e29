import React from 'react';

import { Grid, Typography } from '@mui/material';
import PropTypes from 'prop-types';

const Header = ({ text, hiddenProp, style }) => {
  // Convert hiddenProp to sx display property
  let displaySx = {};
  if (hiddenProp) {
    if (hiddenProp.mdDown) {
      displaySx = { display: { xs: 'none', md: 'none' } };
    } else if (hiddenProp.mdUp) {
      displaySx = { display: { xs: 'block', md: 'none' } };
    }
  }

  return (
    <Grid
      item
      style={{ fontWeight: 'bold', marginBottom: '1rem', ...style }}
      sx={displaySx}
    >
      <Typography style={{ fontSize: '14px', fontWeight: 600 }}>
        {text}
      </Typography>
    </Grid>
  );
};

Header.propTypes = {
  text: PropTypes.string.isRequired,
  hiddenProp: PropTypes.shape({
    mdDown: PropTypes.bool,
    mdUp: PropTypes.bool,
  }),
  style: PropTypes.object,
};

const GridListRow = ({ children, key = 0, style = {} }) => (
  <Grid
    container
    direction='row'
    spacing={1}
    key={key}
    justify='space-between'
    style={{
      background: 'white',
      borderBottom: '1px solid #DDDDDD',
      padding: '5px',
      borderRadius: '2px',
      marginBottom: '5px',
      boxShadow: '1px 1px 1px 1px #EEEEEE',
      ...style,
    }}
  >
    {children}
  </Grid>
);

const GridListView = ({
  data,
  columns,
  cellProps,
  headerContainerStyle = {},
  rowContainerStyle = {},
  show: { comment = true, reject = true } = {},
}) => {
  return (
    <>
      <GridListRow
        style={{
          position: 'sticky',
          top: -10,
          zIndex: 1,
          ...headerContainerStyle,
        }}
        sx={{ display: { xs: 'none', md: 'flex' } }}
      >
        {columns.map((column, idx) => (
          <Grid
            key={idx}
            style={{ display: 'flex', alignItems: 'flex-end' }}
            item
            {...column.props}
          >
            <Header
              hiddenProp={{ mdDown: true }}
              style={column.header.style}
              text={column.header.label}
            />
          </Grid>
        ))}
      </GridListRow>
      {data?.map((value, index) => (
        <GridListRow key={index} style={rowContainerStyle}>
          {columns.map((column, idx) => (
            <Grid key={idx} item {...column.props}>
              <Header
                hiddenProp={{ mdUp: true }}
                style={column.header.style}
                text={column.header.label}
              />
              <div style={column.style}>
                {column.render ? (
                  column.render(value, cellProps, {
                    columnIndex: idx,
                    rowIndex: index,
                  })
                ) : (
                  <Typography variant='body1'>{value[column.key]}</Typography>
                )}
              </div>
            </Grid>
          ))}
          {comment && value.comments && (
            <Grid
              item
              md={12}
              style={{
                background: '#f3f3f4',
                padding: '0.5rem 1rem',
                borderRadius: '0.2rem',
              }}
            >
              <Header hiddenProp={{ mdUp: true }} text='comments' />
              {columns.comments ? (
                column.comments(value, cellProps, {
                  columnIndex: idx,
                  rowIndex: index,
                })
              ) : (
                <Typography variant='body1'>
                  <b>Comment:</b> {value.comments}
                </Typography>
              )}
            </Grid>
          )}
          {reject && value.reject_reason && (
            <Grid
              item
              md={12}
              style={{
                background: '#f3f3f4',
                padding: '0.5rem 1rem',
                borderRadius: '0.2rem',
              }}
            >
              <Header hiddenProp={{ mdUp: true }} text='Reject Reason' />
              {columns.reject_reason ? (
                column.rejectReason(value, cellProps, {
                  columnIndex: idx,
                  rowIndex: index,
                })
              ) : (
                <Typography variant='body1'>
                  <b>Reject Reason:</b> {value.reject_reason}
                </Typography>
              )}
            </Grid>
          )}
        </GridListRow>
      ))}
    </>
  );
};

export default GridListView;
