import React from 'react';

import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
} from '@mui/material';
import { useField } from 'formik';

/**
 * Select dropdown component for forms
 * @param {Object} props - Component properties
 * @returns {JSX.Element}
 */
const SelectField = ({ label, options, helperText, required, ...props }) => {
  const [field, meta] = useField(props);
  const isError = <PERSON><PERSON>an(meta.touched && meta.error);

  return (
    <FormControl fullWidth variant='outlined' error={isError} margin='normal'>
      <InputLabel id={`${props.id || props.name}-label`}>
        {required ? `${label} *` : label}
      </InputLabel>
      <Select
        {...field}
        {...props}
        labelId={`${props.id || props.name}-label`}
        label={required ? `${label} *` : label}
      >
        {options.map(option => (
          <MenuItem key={option.value} value={option.value}>
            {option.label}
          </MenuItem>
        ))}
      </Select>
      {(isError || helperText) && (
        <FormHelperText>{isError ? meta.error : helperText}</FormHelperText>
      )}
    </FormControl>
  );
};

export default SelectField;
