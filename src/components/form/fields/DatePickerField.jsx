import React from 'react';

import { FormHelperText, FormControl } from '@mui/material';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs from 'dayjs';
import { useField, useFormikContext } from 'formik';

/**
 * Date picker component for forms
 * @param {Object} props - Component properties
 * @returns {JSX.Element}
 */
const DatePickerField = ({ label, helperText, required, ...props }) => {
  const { setFieldValue } = useFormikContext();
  const [field, meta] = useField(props);
  const isError = Boolean(meta.touched && meta.error);

  // Convert string date to dayjs object if it exists
  const value = field.value ? dayjs(field.value) : null;

  return (
    <FormControl fullWidth error={isError} margin='normal'>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DatePicker
          label={required ? `${label} *` : label}
          value={value}
          onChange={newValue => {
            setFieldValue(field.name, newValue ? newValue.toISOString() : null);
          }}
          slotProps={{
            textField: {
              error: isError,
              helperText: isError ? meta.error : helperText,
              fullWidth: true,
              variant: 'outlined',
            },
          }}
          {...props}
        />
      </LocalizationProvider>
      {!isError && helperText && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  );
};

export default DatePickerField;
