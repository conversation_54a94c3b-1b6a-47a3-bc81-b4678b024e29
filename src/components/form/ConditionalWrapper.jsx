import React from 'react';

import { useFormikContext } from 'formik';

/**
 * A wrapper component that conditionally renders its children based on a condition function
 *
 * @param {Object} props - Component properties
 * @param {Function} props.condition - Function that evaluates form values and returns boolean
 * @param {React.ReactNode} props.children - Child components to render conditionally
 * @returns {JSX.Element|null}
 */
const ConditionalWrapper = ({ condition, children }) => {
  const { values } = useFormikContext();

  // Evaluate the condition with the current form values
  const shouldRender = condition(values);

  // Only render children if the condition is met
  return shouldRender ? children : null;
};

export default ConditionalWrapper;
