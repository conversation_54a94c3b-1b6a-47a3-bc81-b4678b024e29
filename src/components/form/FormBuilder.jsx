import React from 'react';

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Typo<PERSON>, Divider } from '@mui/material';
import { Formik, Form } from 'formik';

import ConditionalWrapper from './ConditionalWrapper';
import {
  TextField,
  SelectField,
  CheckboxField,
  RadioGroupField,
  DatePickerField,
  FileUploadField,
  MultiSelectField,
} from './fields';
import { createValidationSchema } from './validation/validationRules';

/**
 * FormBuilder - A component to dynamically generate forms based on field definitions
 *
 * @param {Object} props - Component properties
 * @param {Array} props.fields - Array of field definition objects
 * @param {Object} props.initialValues - Initial values for the form
 * @param {Function} props.onSubmit - Function to handle form submission
 * @param {String} props.title - Optional form title
 * @param {String} props.submitButtonText - Text for the submit button
 * @param {String} props.cancelButtonText - Text for the cancel button
 * @param {Function} props.onCancel - Function to handle form cancellation
 * @param {Object} props.formLayout - Form layout configuration (grid spacing, etc.)
 * @returns {JSX.Element}
 */
const FormBuilder = ({
  fields,
  initialValues = {},
  onSubmit,
  title,
  submitButtonText = 'Submit',
  cancelButtonText = 'Cancel',
  onCancel,
  formLayout = {
    spacing: 2,
    direction: 'row',
    containerProps: {},
    submitButtonProps: {},
    cancelButtonProps: {},
  },
}) => {
  // Create validation schema from field definitions
  const validationSchema = createValidationSchema(fields);

  // Create default initial values if not provided
  const defaultInitialValues = {};
  fields.forEach(field => {
    if (!(field.name in initialValues)) {
      switch (field.type) {
        case 'checkbox':
          defaultInitialValues[field.name] = false;
          break;
        case 'array':
          defaultInitialValues[field.name] = [];
          break;
        case 'number':
          defaultInitialValues[field.name] = '';
          break;
        default:
          defaultInitialValues[field.name] = '';
      }
    }
  });

  const finalInitialValues = { ...defaultInitialValues, ...initialValues };

  // Render a specific field based on its type
  const renderField = field => {
    const { type, name, label, helperText, required, options, ...restProps } =
      field;

    const commonProps = {
      name,
      label: label || name,
      helperText,
      required,
      ...restProps,
    };

    switch (type) {
      case 'text':
      case 'email':
      case 'password':
      case 'number':
      case 'tel':
        return <TextField type={type} {...commonProps} />;
      case 'select':
        return <SelectField options={options || []} {...commonProps} />;
      case 'multiselect':
        return <MultiSelectField options={options || []} {...commonProps} />;
      case 'checkbox':
        return <CheckboxField {...commonProps} />;
      case 'radio':
        return <RadioGroupField options={options || []} {...commonProps} />;
      case 'date':
        return <DatePickerField {...commonProps} />;
      case 'file':
        return <FileUploadField {...commonProps} />;
      default:
        return <TextField {...commonProps} />;
    }
  };

  return (
    <Box {...formLayout.containerProps}>
      {title && (
        <>
          <Typography variant='h5' component='h2' gutterBottom>
            {title}
          </Typography>
          <Divider sx={{ mb: 3 }} />
        </>
      )}

      <Formik
        initialValues={finalInitialValues}
        validationSchema={validationSchema}
        onSubmit={onSubmit}
        enableReinitialize
      >
        {({ isSubmitting, isValid, dirty }) => (
          <Form>
            <Grid
              container
              spacing={formLayout.spacing}
              direction={formLayout.direction}
            >
              {fields.map(field => {
                // Wrap field in ConditionalWrapper if it has conditional property
                const fieldComponent = renderField(field);

                return (
                  <Grid
                    item
                    key={field.name}
                    xs={12}
                    md={field.gridProps?.md || 12}
                    sm={field.gridProps?.sm || 12}
                    lg={field.gridProps?.lg || 12}
                    xl={field.gridProps?.xl || 12}
                  >
                    {field.conditional ? (
                      <ConditionalWrapper condition={field.conditional}>
                        {fieldComponent}
                      </ConditionalWrapper>
                    ) : (
                      fieldComponent
                    )}
                  </Grid>
                );
              })}

              <Grid item xs={12}>
                <Box
                  sx={{
                    mt: 3,
                    display: 'flex',
                    justifyContent: 'flex-end',
                    gap: 2,
                  }}
                >
                  {onCancel && (
                    <Button
                      variant='outlined'
                      color='secondary'
                      onClick={onCancel}
                      disabled={isSubmitting}
                      {...formLayout.cancelButtonProps}
                    >
                      {cancelButtonText}
                    </Button>
                  )}
                  <Button
                    type='submit'
                    variant='contained'
                    color='primary'
                    disabled={isSubmitting || !(isValid && dirty)}
                    {...formLayout.submitButtonProps}
                  >
                    {submitButtonText}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Form>
        )}
      </Formik>
    </Box>
  );
};

export default FormBuilder;
