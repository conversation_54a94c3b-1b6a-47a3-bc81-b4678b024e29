import React from 'react';

import { InfoOutlined } from '@mui/icons-material';
import { Box, IconButton, Tooltip, Typography } from '@mui/material';

import { PR_STATUS_LABEL } from 'Utilities/constants/paymentRequest';
import { getFormattedDateTime } from 'Utilities/dateUtils';

const CustomTooltip = ({
  created_date,
  creator_name,
  approver_name,
  approved_date,
  rejector_name,
  rejected_date,
  payer_name,
  paid_date,
  data,
  is_paid_and_approved,
}) => {
  const status = [
    PR_STATUS_LABEL.REJECTED,
    PR_STATUS_LABEL.PAID,
    PR_STATUS_LABEL.APPROVED,
  ];

  const statusTooltips =
    data === PR_STATUS_LABEL.REJECTED
      ? {
          title: `${data} By: ${rejector_name} `,
          date: rejected_date,
        }
      : data === PR_STATUS_LABEL.PAID
        ? {
            title: `${data} By: ${payer_name}`,
            date: paid_date,
          }
        : data === PR_STATUS_LABEL.APPROVED
          ? {
              title: `${data} By: ${approver_name}`,
              date: approved_date,
            }
          : null;

  return (
    <Tooltip
      arrow
      enterTouchDelay={0}
      title={
        <Box p={1}>
          <Box>
            <Typography>
              Created By: <span>{creator_name}</span>
            </Typography>
            <Typography>{getFormattedDateTime(created_date)}</Typography>
          </Box>
          {status?.includes(data) && (
            <Box mt={1}>
              <Typography>{statusTooltips?.title}</Typography>
              <Typography>
                {getFormattedDateTime(statusTooltips?.date)}
              </Typography>
            </Box>
          )}
          {is_paid_and_approved && (
            <Box mt={1}>
              <Typography>Approved By: {approver_name}</Typography>
              <Typography>
                Paid At: {getFormattedDateTime(paid_date)}
              </Typography>
            </Box>
          )}
        </Box>
      }
    >
      <IconButton size='small' color='primary'>
        <InfoOutlined />
      </IconButton>
    </Tooltip>
  );
};

export default CustomTooltip;
