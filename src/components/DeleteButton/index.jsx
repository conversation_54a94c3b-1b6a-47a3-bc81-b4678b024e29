import React from 'react';

import { Delete as DeleteIcon } from '@mui/icons-material';
import { Button } from '@mui/material';

const DeleteButton = ({
  children,
  toggleConfirmDialog,
  isDelete,
  disabled,
  text,
  ...rest
}) => {
  if (isDelete && children) {
    return children;
  }

  return isDelete ? (
    <Button
      size='small'
      variant='contained'
      color='primary'
      startIcon={<DeleteIcon />}
      onClick={() => toggleConfirmDialog()}
      disabled={disabled}
      {...rest}
    >
      {text || 'DETELE'}
    </Button>
  ) : null;
};

export default DeleteButton;
