import React from 'react';

import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from '@mui/material';

const ConfirmationDialog = ({
  title,
  open,
  children,
  onCancel,
  onConfirm,
  confirmText = 'ok',
  confirmButtonVariant,
  disabled,
}) => {
  const onClose = (_, reason) => {
    if (reason !== 'backdropClick') {
      onCancel();
    }
  };
  return (
    <Dialog
      onClose={onClose}
      disableEscapeKeyDown
      maxWidth='xs'
      aria-labelledby='confirmation-dialog-title'
      open={open}
    >
      <DialogTitle>
        <b>{title}</b>
      </DialogTitle>
      <DialogContent dividers>{children}</DialogContent>
      <DialogActions>
        {onCancel && (
          <Button
            autoFocus
            onClick={onCancel}
            color='primary'
            data-cy='mandi.confirmationDialog.cancel'
          >
            Cancel
          </Button>
        )}
        <Button
          onClick={onConfirm}
          color='primary'
          variant={confirmButtonVariant}
          disabled={disabled}
          data-cy='mandi.confirmationDialog.confirm'
        >
          {confirmText}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
export default ConfirmationDialog;
