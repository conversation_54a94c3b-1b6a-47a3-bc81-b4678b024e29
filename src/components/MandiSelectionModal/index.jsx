import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  Typography,
  Box,
  Checkbox,
  FormControlLabel,
  Divider,
} from '@mui/material';

const MandiSelectionModal = ({ open, onClose, mandiNumbers, onSelectMandi, onSelectMultiple }) => {
  const [selectedMandis, setSelectedMandis] = useState([]);
  const [selectAll, setSelectAll] = useState(false);

  // Handle individual checkbox change
  const handleMandiToggle = (mandiNumber) => {
    setSelectedMandis(prev => {
      const newSelected = prev.includes(mandiNumber)
        ? prev.filter(m => m !== mandiNumber)
        : [...prev, mandiNumber];
      
      // Update select all state
      setSelectAll(newSelected.length === mandiNumbers.length);
      
      return newSelected;
    });
  };

  // Handle select all checkbox
  const handleSelectAllToggle = () => {
    if (selectAll) {
      setSelectedMandis([]);
      setSelectAll(false);
    } else {
      setSelectedMandis([...mandiNumbers]);
      setSelectAll(true);
    }
  };

  // Handle download selected
  const handleDownloadSelected = () => {
    if (selectedMandis.length > 0) {
      onSelectMultiple(selectedMandis);
      // Reset state
      setSelectedMandis([]);
      setSelectAll(false);
    }
  };

  // Handle single mandi selection (individual generate)
  const handleSingleSelect = (mandiNumber) => {
    onSelectMandi(mandiNumber);
    // Reset state
    setSelectedMandis([]);
    setSelectAll(false);
  };

  // Handle modal close
  const handleClose = () => {
    setSelectedMandis([]);
    setSelectAll(false);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Typography variant="h6" component="div">
          Select Mandi Numbers for Customer Slip
        </Typography>
      </DialogTitle>
      <DialogContent>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Choose mandi numbers to generate customer slips:
        </Typography>
        
        {/* Select All Option */}
        <Box sx={{ mb: 2 }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={selectAll}
                onChange={handleSelectAllToggle}
                color="primary"
              />
            }
            label={
              <Typography variant="subtitle2" fontWeight="medium">
                Select All ({mandiNumbers.length} mandi numbers)
              </Typography>
            }
          />
        </Box>
        
        <Divider sx={{ mb: 2 }} />
        
        {/* Individual Mandi Options */}
        <List>
          {mandiNumbers.map((mandiNumber, index) => (
            <ListItem key={index} disablePadding sx={{ mb: 1 }}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '100%',
                  border: '1px solid #e0e0e0',
                  borderRadius: 1,
                  p: 1,
                }}
              >
                {/* Checkbox */}
                <Checkbox
                  checked={selectedMandis.includes(mandiNumber)}
                  onChange={() => handleMandiToggle(mandiNumber)}
                  color="primary"
                />
                
                {/* Mandi Info */}
                <ListItemText
                  primary={
                    <Typography variant="subtitle1" fontWeight="medium">
                      Mandi No: {mandiNumber}
                    </Typography>
                  }
                  sx={{ flex: 1, ml: 1 }}
                />
                
                {/* Individual Generate Button */}
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => handleSingleSelect(mandiNumber)}
                  sx={{ ml: 2 }}
                >
                  Generate PDF
                </Button>
              </Box>
            </ListItem>
          ))}
        </List>
      </DialogContent>
      <DialogActions sx={{ justifyContent: 'space-between', px: 3, pb: 2 }}>
        <Button onClick={handleClose} color="secondary">
          Cancel
        </Button>
        
        <Box>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1, textAlign: 'right' }}>
            {selectedMandis.length} selected
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={handleDownloadSelected}
            disabled={selectedMandis.length === 0}
            sx={{ minWidth: 140 }}
          >
            Download Selected ({selectedMandis.length})
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
};

export default MandiSelectionModal;
