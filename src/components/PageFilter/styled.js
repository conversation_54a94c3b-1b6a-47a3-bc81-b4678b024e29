import { Paper } from '@mui/material';
import styled from 'styled-components';

export const FilterWrapper = styled(Paper)`
  display: flex;
  padding: ${props => props.theme.spacing(1, 1)};
  margin-bottom: ${props => props.theme.spacing(1)};
  .datepicker-icon {
    padding: 0;
  }
`;

export const Wrapper = styled.div`
  display: flex;
  height: 100%;
  width: 100%;
  flex-direction: column;
  overflow: auto;

  ${props => props.theme.breakpoints.down('md')} {
    align-self: center;
  }
`;
