import { SwipeableDrawer, Typography } from '@mui/material';

const Drawer = ({
  content,
  anchor,
  title,
  style,
  toggleDrawer = () => {},
  state,
}) => (
  <div>
    <Typography
      type='button'
      className={style}
      onClick={toggleDrawer(anchor, true)}
    >
      {title}
    </Typography>
    <SwipeableDrawer
      anchor={anchor}
      open={state[anchor]}
      onClose={toggleDrawer(anchor, false)}
      onOpen={toggleDrawer(anchor, true)}
    >
      {content}
    </SwipeableDrawer>
  </div>
);

export default Drawer;
