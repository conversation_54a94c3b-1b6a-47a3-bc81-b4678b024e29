import React from 'react';

import { Button } from '@mui/material';

import { ButtonProgress, Wrapper } from './styled';

const AppButton = ({ children, loading, color = 'primary', ...props }) => {
  return (
    //! Moving the disabled prop to the front to override the loading. As the prop value coming at the end will have the priority,
    //! we will be able to disable a button with disabled={true} (even with loading={true})
    <Wrapper>
      <Button disabled={loading} {...props} color={color}>
        {children}
      </Button>
      {loading && <ButtonProgress color={color} size={24} />}
    </Wrapper>
  );
};

export default AppButton;
