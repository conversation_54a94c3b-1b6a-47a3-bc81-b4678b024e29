import React from 'react';

import { render } from 'test-utils';

import AppButton from './index';

test('should renders App Button', () => {
  const { getByText } = render(<AppButton>Submit</AppButton>);
  expect(getByText('Submit')).toBeDefined();
});

test('should renders App Button with loading', () => {
  const { getByRole } = render(<AppButton loading>Submit</AppButton>);
  expect(getByRole('progressbar')).toBeDefined();
});
