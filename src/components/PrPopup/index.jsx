import React, { useState } from 'react';

import { Description, FiberManualRecord } from '@mui/icons-material';
import {
  Box,
  DialogActions,
  FormControlLabel,
  Grid,
  Link,
  Paper,
  Radio,
  RadioGroup,
  Typography,
} from '@mui/material';
import { Formik } from 'formik';
import { requestPaymentForGateIn } from 'Services/trips';

import { useSiteValue } from 'App/SiteContext';
import AppButton from 'Components/AppButton';
import {
  FieldDatePicker,
  UploadInput,
  FieldInput,
  FieldSwitch,
} from 'Components/FormFields';
import CustomModal from 'Components/Modal';
import { mergeValidator, toFixedNumber } from 'Utilities';
import {
  PAYMENT_MODE,
  PR_PAYMENT_METHOD,
  PRIORITY,
} from 'Utilities/constants/paymentRequest';
import { currency } from 'Utilities/currencyFormatter';
import fileUpload from 'Utilities/fileUpload';
import {
  validateFileUpload,
  validateMax,
  validateMin,
  validatePositiveInt,
  validateRequired,
} from 'Utilities/formvalidation';

const PrPopup = ({
  popupOpen,
  setPopupOpen,
  amountToBeAdjusted,
  partnerdetails = {},
  prPopupData,
  billUrl,
  paymentRequestType,
  listOfPaymentRequests,
}) => {
  const [loading, setLoading] = useState(false);
  const currentDate = Date.now();
  const { mandiId, mandiList = [], userInfo } = useSiteValue();
  const { approver_ids = [] } = mandiList.find(i => i?.id === mandiId) || {};
  const {
    id: gatein_id,
    partner,
    contact,
    transporterid,
    amount: totalAmount,
  } = partnerdetails;
  const totalAmountCanBeRaised =
    totalAmount - toFixedNumber(amountToBeAdjusted, 2) || totalAmount;
  const { id: parent_id } = prPopupData || {};

  const getBillUniqueId = bill => {
    const splittedUrl = bill?.split('/') || [];
    return splittedUrl[splittedUrl.length - 2];
  };

  const createPaymentRequest = async values => {
    setLoading(true);
    const {
      upload_bill,
      partial_amount,
      partial_payment,
      payment_method,
      bill_number,
      bill_date,
    } = values;
    const fileData = await fileUpload(upload_bill);

    const postRequestBody = {
      payment_request: {
        created_date: currentDate,
        payment_request_type: paymentRequestType,
        creator_id: userInfo.id,
        priority: PRIORITY.LOW,
        vendor_id: transporterid,
        gatein_id: +gatein_id,
        amount: partial_payment ? +partial_amount : totalAmountCanBeRaised,
        bill: fileData?.[0] || getBillUniqueId(billUrl),
        due_date: currentDate,
        bill_number,
        bill_date,
        approver_ids,
        cost_head_id: 4,
        status: 1,
        payment_mode:
          payment_method === PR_PAYMENT_METHOD.PAY_BY_CASH
            ? PAYMENT_MODE.CASH
            : PAYMENT_MODE.NORMAL_PR,
        parent_bill_id: billUrl ? parent_id : null,
      },
    };
    requestPaymentForGateIn(postRequestBody)
      .then(() => {
        setPopupOpen(false);
        listOfPaymentRequests();
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <>
      <CustomModal
        open={popupOpen}
        isCloseIcon={false}
        title={
          <Typography variant='h5' color='primary' style={{ margin: '10px' }}>
            <b>{gatein_id}</b>
          </Typography>
        }
        contentSize
      >
        <Box mx='10px'>
          <Formik
            initialValues={{
              partial_payment: null,
              partial_amount: null,
              upload_bill: null,
              payment_method: null,
            }}
            onSubmit={createPaymentRequest}
          >
            {({ values, handleSubmit, handleChange }) => (
              <>
                {/* User information */}
                <Grid container sx={{ display: 'flex' }} spacing={4}>
                  <Grid item>
                    <Typography variant='body1' pr={2}>
                      Transporter Name:
                      <b color='gray'>{partner}</b>
                    </Typography>
                  </Grid>
                  <Grid item>
                    <Typography>
                      Contact: <b>{contact}</b>
                    </Typography>
                  </Grid>
                </Grid>
                {/* Payment and attachment component */}
                <Box display='flex' alignItems='center' py={2}>
                  <Typography variant='body1' style={{ paddingRight: '5rem' }}>
                    Send Partial Payment
                  </Typography>
                  <Box display='flex' flexDirection='row' alignItems='center'>
                    <Typography variant='body1'>No</Typography>
                    <FieldSwitch
                      size='medium'
                      label='Yes'
                      name='partial_payment'
                      labelPlacement='end'
                      InputLabelProps={{
                        fullWidth: false,
                      }}
                    />
                  </Box>
                </Box>
                {/* Partial */}
                <Box display='flex' alignItems='center'>
                  <Box display='flex' alignItems='center' pr={2}>
                    <FiberManualRecord
                      color='primary'
                      style={{ fontSize: 12 }}
                    />
                    <Typography>Total Amount </Typography>
                  </Box>
                  {values.partial_payment && (
                    <Box display='flex' alignItems='center' pr={1}>
                      <FieldInput
                        name='partial_amount'
                        variant='outlined'
                        type='number'
                        color='primary'
                        size='small'
                        className='amountInput'
                        required={!!values.partial_payment}
                        onChange={handleChange}
                        validate={mergeValidator(
                          validateRequired,
                          validatePositiveInt,
                          validateMin(0),
                          validateMax(+totalAmountCanBeRaised + 1)
                        )}
                        style={{
                          width: '8rem',
                        }}
                      />
                      <Typography
                        style={{ paddingLeft: 20, fontWeight: 'bold' }}
                      >
                        /
                      </Typography>
                    </Box>
                  )}
                  <Typography variant='body1'>
                    {currency(totalAmountCanBeRaised)}
                  </Typography>
                </Box>
                <Grid container py={2} display='flex' spacing={4}>
                  <Grid item display='flex' alignItems='center'>
                    <Typography width='40%'>Bill Number :</Typography>
                    <FieldInput
                      name='bill_number'
                      variant='outlined'
                      type='text'
                      color='primary'
                      size='small'
                      InputLabelProps={{
                        required: true,
                        shrink: true,
                      }}
                      required
                      validate={validateRequired}
                      style={{
                        width: '60%',
                      }}
                    />
                  </Grid>
                  <Grid item display='flex' alignItems='center'>
                    <Typography width='40%'>Bill Date :</Typography>
                    <FieldDatePicker
                      name='bill_date'
                      variant='inline'
                      inputVariant='outlined'
                      format='DD/MM/YYYY'
                      margin='normal'
                      size='small'
                      textFieldProps={{
                        InputLabelProps: {
                          required: true,
                          shrink: true,
                        },
                      }}
                      required
                      validate={validateRequired}
                      style={{
                        width: '60%',
                      }}
                    />
                  </Grid>
                </Grid>
                <Box py={2} display='flex' alignItems='center'>
                  <UploadInput
                    accept='image/*, application/pdf'
                    label='Attach Bill'
                    name='upload_bill'
                    multiple={false}
                    size='small'
                    required={!billUrl}
                    disabled={!!billUrl}
                    validate={!billUrl && validateFileUpload}
                    style={{ marginRight: '1rem' }}
                  />
                  <Box display='flex'>
                    {values?.upload_bill ? (
                      <>
                        <Description color='primary' style={{ padding: 2 }} />
                        <Typography>{values.upload_bill?.[0]?.name}</Typography>
                      </>
                    ) : billUrl ? (
                      <Link href={billUrl} target='_blank'>
                        <Description color='primary' />
                      </Link>
                    ) : null}
                  </Box>
                </Box>
                <Paper variant='outlined'>
                  <Box marginY={1} marginX={2}>
                    <RadioGroup
                      name='payment_method'
                      onChange={handleChange}
                      defaultValue='normal-pr'
                    >
                      <FormControlLabel
                        value='pay-by-cash'
                        control={<Radio color='primary' />}
                        label={
                          <Box display='flex' gridGap={10}>
                            <Typography display='inline'>
                              Pay By Cash
                            </Typography>
                          </Box>
                        }
                      />
                      <FormControlLabel
                        value='normal-pr'
                        control={<Radio color='primary' />}
                        label='Normal PR'
                      />
                    </RadioGroup>
                  </Box>
                </Paper>
                {/* Dialogue Action */}
                <DialogActions>
                  <AppButton
                    variant='contained'
                    color='inherit'
                    disabled={loading}
                    onClick={() => setPopupOpen(false)}
                  >
                    Cancel
                  </AppButton>
                  <AppButton
                    variant='contained'
                    color='primary'
                    onClick={handleSubmit}
                    disabled={
                      loading || !values.bill_date || !values.bill_number
                    }
                  >
                    Save
                  </AppButton>
                </DialogActions>
              </>
            )}
          </Formik>
        </Box>
      </CustomModal>
    </>
  );
};

export default PrPopup;
