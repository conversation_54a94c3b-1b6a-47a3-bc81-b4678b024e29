import { useCallback, useState } from 'react';

import Header from './components/Header';
import Sidebar from './components/Sidebar';
import { AppWrapper, MainContentWrapper, SiteWrapper } from './styled';

const AppLayout = ({ children }) => {
  const [showSidebar, setShowSidebar] = useState(false);
  const toggleSidebar = useCallback(() => setShowSidebar(prev => !prev), []);

  return (
    <SiteWrapper>
      <Header toggleSidebar={toggleSidebar} />
      <AppWrapper>
        <Sidebar sidebarOpen={showSidebar} toggleSidebar={toggleSidebar} />
        <MainContentWrapper>{children}</MainContentWrapper>
      </AppWrapper>
    </SiteWrapper>
  );
};

export const SiteLayout = SiteWrapper;

export default AppLayout;
