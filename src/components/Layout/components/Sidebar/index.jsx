import { Fragment, useState } from 'react';

import { ChevronLeft, ExpandLess, ExpandMore, OpenInNew } from '@mui/icons-material';
import {
  Collapse,
  Divider,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import PropTypes from 'prop-types';
import { NavLink, useLocation } from 'react-router-dom';

import { useSiteValue } from 'App/SiteContext';
import FruitXLogo from 'Assets/FruitX.png';
import AppIcons from 'Components/AppIcons/index.jsx';
import envConfig from 'Config/envConfig';

import { StyledDrawer, ToolbarIconContainer, ToolbarImage } from './styled';

const Sidebar = ({ sidebarOpen, toggleSidebar }) => {
  const { allowedTabs } = useSiteValue();
  const [open, setOpen] = useState({});
  const location = useLocation();

  const handleClick = id => {
    setOpen({ ...open, [id]: !open[id] });
  };

  const handleExternalLink = (url) => {
    const fullUrl = `${envConfig.MandiOpsUi}${url}`;
    window.open(fullUrl, '_blank', 'noopener,noreferrer');
    toggleSidebar(); // Close sidebar after clicking external link
  };

  return (
    <StyledDrawer
      sidebarOpen={sidebarOpen}
      open={sidebarOpen}
      onClose={toggleSidebar}
    >
      <ToolbarIconContainer>
        <ToolbarImage src={FruitXLogo} alt='sidebar-logo' />
        <IconButton
          size='small'
          variant='contained'
          data-cy='mandi.closeSidebar'
          className='classes.backButton'
          onClick={toggleSidebar}
          sx={{
            bgcolor: 'primary.main',
            p: 0.5,
            '&:hover': { bgcolor: 'primary.main', transform: 'scale(1.1)' },
          }}
        >
          <ChevronLeft
            fontSize='small'
            sx={{ color: 'common.white', fontWeight: 'bold' }}
          />
        </IconButton>
      </ToolbarIconContainer>
      <Divider />
      <List
        component='nav'
        aria-labelledby='nested-list-subheader'
        sx={{ flex: 1, overflow: 'auto' }}
      >
        {allowedTabs.map(
          t =>
            t.main &&
            (t.children && t.childrenDropdown ? (
              <Fragment key={t.id}>
                <ListItemButton
                  data-cy={`mandi.sidebar.${t.id}`}
                  onClick={() => handleClick(t.id)}
                  dense
                  disableGutters
                >
                  <ListItem>
                    <ListItemIcon>
                      <AppIcons name={t.icon} />
                    </ListItemIcon>
                    <ListItemText primary={t.label} />
                    {open[t.id] ? <ExpandLess /> : <ExpandMore />}
                  </ListItem>
                </ListItemButton>
                <Collapse in={open[t.id]} timeout='auto' unmountOnExit>
                  <List component='div'>
                    {t.children.map(child => (
                      <ListItemButton
                        selected={
                          `/app/${t.url}/${child?.url}` === location.pathname
                        }
                        key={child.id}
                        to={`/app/${t.url}/${child?.url}`}
                        component={NavLink}
                        onClick={toggleSidebar}
                        data-cy={`mandi.sidebar.${child.id}`}
                        sx={{ pl: 4 }}
                        dense
                        disableGutters
                      >
                        <ListItem>
                          <ListItemIcon>
                            <AppIcons name={child.icon} />
                          </ListItemIcon>
                          <ListItemText primary={child.label} />
                        </ListItem>
                      </ListItemButton>
                    ))}
                  </List>
                </Collapse>
              </Fragment>
            ) : t.external ? (
              // External link - open in new tab
              <ListItemButton
                key={t.id}
                onClick={() => handleExternalLink(t.url)}
                data-cy={`mandi.sidebar.${t.id}`}
                dense
                disableGutters
              >
                <ListItem>
                  <ListItemIcon>
                    <AppIcons name={t.icon} />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        {t.label}
                        <OpenInNew
                          sx={{
                            fontSize: 14,
                            color: 'text.secondary',
                            opacity: 0.7
                          }}
                        />
                      </div>
                    }
                  />
                </ListItem>
              </ListItemButton>
            ) : (
              // Internal link - use NavLink
              <ListItemButton
                selected={`/app/${t.url}` === location.pathname}
                key={t.id}
                to={`/app/${t.url}`}
                component={NavLink}
                onClick={toggleSidebar}
                data-cy={`mandi.sidebar.${t.id}`}
                dense
                disableGutters
              >
                <ListItem>
                  <ListItemIcon>
                    <AppIcons
                      color={
                        `/app/${t.url}` === location.pathname ? 'primary' : ''
                      }
                      name={t.icon}
                    />
                  </ListItemIcon>
                  <ListItemText primary={t.label} />
                </ListItem>
              </ListItemButton>
            ))
        )}
      </List>
    </StyledDrawer>
  );
};

Sidebar.propTypes = {
  sidebarOpen: PropTypes.bool.isRequired,
  toggleSidebar: PropTypes.func.isRequired,
};

export default Sidebar;
