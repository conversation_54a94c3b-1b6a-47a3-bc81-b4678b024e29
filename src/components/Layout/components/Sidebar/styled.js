import { Drawer } from '@mui/material';
import { styled } from '@mui/material/styles';

export const StyledDrawer = styled(Drawer)(({ theme, sidebarOpen }) => ({
  '& .MuiDrawer-paper': {
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.easeInOut,
      duration: theme.transitions.duration.short,
    }),
    display: 'flex',
    flexDirection: 'column',
    ...(sidebarOpen === false && {
      overflowX: 'hidden',
      transition: theme.transitions.create('width', {
        easing: theme.transitions.easing.easeInOut,
        duration: theme.transitions.duration.short,
      }),
    }),
  },
  '& .MuiModal-root': {
    zIndex: theme.zIndex.modal,
  },
}));

export const ToolbarIconContainer = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: theme.spacing(0, 1),
  minHeight: 50,
}));

export const ToolbarImage = styled('img')(() => ({
  height: '2rem',
}));
