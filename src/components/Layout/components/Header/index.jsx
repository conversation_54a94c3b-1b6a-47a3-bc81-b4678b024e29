import { useState } from 'react';

import {
  AccountCircle,
  LogoutOutlined,
  Menu as MenuIcon,
} from '@mui/icons-material';
import {
  Box,
  IconButton,
  ListItemIcon,
  ListItemText,
  MenuItem,
} from '@mui/material';
import PropTypes from 'prop-types';

import { useSiteValue } from 'App/SiteContext';
import FruitXLogo from 'Assets/FruitX.png';
import { useAuth } from 'Contexts/AuthContext';

import { StyledAppBar, StyledMenu, StyledToolbar } from './styled';

const Header = ({ toggleSidebar }) => {
  const { userInfo } = useSiteValue();
  const { logout } = useAuth();
  const [anchorEl, setAnchorEl] = useState(null);

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <StyledAppBar position='relative'>
      <StyledToolbar>
        <IconButton
          edge='start'
          color='inherit'
          aria-label='open sidebar'
          onClick={toggleSidebar}
          data-cy='mandi.header.openSidebar'
        >
          <MenuIcon />
        </IconButton>
        <img src={FruitXLogo} alt='logo' width='100' />
        <Box flex={1} />
        <IconButton
          size='large'
          color='inherit'
          aria-label='profile icon'
          aria-controls='simple-menu'
          aria-haspopup='true'
          data-cy='mandi.profileIcon'
          onClick={event => setAnchorEl(event.currentTarget)}
        >
          <AccountCircle fontSize='inherit' />
        </IconButton>

        <StyledMenu
          id='user-menu'
          anchorEl={anchorEl}
          keepMounted
          open={!!anchorEl}
          onClose={handleClose}
        >
          <MenuItem>
            <ListItemIcon>
              <AccountCircle />
            </ListItemIcon>
            <ListItemText data-cy='mandi.username'>
              {userInfo?.name}
            </ListItemText>
          </MenuItem>
          <MenuItem data-cy='mandi.logout' onClick={logout}>
            <ListItemIcon>
              <LogoutOutlined />
            </ListItemIcon>
            Logout
          </MenuItem>
        </StyledMenu>
      </StyledToolbar>
    </StyledAppBar>
  );
};

Header.propTypes = {
  toggleSidebar: PropTypes.func.isRequired,
};

export default Header;
