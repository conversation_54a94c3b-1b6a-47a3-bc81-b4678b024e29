import { toFixedNumber } from 'Utilities';
import { ADMIN_CONFIG_UNIT, INWARD_TYPE } from 'Utilities/constants';

export const calcNetAmount = (lots_data) => {
  const {
    warehouse_fee_for_truck = 0,
    warehouse_fee_for_crate = 0,
    discount_on_warehouse_fee_for_crate = 0,
    unit_for_config,
    inward_type,
    farmer_commission,
    discount_on_farmer_commission
  } = lots_data?.[0] || {};

  const isInwardTypeTruck = inward_type === INWARD_TYPE.TRUCK;

  const totalNetWeight = lots_data?.reduce(
    (total, { net_weight = 0, is_farmer_return = false }) => {
      if (!is_farmer_return) {
        total += net_weight;
      }
      return total;
    },
    0
  );

  const totalCrates = lots_data?.reduce((total, { units = 0 }) => {
    total += units;
    return total;
  }, 0);

  const warehousingCharge = () => {
    return isInwardTypeTruck
      ? warehouse_fee_for_truck * totalNetWeight
      : unit_for_config === ADMIN_CONFIG_UNIT.KG
      ? warehouse_fee_for_crate * totalNetWeight
      : warehouse_fee_for_crate * totalCrates;
  };

  const totalDiscount = () => {
    return toFixedNumber(
      isInwardTypeTruck
        ? 0
        : unit_for_config === ADMIN_CONFIG_UNIT.KG
        ? discount_on_warehouse_fee_for_crate * totalNetWeight
        : discount_on_warehouse_fee_for_crate * totalCrates,
      2
    );
  };

  const netAmount = toFixedNumber(
    lots_data?.reduce((acc, cur) => {
      acc += +cur.selling_price * +cur.net_weight * (1 - +cur.chute / 100) || 0;
      return acc;
    }, 0) -
      warehousingCharge() +
      totalDiscount(),
    2
  );

  const subTotal = toFixedNumber(
    lots_data?.reduce((acc, cur) => {
      acc += +cur.selling_price * +cur.net_weight * (1 - +cur.chute / 100) || 0;
      return acc;
    }, 0)
  );

  const totalNetAmount =
    netAmount -
    (subTotal * farmer_commission) / 100 +
    (subTotal * discount_on_farmer_commission) / 100;

  return toFixedNumber(totalNetAmount);
};

export const calcLotSellingPriceForFarmer = (lot) => {
  const { selling_price_per_unit = 0, selling_price = 0, discount_per_unit = 0, enable_pack_auction = false, auction_price_type = 'UNIT' } = lot;
  if (!enable_pack_auction) {
    return selling_price
  }

  if (auction_price_type === 'UNIT') {
    return selling_price_per_unit === 0 ? selling_price_per_unit : toFixedNumber(selling_price_per_unit + discount_per_unit)
  }

  return selling_price === 0 ? selling_price : toFixedNumber(selling_price + discount_per_unit)
}