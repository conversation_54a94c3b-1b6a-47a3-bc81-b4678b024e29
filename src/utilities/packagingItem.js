import { PACKAGING_ITEM } from 'Utilities/constants';

export const getFilteredPackagingItems = packagingItems => {
  const toBeFilteredItems = Object.values(PACKAGING_ITEM);

  return packagingItems.reduce((modifiedPackagingItems, item) => {
    if (toBeFilteredItems.includes(item.mandi_identifier)) {
      modifiedPackagingItems[item.mandi_identifier] = item;
    } else if (toBeFilteredItems.includes(item.text)) {
      modifiedPackagingItems[item.text] = item;
    }
    return modifiedPackagingItems;
  }, {});
};
