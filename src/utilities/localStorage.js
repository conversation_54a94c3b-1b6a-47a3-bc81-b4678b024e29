/**
 * Local storage utility functions for managing user data
 */

// In-memory cache for better performance
let userObj = null;
let userPermission = null;
let userDcId = null;
let mandiList = null;
let mandiConfig = null;
let mandiId = null;
let auctionDate = null;
let satelliteList = null;
let satelliteId = null;

/**
 * Save user data to local storage and memory
 * @param {Object} user - User data to save
 */
export const saveUserData = user => {
  try {
    if (!user) {
      return;
    }
    userObj = user;
    localStorage.setItem('user', JSON.stringify(userObj));
  } catch (error) {
    console.error('Error saving user data to localStorage:', error);
  }
};

/**
 * Remove user data from local storage and memory
 */
export const removeUser = () => {
  userObj = null;
  userPermission = null;
  mandiList = null;
  mandiId = null;
  auctionDate = null;
  mandiConfig = null;
  satelliteList = null;
  satelliteId = null;
  [
    'user',
    'userpermission',
    'dcid',
    'mandiId',
    'mandilist',
    'satelliteList',
    'satelliteId',
    'auctionDate',
    'mandiConfig',
  ].forEach(key => localStorage.removeItem(key));
};

const loadFromLocalStorage = data => {
  const locaStorageData = localStorage.getItem(data);
  return locaStorageData ? JSON.parse(locaStorageData) : null;
};

/**
 * Get user data from memory or local storage
 * @returns {Object|null} User data or null if not logged in
 */
export const getUserData = () => {
  if (userObj) {
    return userObj;
  }
  userObj = loadFromLocalStorage('user');
  return userObj;
};

/**
 * Check if user is authenticated
 * @returns {boolean} True if user is authenticated
 */
export const isAuthenticated = () => {
  try {
    // First, directly check for the authToken (our new direct token storage)
    const directToken = localStorage.getItem('authToken');
    if (directToken) {
      return true;
    }

    // Fallback to checking user data
    const user = getUserData();
    if (!user) {
      return false;
    }

    // Check for token in user object
    const hasToken = !!user.authentication_token;

    // Return authentication status
    return hasToken;
  } catch (error) {
    console.error('Error checking authentication status:', error);
    return false;
  }
};

/**
 * Get geolocation coordinates for API requests
 * Note: This returns a Promise that resolves to coordinates
 * @returns {Promise<{latitude: string, longitude: string}>} Promise with coordinates
 */
export const getCoordinates = () => {
  return new Promise(resolve => {
    // Default to Bangalore coordinates
    const defaultCoords = {
      latitude: '12.9716',
      longitude: '77.5946',
    };

    // Try to get actual location if browser supports it
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        position => {
          resolve({
            latitude: position.coords.latitude.toString(),
            longitude: position.coords.longitude.toString(),
          });
        },
        () => {
          // On error, use default coordinates
          resolve(defaultCoords);
        }
      );
    } else {
      // Geolocation not supported
      resolve(defaultCoords);
    }
  });
};

export const saveUserPermission = permission => {
  userPermission = permission;
  localStorage.setItem('userpermission', JSON.stringify(permission));
};

export const getSavedUserDCId = () => {
  if (userDcId) {
    return userDcId;
  }

  userDcId = localStorage.dcid || null;

  return userDcId;
};

export const saveUserDCId = id => {
  userDcId = id;
  localStorage.setItem('dcid', id);
};

export const getSavedMandiId = () => {
  if (mandiId) {
    return mandiId;
  }
  mandiId = loadFromLocalStorage('mandiId');
  return mandiId;
};

export const saveMandiId = id => {
  localStorage.setItem('mandiId', id);
};

export const getSavedMandis = () => {
  if (mandiList) {
    return mandiList;
  }

  mandiList = loadFromLocalStorage('mandilist');

  return mandiList;
};

export const saveMandiList = (mandis = []) => {
  mandiList = mandis;
  localStorage.setItem('mandilist', JSON.stringify(mandis));
};

export const getSavedSatelliteId = () => {
  if (satelliteId) {
    return satelliteId;
  }

  satelliteId = loadFromLocalStorage('satelliteId');

  return satelliteId;
};

export const saveSatelliteId = id => {
  localStorage.setItem('satelliteId', id);
};

export const getSavedSatellites = () => {
  if (satelliteList) {
    return satelliteList;
  }

  satelliteList = loadFromLocalStorage('satelliteList');

  return satelliteList;
};

export const saveSatelliteList = satellite => {
  satelliteList = satellite;
  localStorage.setItem('satelliteList', JSON.stringify(satellite));
};

export const getSavedMandiConfig = () => {
  if (mandiConfig) {
    return mandiConfig;
  }
  mandiConfig = loadFromLocalStorage('mandiConfig');

  return mandiConfig;
};

export const saveMandiConfig = (config = []) => {
  mandiConfig = config;
  localStorage.setItem('mandiConfig', JSON.stringify(config));
};

export const getSavedAuctionDate = () => {
  if (auctionDate) {
    return auctionDate;
  }

  auctionDate = loadFromLocalStorage('auctionDate');

  return auctionDate;
};

export const saveAuctionDate = date => {
  auctionDate = date;
  localStorage.setItem('auctionDate', JSON.stringify(date));
};

export const getSavedPermissions = () => {
  if (userPermission) {
    return userPermission;
  }
  userPermission = loadFromLocalStorage('userpermission');

  return userPermission;
};
