export const ROUTES = [
  {
    title: 'Registration',
    url: '/app/registration',
    id: 'REGISTRATION_PAGE',
    description: '',
  },
  {
    title: 'Gate In',
    id: 'GATE_IN',
    url: '/app/gate-in/',
    description: '',
    subRoute: true,
    children: [
      {
        label: 'Main Mandi',
        url: 'main_mandi',
        id: 'GATE_IN',
      },
      {
        label: 'Satellite',
        url: 'satellite',
        id: 'GATE_IN',
      },
    ],
  },
  {
    title: 'Unloading',
    id: 'UNLOADING',
    url: '/app/unloading',
    description: '',
  },
  {
    title: 'Grading',
    id: 'REGRADE',
    url: '/app/regrade',
    description: '',
  },
  {
    title: 'Weighment',
    id: 'WEIGHMENT',
    url: '/app/weighment',
    description: '',
  },
  {
    title: 'Auction (New)',
    id: 'AUCTION_GRADING',
    url: '/app/auction-grading',
    description: '',
  },
  {
    title: 'Auction',
    id: 'AUCTION_PAGE',
    url: '/app/auction',
    description: '',
  },
  {
    title: 'Tokens',
    id: 'TOKEN_PAGE',
    url: '/app/token',
    description: '',
  },
  {
    title: 'Cash Advance',
    id: 'CASH_ADVANCE_PAGE',
    url: '/app/cash-advance',
    description: '',
  },
  {
    title: 'Sales',
    id: 'SALES_PAGE',
    url: '/app/sales',
    description: '',
  },
  {
    title: 'Partner List',
    id: 'PARTNER_LIST',
    url: '/app/registration/partner-list',
    description: '',
  },
  {
    title: 'Material Delivery',
    id: 'MATERIAL_DELIVERY',
    url: '/app/materialDelivery',
    description: '',
  },
  {
    title: 'Sequencing',
    id: 'SEQUENCING',
    url: '/app/sequencing',
    description: '',
  },
  {
    title: 'Catalogue',
    id: 'CATALOGUE',
    url: '/app/catalogue',
    description: '',
  },
  {
    title: 'Bidder Access',
    id: 'BIDDER_ACCESS_CONTROL',
    url: '/app/bidder',
    description: '',
  },
  {
    title: 'Trip',
    id: 'TRIP',
    url: '/app/gateinForTrips',
    description: '',
  },
  {
    title: 'Payment Request',
    id: 'PAYMENT_REQUEST',
    url: '/app/payment',
    description: '',
  },
  {
    title: 'Temp. Limit Booster Request',
    id: 'DCL_REQUEST',
    url: '/app/dclRequest',
    description: '',
  },
  {
    title: 'Truck Parking Fee',
    id: 'PARKING_FEE',
    url: '/app/parking-fee/',
    description: '',
    subRoute: true,
    childrenDropdown: true,
    children: [
      {
        label: 'Record Entry',
        url: 'record_entry',
        id: 'PARKING_FEE',
      },
      {
        label: 'Past Transaction',
        url: 'past_transaction',
        id: 'PARKING_FEE',
      },
    ],
  },
  {
    title: 'Slot Booking',
    id: 'SLOT_BOOKING',
    url: '/app/slot_booking',
    description: '',
  },
  // {
  //   title: 'Rating Based QR',
  //   id: 'RATING_BASED_QR',
  //   url: '/app/rating_based_qr',
  //   description: ''
  // }
];
