export const ROUTES = [
  {
    title: 'Registration',
    url: '/app/registration',
    id: 'REGISTRATION_PAGE',
    icon: 'registration',
  },
  {
    title: 'Gate In',
    id: 'GATE_IN',
    url: '/app/gate-in/',
    icon: 'gatein',
    subRoute: true,
    children: [
      {
        label: 'Main Mandi',
        url: 'main_mandi',
        id: 'GATE_IN',
      },
      {
        label: 'Satellite',
        url: 'satellite',
        id: 'GATE_IN',
      },
    ],
  },
  {
    title: 'Auction',
    id: 'AUCTION_GRADING',
    url: '/app/auction-grading',
    icon: 'auction',
  },
  {
    title: 'Tokens',
    id: 'TOKEN_PAGE',
    url: '/app/token',
    icon: 'tokens',
  },
  // External URL cards that open in new tabs
  {
    title: 'Sales',
    id: 'SALES_EXTERNAL',
    url: 'app/sales',
    icon: 'sales',
    external: true,
  },
  {
    title: 'Partner List',
    id: 'PARTNER_LIST_EXTERNAL',
    url: 'app/registration/partner-list',
    icon: 'partnerlist',
    external: true,
  },
  {
    title: 'Trips',
    id: 'TRIP_EXTERNAL',
    url: 'app/gateinForTrips',
    icon: 'trip',
    external: true,
  },
  {
    title: 'Payment Request',
    id: 'PAYMENT_REQUEST_EXTERNAL',
    url: 'app/payment',
    icon: 'paymentrequest',
    external: true,
  },
  // {
  //   title: 'Rating Based QR',
  //   id: 'RATING_BASED_QR',
  //   url: '/app/rating_based_qr',
  //   description: ''
  // }
];
