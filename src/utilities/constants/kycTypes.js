const MSME_DOCUMENT = {
  MSME_CERTIFICATE: 'MSME Certificate',
  NON_MSME_DECLARATION: 'Non-MSME Declaration',
};

const ADDRESS_PROOF = {
  AADHAR: '<PERSON><PERSON><PERSON> (Front & Back)',
  DL: 'Driving License',
  VOTERID: 'Voter ID',
  GST_CERT: 'GST Certificate',
  FSSAI_CERT: 'FSSAI Certificate',
  OTHERS: 'Others',
};

export const KYC_TYPE = {
  MSME_DOCUMENT,
  ADDRESS_PROOF,
  ALL_MSME_DOCUMENTS: Object.values(MSME_DOCUMENT),
  ALL_ADDRESS_PROOF: Object.values(ADDRESS_PROOF),
  ALL: [...Object.values(MSME_DOCUMENT), ...Object.values(ADDRESS_PROOF)],
};

export const KYC_CATEGORY = {
  PAN: 'Pan Card',
  MSME_DOCUMENT: 'MSME Document',
  ADDRESS_PROOF: 'Address Proof',
  FARMER_DECLARATION: 'Farmer Declaration',
  TRANSPORTER_DECLARATION: 'Transporter Declaration',
  BANK_PASSBOOK: 'Bank Statement/Passbook',
};

export const DOCUMENT_STATUS = {
  VERIFIED: 'Verified',
  UNVERIFIED: 'Unverified',
  NOT_UPLOADED: 'Not Uploaded',
  REJECTED: 'Rejected',
  PENDING: 'Pending',
};

export const FLEET_SIZE = [
  { value: 1, text: 'More than 10 vehicles' },
  { value: 2, text: 'Less than 10 vehicles' },
  { value: 3, text: 'TDS Exempt Certified' },
];
