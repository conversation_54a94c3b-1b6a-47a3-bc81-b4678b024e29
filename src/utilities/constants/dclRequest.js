// import { APPROVED, EXPIRED, PENDING, REJECTED } from 'Pages/DclRequest/columns';

export const CASH_AND_CARRY = 'Cash & Carry';

export const DCL_TABS_LIST = [
  { value: 'pending_credit_head_approval', label: 'Pending' },
  { value: 'approved', label: 'Approved' },
  { value: 'rejected', label: 'Rejected' },
  { value: 'expired', label: 'Expired' },
];

// export const TAB_VALUES = [
//   { pending_credit_head_approval: PENDING },
//   { approved: APPROVED },
//   { rejected: REJECTED },
//   { expired: EXPIRED }
// ];

export const DCL_STATUS = {
  PENDING: 'pending_credit_head_approval',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  EXPIRED: 'expired',
};
export const STATUS_TYPE = {
  ACCEPTANCE: 'Acceptance',
  REJECTION: 'Rejection',
};

export const STATUS = {
  APPROVED: 'Approved',
  REJECTED: 'Rejected',
};

export const NAME_VALUES = {
  APPROVE: 'Approve',
  REJECT: 'Reject',
};

export const TABLE_LABEL = {
  TOTAL_OUTSTANDING: 'Total outstanding',
  CREDIT_LIMIT: 'Credit Limit',
  AVAILABLE_CREDIT_LIMIT: 'Available Credit Limit',
  PAYMENT_TERMS: 'Payment Terms',
  OVERDUE_DAYS: 'Overdue Days',
};

export const DCL_TYPE_TEXT = [
  { value: 1, text: 'Payment terms breach' },
  { value: 2, text: 'Credit limit and payment terms breach' },
];

export const getTextByValue = value => {
  const item = DCL_TYPE_TEXT.find(item => item.value === value);
  return item ? item.text : null;
};
