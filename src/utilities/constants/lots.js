import {
  AUCTION_READY,
  CANCELLED,
  COLUMNS,
  IN_SATELLITE_MANDI,
  PAID,
  PR_APPROVED,
  PR_RAISED,
  SCHEDULED_TO_PAY,
  SELF_GATEIN,
  SOLD,
} from 'Pages/TokenPage/tokenColumns';

export const STATUS_LIST = [
  { value: 'To_Be_Graded', label: 'To be graded' },
  { value: 'Auction_Ready', label: 'Auction Ready' },
  { value: 'Sold', label: 'Sold' },
];

export const STATUS = {
  IN_SATELLITE_MANDI: 'In_Satellite_Mandi',
  TO_BE_GRADED: 'To_Be_Graded',
  AUCTION_READY: 'Auction_Ready',
  SOLD: 'Sold',
};

export const DELIVERY_STATUS = {
  TO_DO: 'To_Do',
  DELIVERED: 'Delivered',
};

export const DELIVERY = {
  DELIVERED: 'Delivered',
  NOT_DELIVERED: 'NotDelivered',
};

export const DELIVERY_STATUS_LIST = [
  { value: 'To_Do', label: 'Todo' },
  { value: 'Delivered', label: 'Delivered' },
];

export const TOKEN_STATUS_LIST = [
  // { value: 'In_Satellite_Mandi', label: 'In Satellite Mandi' },
  // { value: 'To_Be_Graded', label: 'To be Graded/Weighed' },
  { value: 'Auction_Ready', label: 'Auction Ready' },
  { value: 'Sold', label: 'Sold' },
  { value: 'PR_Raised', label: 'PR Raised' },
  { value: 'PR_Approved', label: 'PR APPROVED' },
  { value: 'finance_approved', label: 'Finance APPROVED' },
  { value: 'scheduled_to_pay', label: 'Scheduled To Pay' },
  { value: 'PR_Paid', label: 'Paid' },
  { value: 'cancelled', label: 'cancelled' },
  { value: 'self_sold', label: 'Sold - self gatein' },
];

export const TAB_VALUES = [
  { In_Satellite_Mandi: IN_SATELLITE_MANDI },
  { To_Be_Graded: COLUMNS },
  { Auction_Ready: AUCTION_READY },
  { Sold: SOLD },
  { PR_Raised: PR_RAISED },
  { PR_Paid: PAID },
  { PR_Approved: PR_APPROVED },
  { finance_approved: PR_APPROVED },
  { scheduled_to_pay: SCHEDULED_TO_PAY },
  { cancelled: CANCELLED },
  { self_sold: SELF_GATEIN },
];

export const TOKEN_TABS = {
  IN_SATELLITE_MANDI: 'In_Satellite_Mandi',
  TO_BE_GRADED: 'To_Be_Graded',
  AUCTION_READY: 'Auction_Ready',
  SOLD: 'Sold',
  PR_RAISED: 'PR_Raised',
  PR_APPROVED: 'PR_Approved',
  FINANCE_APPROVED: 'finance_approved',
  SCHEDULED_TO_PAY: 'scheduled_to_pay',
  PAID: 'PR_Paid',
  CANCELLED: 'cancelled',
  SELF_SOLD: 'self_sold',
};

export const SIZE_ITEM_STRUCTURE = {
  id: null,
  units: null,
  sku_id: null,
};

export const ITEM_DATA_STRUCTURE = {
  product_id: null,
  grade: null,
  farmer_marka: null,
  mandi_pack_id: null,
  mandi_number: null,
  items: [SIZE_ITEM_STRUCTURE],
};

export const FARMER_DATA_STRUCTURE = {
  items: [ITEM_DATA_STRUCTURE],
  farmer: null,
  token: null,
  status: STATUS.TO_BE_GRADED,
};

export const LANGUAGE_OPTIONS = [
  {
    text: 'English',
    value: 1,
    key: 'en',
  },
  {
    text: 'Hindi',
    value: 2,
    key: 'hi',
  },
  {
    text: 'Telugu',
    value: 3,
    key: 'te',
  },
  {
    text: 'Tamil',
    value: 4,
    key: 'ta',
  },
  {
    text: 'Kannada',
    value: 5,
    key: 'kn',
  },
  {
    text: 'Marathi',
    value: 6,
    key: 'mr',
  },
  {
    text: 'Gujarati',
    value: 7,
    key: 'gu',
  },
  {
    text: 'Bengali',
    value: 8,
    key: 'bn',
  },
  {
    text: 'Malayalam',
    value: 9,
    key: 'ml',
  },
  {
    text: 'Assamese',
    value: 10,
    key: 'as',
  },
  {
    text: 'Bodo',
    value: 11,
    key: 'bo',
  },
  {
    text: 'Dogri',
    value: 12,
    key: 'doi',
  },
  {
    text: 'Kashmiri',
    value: 13,
    key: 'ks',
  },
  {
    text: 'Konkani',
    value: 14,
    key: 'kok',
  },
  {
    text: 'Maithili',
    value: 15,
    key: 'mai',
  },
  {
    text: 'Manipuri',
    value: 16,
    key: 'mni',
  },
  {
    text: 'Nepali',
    value: 17,
    key: 'ne',
  },
  {
    text: 'Oriya',
    value: 18,
    key: 'or',
  },
  {
    text: 'Punjabi',
    value: 19,
    key: 'pa',
  },
  {
    text: 'Sanskrit',
    value: 20,
    key: 'sa',
  },
  {
    text: 'Santhali',
    value: 21,
    key: 'sat',
  },
  {
    text: 'Sindhi',
    value: 22,
    key: 'sd',
  },
  {
    text: 'Urdu',
    value: 23,
    key: 'ur',
  },
];

export const GATEIN_TYPE = {
  SELF: 3,
  FARMER: 2,
};

export const MANDI_TYPE = {
  SATELLITE: 'satellite',
  MAIN_MANDI: 'main_mandi',
};

// Grade mapping from API grade format to PrintTokenSlip format
export const GRADE_MAPPING = {
  'EL (80)': 'EL_80',
  'L (100)': 'L_100', 
  'M (125)': 'M_125',
  'S (150)': 'S_150',
  'ES (175)': 'ES_175',
  'EES (200)': 'EES_200',
  'Pitoo (310)': 'Pitto_310', 
  'Mix (6L)': 'Mix_6L',
  'Mix (7L)': 'Mix_7L',
  'Mix Garde': 'Mix', 
};

export const extractGradeType = (gradeString) => {
  if (!gradeString) return null;
  
  const gradePart = gradeString.replace(/^[A-Z]-/, '');
  
  return GRADE_MAPPING[gradePart] || null;
};
