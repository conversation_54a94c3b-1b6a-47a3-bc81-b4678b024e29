import {
  APPROVED,
  IN_PROCESS,
  PAID,
  PAID_NOT_VERIFIED,
  PENDING,
  REJECTED,
} from 'Pages/PaymentRequest/Columns.jsx';

export const PR_STATUS = {
  PENDING: 1,
  APPROVED: 2,
  REJECTED: 3,
  PAID: 4,
  IN_PROCESS: 5,
  PAID_AND_NOT_VERIFIED: 6,
  VERIFIED: 7,
};

export const PAYMENT_REQUEST_TYPE = {
  ADVANCE: 1,
  BILL: 2,
};

export const PAYMENT_STATUS_LIST = [
  { value: 1, label: 'Pending' },
  { value: 2, label: 'Approved' },
  { value: 5, label: 'In Process' },
  { value: 6, label: 'Paid and not verified' },
  { value: 4, label: 'Paid' },
  { value: 3, label: 'Rejected' },
];

export const TAB_VALUES = [
  { 1: PENDING },
  { 2: APPROVED },
  { 8: APPROVED },
  { 5: IN_PROCESS },
  { 6: PAID_NOT_VERIFIED },
  { 4: PAID },
  { 3: REJECTED },
];

export const STATUS = {
  PENDING: 'pending',
  APPROVED: 'approved',
  IN_PROCESS: 'in_process',
  PAID_NOT_VERIFIED: 'paid_not_verified',
  PAID: 'paid',
  REJECTED: 'rejected',
};

export const PR_STATUS_LABEL = {
  PENDING: 'Pending',
  APPROVED: 'Approved',
  REJECTED: 'Rejected',
  PAID_AND_NOT_VERIFIED: 'Paid and Not Verified',
  PAID: 'Paid',
  IN_PROCESS: 'In Process',
  VERIFIED: 'Verified',
};

export const PR_PAYMENT_METHOD = {
  PAY_BY_CASH: 'pay-by-cash',
  NORMAL_PR: 'normal-pr',
};

export const PAYMENT_MODE = { CASH: '2', NORMAL_PR: '1' };

export const PRIORITY = {
  HIGH: 1,
  LOW: 3,
};
