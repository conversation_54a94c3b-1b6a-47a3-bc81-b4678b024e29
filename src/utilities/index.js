/* eslint-disable no-return-assign */
/* eslint-disable react-hooks/rules-of-hooks */
/*
 * generate list of object {text, value} for selects component
 */
import { useLocation } from 'react-router-dom';

import { updatePaymentRequest } from 'Services/payments';
import { STANDARD_PACKAGING_WEIGHT } from 'Utilities/constants';

const MANDI_MIX = 'mandimix';

export const getTextValueFromList = (list = [], key, val) => {
  if (!(list && list.length)) {
    return [];
  }
  return list.map(l => ({
    text: key ? l[key] : l,
    value: val ? l[val] : l,
  }));
};

/*
 * get image local path
 */
export const getImageLocalPath = file =>
  (window.URL || window.webkitURL).createObjectURL(file);

/*
 * formik validation function merging
 */
export const mergeValidator =
  (...validator) =>
  value => {
    for (let i = 0; i < validator.length; i += 1) {
      const res = !!validator[i] && validator[i](value);
      if (res) {
        return res;
      }
    }
    return null;
  };
export const processPermission = permissions => {
  const formattedPermissions = {};
  permissions.forEach(obj => {
    // eslint-disable-next-line prefer-destructuring
    formattedPermissions[obj.resource] = (obj.access || []).map(a => a[0] || a);
  });
  return formattedPermissions;
};

export const checkForCondition = (conditionKeys, conditions, data) => {
  conditionKeys.some(key => {
    if (data[key] === conditions[key]) {
      if (Object.keys(conditions[key]).length > 0) {
        return checkForCondition(
          Object.keys(conditions[key]),
          conditions[key],
          data[key]
        );
      }
      return true;
    }
    return false;
  });
};

export const checkPermissions = ({ permissions, resource, action, data }) => {
  if (!permissions) {
    return false;
  }
  let isAllowed = false;
  const permissionObj = permissions['*']
    ? permissions['*']
    : permissions[resource];

  const isAllActionObj = permissionObj.find(o => o.action === '*');
  const actionObj =
    isAllActionObj || permissionObj.find(p => p.action === action);
  isAllowed = !!actionObj;

  if (actionObj?.conditions && data) {
    const conditionKeys = Object.keys(actionObj.conditions);
    if (conditionKeys.length > 0) {
      isAllowed = checkForCondition(conditionKeys, actionObj.conditions, data);
    }
  }
  return { isAllowed, conditions: actionObj?.conditions || null };
};

export const priceFormat = x =>
  x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');

export const toFixedNumber = (num = 0, digit = 2) =>
  //! Added this line to fix javascript calculation issue
  +Number(num).toFixed(digit);

export const saveAttachments = (bill, bill_number, id) => {
  const fd = new FormData();
  let isUpload = false;
  if (bill?.length > 0) {
    bill?.forEach(obj => {
      fd.append('payment_request[bill]', obj);
      bill_number && fd.append('payment_request[bill_number]', bill_number);
    });
    isUpload = true;
  }

  if (isUpload) {
    return updatePaymentRequest(fd, id);
  }

  return Promise.resolve();
};
export const skipWeightmentValidation = () => {
  const params = useLocation()?.search;
  return new URLSearchParams(params)?.has('skip_weighment_validation');
};

export const validateMandiMix = key => {
  if (!key) return false;
  return key.split(' ').join('').toLowerCase() === MANDI_MIX;
};

export const scrollToView = (element, className) => {
  const el = element || document.querySelector(className);
  el.scrollIntoView(
    {
      behavior: 'smooth',
    },
    500
  );
};

export const filterOptions = (options = [], state = {}) =>
  options.reduce((acc, item) => {
    if (
      item?.name
        .replace(',', '')
        .toLowerCase()
        .includes(state?.inputValue?.toLowerCase()) ||
      item?.phone_number.includes(state?.inputValue)
    ) {
      acc.push(item);
    }
    return acc;
  }, []);

//! this functions takes a string as input and converts the first character of every word to uppercase
//! and returns the rest of the characters as it is
export const capitalize = (str = '') =>
  str
    .split(' ')
    .map(ele => ele.charAt(0).toUpperCase() + ele.slice(1))
    .join(' ');

export const notValidNetWeight = (values = {}) =>
  values?.output_lots?.some(
    ({ gross_weight_in_kgs = 0, units }) =>
      gross_weight_in_kgs > 0 &&
      gross_weight_in_kgs - STANDARD_PACKAGING_WEIGHT * units <= 0
  );

export const trimString = (str = '', len) => {
  if (str?.length > len) {
    str = str.slice(0, len);
    str += '...';
  }
  return str;
};

export const getBrandLogo = (mandiList = [], mandiId) => {
  for (const mandi of mandiList) {
    if (mandi.id === mandiId) {
      return mandi.brand_logo;
    }
  }
  return '';
};

export const numberMasking = (number, startCount = 1, endCount = 2) => {
  const strNumber = String(number);
  const start = strNumber.slice(0, startCount);
  const last = strNumber.slice(-endCount);
  const mask = strNumber.slice(startCount, -endCount).replace(/./g, 'X');

  const maskNum = `${start}${mask}${last}`;
  return maskNum;
};
export const getSortedSkus = (skus, id) =>
  skus?.filter(
    ({ grade = '', product_id = '' }) =>
      !validateMandiMix(grade) && product_id === id
  );

// format the dc list so that it can be used in a FieldSelect
export const formatDcList = (items = []) =>
  items.map(item => {
    const { name, id } = item;
    return { text: name, value: id };
  });

export const getTotalFieldValue = (data, key) =>
  data?.reduce((acc, cur) => (acc += +cur[key]), 0);

export const findWordInUrl = (url, word) => {
  const parts = url.split('/');
  return parts.includes(word);
};

export const convertSecondsToMinSec = seconds => {
  const min = Math.floor(seconds / 60);
  const sec = seconds % 60;
  return { minutes: min, seconds: sec };
};
