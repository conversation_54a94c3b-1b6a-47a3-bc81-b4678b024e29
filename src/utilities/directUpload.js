import { DirectUpload } from 'activestorage';
import imageCompression from 'browser-image-compression';

import { getUserData } from 'Utilities/localStorage';

import API from '../config/envConfig';

const FILE_SIZE = 250000; //! 250kb

export default function imageUpload(
  file,
  service = 'defaultUrl',
  compressRequired = true,
  props = false,
  field = false,
  previewField = false,
  compressionOptions = {}
) {
  const serviceUrl = {
    mandi: `${API.mandiService}/directupload`,
    gatein: `${API.gateInService}/directupload`,
    defaultUrl: `${API.CRMUrl}/rails/active_storage/direct_uploads`,
  };
  const url = serviceUrl[service];

  const user = getUserData();

  const uploadFile = (file, resolve, reject) => {
    if (props) {
      props.change(previewField, 'random');
    }

    const upload = new DirectUpload(file, url, {
      directUploadWillCreateBlobWithXHR: xhr => {
        //! Put my JWT token in the auth header here
        xhr.setRequestHeader(
          'Authorization',
          `Bearer ${user?.authentication_token}`
        );
        //! Send progress upload updates
        //! xhr.upload.addEventListener('progress', event => this.directUploadProgress(event));
      },
    });
    return upload.create((error, blob) => {
      if (error) {
        reject({ error });
      } else {
        if (props) {
          props.change(field, blob.signed_id);
          props.change(previewField, blob.service_url);
        }
        resolve({ data: { ...blob, link: blob.service_url } });
      }
    });
  };

  if (!file?.type) {
    return Promise.resolve();
  }
  return new Promise((resolve, reject) => {
    if (
      compressRequired &&
      file.type !== 'application/pdf' &&
      file.size > FILE_SIZE
    ) {
      const imgCompressOptions = {
        maxSizeMB: 0.25,
        useWebWorker: true,
        maxIteration: 30,
        maxWidthOrHeight: 1920,
        ...compressionOptions,
      };
      imageCompression(file, imgCompressOptions).then(compressedFile => {
        uploadFile(compressedFile, resolve, reject);
      });
    } else {
      uploadFile(file, resolve, reject);
    }
  });
}
