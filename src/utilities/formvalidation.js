import axios from 'axios';
import { debounce } from 'lodash';

export const validateRequired = value => {
  if (!(value || value?.toString()?.length)) {
    return 'Required';
  }
  return undefined;
};

export const validateIfsc = debounce(async ifsc => {
  let ifscVerified;
  try {
    ifscVerified = await axios.get(`https://ifsc.razorpay.com/${ifsc}`);
  } catch (e) {
    ifscVerified = undefined;
  }
  return ifscVerified ? undefined : 'IFSC is not valid';
}, 300);

export const validateEquals = equalVal => value => {
  if (equalVal && value && value !== equalVal) {
    return `value should be equal to ${equalVal}`;
  }
  return undefined;
};

export const validatePositiveInt = value => {
  if (value) {
    const urlRegEx = /^[1-9]\d*$/;
    return urlRegEx.test(value)
      ? undefined
      : 'value should be a positive integer';
  }
  return undefined;
};

export const validateUrl = value => {
  if (value) {
    const urlRegEx = /^(?:\w+:)?\/\/([^\s\\.]+\.\S{2}|localhost[\\:?\d]*)\S*$/;
    return urlRegEx.test(value) ? undefined : 'Url is not valid';
  }
  return undefined;
};

export const validateMax = maxVal => value => {
  if (maxVal && (value || value === 0) && value >= maxVal) {
    return `value should be less than ${maxVal}`;
  }
  return undefined;
};

export const validateMin = minVal => value => {
  if (!isNaN(minVal) && (value || value === 0) && value <= minVal) {
    return `Value should be greater than ${minVal}`;
  }
  return undefined;
};

export const validatePhone = value => {
  if (value) {
    if (value.length !== 10) {
      return 'Phone number must be 10 digit';
    }

    const phoneRegex = /^\d*(?:\.\d{1,2})?$/;
    return phoneRegex.test(value) ? undefined : 'Phone number is not valid';
  }
  return undefined;
};

export const validateMaxOrEquals = maxVal => value => {
  if (maxVal && value && value > maxVal) {
    return `value should be less than ${maxVal}`;
  }
  return undefined;
};

export const validVehicleNumber = value => {
  if (value) {
    const pattern1 =
      /^[A-Za-z]{2}[-]{0,1}[0-9]{2}[-]{0,1}[A-Za-z]{1,3}[-]{0,1}[0-9]{1,4}$/;
    const pattern2 = '/^[A-Za-z]{2}[]{0,1}[0-9]{1,6}$/';
    return pattern1.test(value) || pattern2.test(value)
      ? undefined
      : 'Vehicle number is must be in format Ex: TS07EX8889 OR **********';
  }

  return undefined;
};

export const validateTransportationCost = value => {
  if (!value || value <= 0) {
    return 'Please enter Actual Transportation Cost';
  }
  return undefined;
};

//! File upload validation
//! Handles case when file is selected and removed
export const validateFileUpload = (value = []) => {
  if (!Array.isArray(value) || !value?.length) {
    return 'Required';
  }
  return undefined;
};

export const minMaxCharacter = (value, min = 20, max = 100) => {
  const inputValue = value?.split(' ')?.join('')?.length;
  if (!inputValue) {
    return 'Required';
  }
  if (inputValue < min || inputValue > max) {
    return `Minimum characters should be ${min} and Maximum should be ${max}`;
  }
  return undefined;
};

export const maxFloatAllowed = (value, allowed = 2) => {
  const [integer, float = ''] = String(value).split('.');
  if (integer && float.length > 2) {
    return 'Two decimal places allowed';
  }
  return undefined;
};

export const notNegativeMaxTwoDecimalPlaces = number => {
  if (number && number >= 0 && /^\d+(\.\d{1,2})?$/.test(number)) {
    return undefined;
  }
  return 'value should be a positive integer and till two decimal palces';
};

export const validateLanguage = value => {
  if (!value) {
    return 'Please select language to get registration code';
  }
  return undefined;
};

export const validateName = value => {
  const regex = /^(?=.*[a-zA-Z])[\w\W]+$/;
  if (!(value || value?.toString()?.length)) {
    return 'Required';
  }
  if (!regex.test(value)) {
    return 'Invalid Name';
  }
  return undefined;
};
