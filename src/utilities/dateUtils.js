import addDays from 'date-fns/addDays';
import addMonths from 'date-fns/addMonths';
import endOfDay from 'date-fns/endOfDay';
import endOfMonth from 'date-fns/endOfMonth';
import format from 'date-fns/format';
import getTime from 'date-fns/getTime';
import getYear from 'date-fns/getYear';
import intervalToDuration from 'date-fns/intervalToDuration';
import isPast from 'date-fns/isPast';
import isSameDay from 'date-fns/isSameDay';
import isValid from 'date-fns/isValid';
import startOfDay from 'date-fns/startOfDay';
import startOfMonth from 'date-fns/startOfMonth';
import dayjs from 'dayjs';

export const MONTHS = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

export const convertUTCDateToLocalDate = date => {
  const dateObj = new Date(date);
  const utcDate = new Date(`${dateObj.toString()} UTC`);
  return utcDate.getTime();
};

export const getDateTimeStamp = getTime;

export const isSameDayDates = (date1, date2) => isSameDay(date1, date2);

export const isPastDate = date => isPast(date);

export const addDaysToDate = (dateTime, days) => addDays(dateTime, days);
export function getFormattedDateTimeWithSecond(dateTime) {
  return isValid(dateTime) ? format(dateTime, 'dd MMM yyyy, hh:mm:ss a') : '';
}

export function getFormattedDate(dateTime) {
  return isValid(dateTime) ? format(dateTime, 'dd MM yyyy') : '';
}

export function getStandardDateFormat(dateTime) {
  return isValid(dateTime) ? format(dateTime, 'yyyy-MM-dd') : '';
}
export function getDate(dateTime) {
  return isValid(dateTime) ? format(dateTime, 'dd/MM/yyyy') : '';
}

export function getDateFormated(dateTime) {
  return isValid(dateTime) ? format(dateTime, 'dd MMM, hh:mma') : '';
}

export function getDateMonthFormat(dateTime) {
  return isValid(dateTime) ? format(dateTime, 'dd MMM') : '';
}

export function getMonthYearFormat(dateTime) {
  return isValid(dateTime) ? format(dateTime, 'MMM-yyyy') : '';
}

export function getFormattedDateTime(dateTime) {
  return isValid(dateTime) ? format(dateTime, 'dd MMM yyyy, hh:mm a') : '';
}

export function getCurrentDate() {
  return getFormattedDate(new Date());
}

export function getCurrentDateTimestamp() {
  return getDateTimeStamp(new Date());
}

export function getFormattedTime(dateTime) {
  return isValid(dateTime) ? format(dateTime, 'h:mm a') : '';
}

export function getCurrentNextMonthDate() {
  return addMonths(new Date(), 1);
}

export function getTodayDateTimeStamp() {
  return {
    startDate: getDateTimeStamp(startOfDay(new Date())),
    endDate: getDateTimeStamp(new Date()),
  };
}

export function getTodayDateTime() {
  return { startDate: startOfDay(new Date()), endDate: new Date() };
}
export function getStartDayTimestamp(date) {
  return getDateTimeStamp(startOfDay(new Date(date)));
}
export function getEndDayTimestamp(date) {
  return getDateTimeStamp(endOfDay(new Date(date)));
}

export function getStartOfMonth(date) {
  return getDateTimeStamp(startOfMonth(new Date(date)));
}
export function getEndOfMonth(date) {
  return getDateTimeStamp(endOfMonth(new Date(date)));
}

export function getThisMonthRange() {
  return { startDate: startOfMonth(new Date()), endDate: new Date() };
}

export function getYearByDate(date) {
  return getYear(date);
}

export function getDiffInHours(start, end) {
  if (!isValid(start) || !isValid(end)) {
    return '';
  }
  return intervalToDuration({
    start,
    end,
  });
}

export function getFormattedDateTimeForCSVFile(dateTime) {
  return isValid(dateTime) ? format(dateTime, 'dd MMM yyyy hh:mm a') : '';
}

export function getStartOfToday() {
  return new Date().setHours(0, 0, 0, 0);
}

export function getStartOfPreviousDay({ date = new Date(), numOfDays = 1 }) {
  const previous = new Date(date.getTime());
  previous.setDate(date.getDate() - numOfDays);

  return new Date(previous).setHours(0, 0, 0, 0);
}

export function getHoursInMs(hours = 0) {
  if (hours <= 0) return 0;

  return hours * 60 * 60 * 1000;
}

export function getTimeSeconds(dateTime) {
  return isValid(dateTime) ? format(dateTime, 'h:mm:ss') : '';
}

export function getTimeHours(dateTime) {
  return isValid(dateTime) ? format(dateTime, 'hh:mm a') : '';
}

export function getPastSixMonths() {
  const today = new Date();
  const months = [];

  for (let i = 5; i >= 0; i--) {
    const pastMonth = new Date(today);
    pastMonth.setMonth(today.getMonth() - i);
    const monthName = new Intl.DateTimeFormat('en-US', {
      month: 'short',
    }).format(pastMonth);
    const year = pastMonth.getFullYear();
    months.push(`${monthName}-${year}`);
  }

  return months;
}

export function getFormattedDateMonth(dateTime) {
  return isValid(dateTime) ? format(dateTime, 'dd MMM, yyyy') : '';
}

export function getFormattedTime24Hours(dateTime) {
  const ISTOffset = 5.5 * 60 * 60 * 1000;
  const ISTDateTime = new Date(dateTime + ISTOffset);

  return isValid(dateTime) ? format(ISTDateTime, 'HH:mm:ss') : '';
}

export const getEndOfTomorrow = () => {
  const today = new Date();
  today.setDate(today.getDate());
  today.setHours(23, 59, 59, 999);
  return today.getTime();
};

export const getSixMonthAgoStartTime = () => {
  const sixMonthsAgo = new Date();
  sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
  sixMonthsAgo.setHours(0, 0, 0, 0);
  return sixMonthsAgo.getTime();
};

export const formatDate = (format = 'DD/MM/YYYY') => dayjs().format(format);
