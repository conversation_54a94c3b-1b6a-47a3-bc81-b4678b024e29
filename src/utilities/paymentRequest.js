export const calFlatCharges = ({ mandi_flat_charge = [] }) => {
  return mandi_flat_charge?.reduce((total, { charge_value = 0 }) => {
    total += +charge_value;
    return total;
  }, 0);
};

export const mandiCalFlatCharges = ({ flat_charges = [] }) => {
  return flat_charges?.reduce((total, { charge_value = 0 }) => {
    total += +charge_value;
    return total;
  }, 0);
};

export const getFarmerOtherCharges = (farmer_other_charges = []) => {
  return farmer_other_charges?.reduce(
    (acc, { charge_value = 0, discount = 0 }) => acc + charge_value - discount,
    0
  );
};
export const getPromoDiscount = (farmer_token_discounts = []) =>
  farmer_token_discounts?.reduce(
    (acc, { discount_value = 0 }) => acc + discount_value,
    0
  );
