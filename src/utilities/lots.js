import { PARTNER_STATUS } from './constants/partnerStatus';

export const getPartnerStatus = (status = '') => {
  const {
    GENERATED,
    VERIFIED,
    UNVERIFIED,
    REJECTED,
    VERIFICATION_PENDING,
    FETCHED,
    KALEYRA_FAILED,
  } = PARTNER_STATUS;

  const converted_status = status.toUpperCase();

  if (!status) return 'NotUploaded';
  if (converted_status === VERIFIED) return 'Verified';
  if (converted_status === REJECTED) return 'Rejected';
  if (
    [
      GENERATED,
      UNVERIFIED,
      VERIFICATION_PENDING,
      FETCHED,
      KALEYRA_FAILED,
    ].includes(converted_status)
  )
    return 'Pending';

  return status;
};

export const isInvalidValidPriceInput = (data, maxPricePerKg) => {
  if (!maxPricePerKg) return false;
  return data.lots.some(lot => lot.selling_price > maxPricePerKg);
};

export const invalidPrice = (value, maxPricePerKg) => {
  if (!maxPricePerKg) return false;
  return value > maxPricePerKg;
};
