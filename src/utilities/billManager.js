import { INWARD_TYPE } from 'Utilities/constants';
import { roundToTwoDecimal } from 'Utilities/mathExpert';

export const getWeightIncludingChute = (
  net_weight = 0,
  isInwardTypeTruck = false,
  chute = 0
) => {
  return roundToTwoDecimal(
    net_weight * (isInwardTypeTruck ? 1 - chute / 100 : 1)
  );
};

export const getTotalAmount = (
  commission = 0,
  selling_price = 0,
  net_weight = 0,
  isInwardTypeTruck = false,
  packaging_cost_per_kg = 0,
  refund_for_trolley_lot = 0,
  markup = 0,
  chute = 0,
  units = 0,
  loading_fee = 0
) => {
  const total_weight_including_chhute = getWeightIncludingChute(
    net_weight,
    isInwardTypeTruck,
    chute
  );

  const subtotal = selling_price * total_weight_including_chhute;

  const totalMarkup = getMarkUp(
    commission,
    selling_price,
    net_weight,
    isInwardTypeTruck,
    packaging_cost_per_kg,
    chute
  );

  const addtional_markup = total_weight_including_chhute * markup;

  const discount = isInwardTypeTruck ? refund_for_trolley_lot * net_weight : 0;

  const loadingCharge = isInwardTypeTruck ? 0 : units * loading_fee;

  return roundToTwoDecimal(
    subtotal + totalMarkup + addtional_markup + loadingCharge - discount
  );
};

export const getMarkUp = (
  commission = 0,
  selling_price = 0,
  net_weight = 0,
  isInwardTypeTruck = false,
  packaging_cost_per_kg = 0,
  chute = 0
) => {
  return roundToTwoDecimal(
    (commission / 100) *
      selling_price *
      getWeightIncludingChute(net_weight, isInwardTypeTruck, chute) +
      packaging_cost_per_kg * net_weight
  );
};

export const getChargeableWeight = (net_weight, chute) => {
  return roundToTwoDecimal(((100 - chute) * net_weight) / 100);
};

export const getTotalWeightIncludingChute = (data) => {
  const sum = data?.reduce((total, { net_weight, chute, inward_type }) => {
    return (
      total +
      getWeightIncludingChute(
        net_weight,
        inward_type === INWARD_TYPE.TRUCK,
        chute
      )
    );
  }, 0);
  return roundToTwoDecimal(sum);
};

export const getSumOfSubtotal = (data) => {
  const sum = data?.reduce(
    (total, { net_weight, chute, selling_price = 1, inward_type }) => {
      return (
        total +
        selling_price *
          getWeightIncludingChute(
            net_weight,
            inward_type === INWARD_TYPE.TRUCK,
            chute
          )
      );
    },
    0
  );
  return roundToTwoDecimal(sum);
};

export const getSumOfMarkUp = (data, commission) => {
  const sum = data?.reduce(
    (
      total,
      {
        net_weight,
        chute,
        selling_price = 1,
        inward_type,
        packaging_cost_per_kg
      }
    ) => {
      return (
        total +
        getMarkUp(
          commission,
          selling_price,
          net_weight,
          inward_type === INWARD_TYPE.TRUCK,
          packaging_cost_per_kg,
          chute
        )
      );
    },
    0
  );
  return roundToTwoDecimal(sum);
};

export const getSumOfAdditionalMarkUp = (data, markup) => {
  const sum = data?.reduce((total, { net_weight, chute, inward_type }) => {
    return (
      total +
      getWeightIncludingChute(
        net_weight,
        inward_type === INWARD_TYPE.TRUCK,
        chute
      ) *
        markup
    );
  }, 0);
  return roundToTwoDecimal(sum);
};

export const getDiscountValue = (
  isInwardTypeTruck = false,
  refund_for_trolley_lot = 0,
  net_weight = 0
) => {
  return isInwardTypeTruck ? refund_for_trolley_lot * net_weight : 0;
};

export const getLoadingCharge = (
  units = 0,
  isInwardTypeTruck = false,
  loading_fee = 0
) => {
  return isInwardTypeTruck ? 0 : units * loading_fee;
};

export const getTotalLoadingCharge = (data = [], loading_fee = 0) => {
  const sum = data?.reduce((total, { units, inward_type }) => {
    return (
      total +
      getLoadingCharge(units, inward_type === INWARD_TYPE.TRUCK, loading_fee)
    );
  }, 0);
  return roundToTwoDecimal(sum);
};

export const getTotalDiscount = (data, refund_for_trolley_lot = 0) => {
  const sum = data?.reduce((total, { net_weight, inward_type }) => {
    return (
      total +
      getDiscountValue(
        inward_type === INWARD_TYPE.TRUCK,
        refund_for_trolley_lot,
        net_weight
      )
    );
  }, 0);
  return roundToTwoDecimal(sum);
};

export const getSubTotalAmount = (
  data,
  commission,
  refund_for_trolley_lot,
  markup,
  loading_fee
) => {
  const sum = data?.reduce(
    (
      total,
      {
        net_weight,
        selling_price,
        packaging_cost_per_kg,
        chute,
        units,
        inward_type
      }
    ) => {
      return (
        total +
        getTotalAmount(
          commission,
          selling_price,
          net_weight,
          inward_type === INWARD_TYPE.TRUCK,
          packaging_cost_per_kg,
          refund_for_trolley_lot,
          markup,
          chute,
          units,
          loading_fee
        )
      );
    },
    0
  );
  return roundToTwoDecimal(sum);
};
