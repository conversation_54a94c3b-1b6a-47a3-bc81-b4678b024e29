import imageDirectUpload from 'Utilities/directUpload';

const fileUpload = async (file, serviceUrl, compressRequired) => {
  if (file && file.length > 0) {
    await Promise.all(
      file?.map(value => imageDirectUpload(value, serviceUrl, compressRequired))
    ).then(res => {
      file = res
        .filter(Boolean)
        ?.map(({ data }) =>
          serviceUrl && serviceUrl !== 'defaultUrl' ? data?.id : data.signed_id
        );
    });
  }
  return new Promise(resolve => {
    resolve(file);
  });
};

export default fileUpload;

export const qualityInfoUpload = async (
  file,
  serviceUrl,
  compressRequired,
  token,
  compressionOptions = {}
) => {
  if (file && file.length > 0) {
    await Promise.all(
      file?.map((value, index) =>
        index % 4 === 0 && token
          ? imageDirectUpload(value, serviceUrl, false)
          : imageDirectUpload(
              value,
              serviceUrl,
              compressRequired,
              false,
              false,
              false,
              compressionOptions
            )
      )
    ).then(res => {
      file = res
        .filter(Boolean)
        ?.map(({ data }) =>
          serviceUrl && serviceUrl !== 'defaultUrl' ? data?.id : data.signed_id
        );
    });
  }
  return new Promise(resolve => {
    resolve(file);
  });
};
