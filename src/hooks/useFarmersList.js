import { useRef, useState } from 'react';

import { getFarmers } from 'Services/common';

const useFarmersList = () => {
  const [farmers, setFarmers] = useState([]);
  const isApiInProgress = useRef(false);

  const getUpdatedFarmersList = query => {
    if (isApiInProgress.current) {
      clearTimeout(isApiInProgress.current);
    }

    isApiInProgress.current = setTimeout(() => {
      getFarmers({ q: query }).then(({ items = [] }) => {
        setFarmers(items);
      });
    }, 300);
  };

  return [farmers, getUpdatedFarmersList];
};

export default useFarmersList;
