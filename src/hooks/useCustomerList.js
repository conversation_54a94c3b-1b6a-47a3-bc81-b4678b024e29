import { useRef, useState } from 'react';

import { getCustomers } from 'Services/users';

const useCustomerList = (minLength = 3) => {
  const [customers, setCustomers] = useState([]);
  const inProgressApi = useRef();

  const getCustomerApiCall = (query, otherParam) => {
    if (query.length > minLength) {
      clearTimeout(inProgressApi.current);

      inProgressApi.current = setTimeout(() => {
        getCustomers({ q: query, ...otherParam }).then(({ items = [] }) => {
          setCustomers(items);
        });
      }, 200);
    }
  };
  return [customers, getCustomerApiCall];
};

export default useCustomerList;
