import { useSiteValue } from 'App/SiteContext';
import { getMandiConfig } from 'Services/common';
import { getStartOfToday } from 'Utilities/dateUtils';

const useMandiConfig = () => {
  const { mandiId, setMandiConfig } = useSiteValue();

  const setMandiData = auctionDate => {
    const defaultDate = getStartOfToday();

    if (mandiId && defaultDate !== auctionDate) {
      getMandiConfig({
        auction_date: auctionDate,
        mandi_id: mandiId,
      }).then(({ responseData }) => setMandiConfig(responseData));
    }
  };
  return setMandiData;
};

export default useMandiConfig;
