import React, { useEffect, useState } from 'react';

import {
  FormControlLabel,
  Grid,
  Radio,
  RadioGroup,
  Typography,
} from '@mui/material';
import { useFormikContext } from 'formik';
import _groupBy from 'lodash/groupBy';

import { AppButton, Modal as CustomModal, Table } from 'Components';

const COLUMNS = [
  {
    key: 'sku.grade',
    header: 'SKU',
    align: 'center',
  },
  {
    key: 'gross_weight_in_kgs',
    header: 'Gross Weight',
    align: 'center',
  },
  {
    key: 'units',
    header: 'Units',
    align: 'center',
  },
];

const GateInConfirmPopUp = ({
  confirm = true,
  toggleConfirm = () => {},
  farmer_details = [],
  loading = false,
}) => {
  const [farmerData, setFarmerData] = useState([]);
  const { handleSubmit, handleChange } = useFormikContext();

  useEffect(() => {
    const data = farmer_details.map(detail =>
      Object.values(_groupBy(detail.items, 'product.id'))
    );
    setFarmerData(data);
  }, [farmer_details]);

  const grossWeight = data =>
    data?.every(item => item?.gross_weight_in_kgs > 0);

  return (
    <CustomModal
      halfScreen
      title='Confirmation'
      open={confirm}
      onClose={toggleConfirm}
      dataCy={{ 'data-cy': 'mandi.confirmPopUp.closeModal' }}
      footerComponent={
        <AppButton
          size='small'
          loading={loading}
          variant='contained'
          color='primary'
          onClick={handleSubmit}
          data-cy='mandi.gateIn.confirmPopUp.save'
        >
          Save
        </AppButton>
      }
    >
      {farmer_details.map(({ farmer = {}, token }, index) => (
        <Grid container key={index} style={{ marginBottom: '1rem' }}>
          <Typography variant='h6'>
            {token ? `${token} - ` : ''}
            {farmer?.name}
            {farmer?.phone_number ? `(${farmer.phone_number})` : ''}
          </Typography>
          {farmerData?.[index]?.map((item, idx) => (
            <>
              <Grid key={idx} container alignItems='center'>
                <Grid item md={6} xs={12}>
                  <Typography variant='body1' style={{ fontWeight: 'bold' }}>
                    {item?.[0]?.product?.name}
                  </Typography>
                </Grid>
                <RadioGroup
                  row
                  defaultValue='To_Be_Graded'
                  name={`farmer_details.${index}.status`}
                  value={farmer_details?.[index]?.status}
                  onClick={e => {
                    handleChange(e);
                  }}
                  data-cy='mandi.gateIn.radioGroup'
                >
                  {grossWeight(item) && (
                    <FormControlLabel
                      value='Auction_Ready'
                      control={
                        <Radio
                          color='primary'
                          size='small'
                          inputProps={{
                            'data-cy': 'mandi.radiogroup.auctionReady',
                          }}
                        />
                      }
                      label='Auction Ready'
                    />
                  )}
                  <FormControlLabel
                    value='To_Be_Graded'
                    control={
                      <Radio
                        color='primary'
                        size='small'
                        inputProps={{
                          'data-cy': 'mandi.radiogroup.toBeGraded',
                        }}
                      />
                    }
                    label='Grading Required'
                  />
                </RadioGroup>
              </Grid>
              <Grid container>
                <Table
                  size='medium'
                  sticky
                  hover
                  columns={COLUMNS}
                  data={item}
                  dataKey='id'
                />
              </Grid>
            </>
          ))}
        </Grid>
      ))}
    </CustomModal>
  );
};

export default GateInConfirmPopUp;
