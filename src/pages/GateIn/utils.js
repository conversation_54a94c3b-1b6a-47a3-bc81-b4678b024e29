export const getLotsGroup = (skus = []) => {
  if (!Array.isArray(skus)) return [];

  return [...new Set(skus.map(sku => sku?.group_name))];
};

export const getSkuSizeMap = (skus = []) => {
  if (!Array.isArray(skus)) return [];

  return skus.reduce((acc, sku) => {
    if (!acc[sku?.product_id]) {
      acc[sku?.product_id] = {};
    }
    if (!acc[sku?.product_id][sku?.group_name]) {
      acc[sku?.product_id][sku?.group_name] = {};
    }
    acc[sku?.product_id][sku?.group_name][sku?.sku_size_id] = sku?.id;
    return acc;
  }, {});
};
