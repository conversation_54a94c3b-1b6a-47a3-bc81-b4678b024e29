import React, { useEffect, useState } from 'react';

import { AddCircleOutlineOutlined, CancelOutlined } from '@mui/icons-material';
import { InputAdornment } from '@mui/material';
import { useFormikContext } from 'formik';

import { useSiteValue } from 'App/SiteContext';
import { FieldCombo, FieldInput } from 'Components/FormFields';
import { validateRequired } from 'Utilities/formvalidation';

import { ActionIcons } from './styled';

const LOT_ITEM_STRUCTURE = {
  product: null,
  sku: null,
  units: null,
  gross_weight_in_kgs: null,
};

const ProductDetails = ({
  index,
  arrayHelpers,
  idx,
  skus = [],
  isGrossRequired = false,
  disabled = false,
}) => {
  const { values, setFieldValue } = useFormikContext();
  const [filteredSkus, setFilteredSkus] = useState([]);
  const { mandiConfig: { enable_pack_auction = false } = {} } = useSiteValue();

  useEffect(() => {
    setFilteredSkus(
      skus?.filter(
        sku =>
          values?.farmer_details?.[index]?.items?.[idx]?.product?.id ===
          sku.product_id
      )
    );
  }, [skus, values?.farmer_details?.[index]?.items?.[idx]?.product?.id]);

  useEffect(() => {
    if (enable_pack_auction) {
      const grade = (skus || []).find(
        ({ grade }) => grade.toLowerCase() === 'mandi mix'
      );
      setFilteredSkus(grade ? [grade] : []);
    }
  }, [
    enable_pack_auction,
    skus,
    values?.farmer_details?.[index]?.items?.[idx]?.product?.id,
  ]);

  useEffect(() => {
    if (filteredSkus.length) {
      setFieldValue(
        `farmer_details.${index}.items.${idx}.sku_id`,
        filteredSkus
      );
    } else {
      setFieldValue(`farmer_details.${index}.items.${idx}.sku_id`, null);
    }
  }, [filteredSkus]);

  return (
    <>
      <FieldCombo
        name={`farmer_details.${index}.items.${idx}.sku`}
        label='SKU'
        placeholder='SKU'
        variant='outlined'
        size='small'
        options={filteredSkus}
        optionLabel={({ grade = '' }) => grade}
        required
        validate={validateRequired}
        style={{ width: '160px' }}
        InputLabelProps={{
          required: true,
          shrink: true,
        }}
        disabled={disabled}
        inputProps={{
          'data-cy': `mandi.farmerDetails.${index}.productDetails.skuSelect.${idx}`,
        }}
      />
      <FieldInput
        type='number'
        name={`farmer_details.${index}.items.${idx}.units`}
        size='small'
        label='Units'
        placeholder='0'
        variant='outlined'
        validate={validateRequired}
        InputProps={{
          endAdornment: <InputAdornment position='end'>units</InputAdornment>,
        }}
        style={{ width: '160px' }}
        InputLabelProps={{
          shrink: true,
        }}
        disabled={disabled}
        inputProps={{
          'data-cy': `mandi.farmerDetails.${index}.productDetails.units.${idx}`,
        }}
      />
      {isGrossRequired && (
        <FieldInput
          type='number'
          name={`farmer_details.${index}.items.${idx}.gross_weight_in_kgs`}
          size='small'
          label='Gross Weight'
          placeholder='0.0'
          variant='outlined'
          InputProps={{
            endAdornment: <InputAdornment position='end'>Kgs</InputAdornment>,
          }}
          style={{ width: '160px' }}
          InputLabelProps={{
            shrink: true,
          }}
          disabled={disabled}
          inputProps={{
            'data-cy': `mandi.farmerDetails.${index}.productDetails.grossWeight.${idx}`,
          }}
        />
      )}
      <ActionIcons style={{ display: 'flex' }}>
        {values?.farmer_details?.[index]?.items.length > 1 && (
          <CancelOutlined
            fontSize='large'
            onClick={() => {
              arrayHelpers.remove(idx);
            }}
            data-cy={`mandi.farmerDetails.${index}.productDetails.removeProductDetails.${idx}`}
          />
        )}
        {values?.farmer_details?.[index]?.items.length - 1 === idx && (
          <AddCircleOutlineOutlined
            fontSize='large'
            onClick={() =>
              arrayHelpers.insert(
                values.farmer_details?.[index]?.items.length,
                LOT_ITEM_STRUCTURE
              )
            }
            data-cy='mandi.gateIn.addProductDetails'
          />
        )}
      </ActionIcons>
    </>
  );
};

export default ProductDetails;
