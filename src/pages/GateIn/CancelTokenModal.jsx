import React, { useState } from 'react';

import { Save as SaveIcon } from '@mui/icons-material';
import { Grid, TextField, Typography } from '@mui/material';
import { Formik } from 'formik';
import { useNavigate } from 'react-router-dom';

import { AppButton, Modal as CustomModal } from 'Components';
import FieldCombo from 'Components/FormFields/FieldCombo/index.jsx';
import useNotify from 'Hooks/useNotify';
import { cancelToken } from 'Services/gateIn';
import { CANCEL_REASON } from 'Utilities/constants';

import { ButtonWrapper } from './styled';

const CancelTokenModal = ({
  open,
  data,
  toggleModal,
  loadTokenListing,
  currentFilter,
}) => {
  const [loading, setLoading] = useState(false);
  const NotificationBar = useNotify();
  const navigate = useNavigate();

  const submitForm = values => {
    setLoading(true);
    const {
      farmer_token_id,
      cancellation_reason,
      comment = '',
    } = values || data;
    const cancelData = {
      farmer_token_id: data?.farmer_name
        ? data?.lots_data?.[0]?.farmer_token_id
        : farmer_token_id,
      reason: cancellation_reason?.name || data?.cancellation_reason,
      comment: data?.comment || comment,
      type: data?.farmer_name ? 'UNDO_CANCEL' : 'CANCEL',
    };
    cancelToken(cancelData)
      .then(() => {
        NotificationBar(
          data?.farmer_name
            ? 'Undo Succesfully '
            : 'Token Cancelled Succesfully'
        );
        toggleModal();
        loadTokenListing && loadTokenListing({ currentFilter });
        navigate('/app/home');
      })
      .finally(() => setLoading(false));
  };

  return (
    <CustomModal
      title={
        data?.farmer_name
          ? `Undo Cancellation (Token ${data?.token})`
          : `Cancel Token (${data?.token})`
      }
      open={open}
      contentSize
      onClose={toggleModal}
      dataCy={{ 'data-cy': 'mandi.cancelToken.closeModal' }}
    >
      <Formik initialValues={data} onSubmit={submitForm} enableReinitialize>
        {({ handleSubmit, values, handleChange }) => (
          <>
            <Grid container style={{ marginBottom: '1rem' }}>
              {data?.farmer_name && (
                <Typography variant='body1'>
                  <b>Confirm Undo Cancellation for Token {`${data?.token}`} </b>
                </Typography>
              )}
            </Grid>
            <Grid container>
              <Grid lg={4} md={4}>
                <Typography variant='body1'>
                  <b>Farmer Name:</b>
                </Typography>
              </Grid>
              <Typography variant='body1'>
                {data?.farmer?.name || data?.farmer_name}
              </Typography>
              &nbsp;
            </Grid>
            <Grid container>
              <Grid lg={4} md={4}>
                <Typography variant='body1'>
                  <b>Address:</b>
                </Typography>
              </Grid>
              <Grid item md={8}>
                <Typography variant='body1'>
                  {data?.farmer?.location?.full_address || data?.farmer_address}
                </Typography>
              </Grid>
            </Grid>
            <Grid container>
              <Grid lg={4} md={4}>
                <Typography variant='body1'>
                  <b>Mobile Number:</b>
                </Typography>
              </Grid>
              <Typography variant='body1'>
                {data?.farmer?.phone_number ||
                  data?.lots_data?.[0]?.phone_number}
              </Typography>
            </Grid>
            {!data?.farmer_name && (
              <Grid container>
                <Grid lg={4} md={4}>
                  <Typography variant='body1'>
                    <b>Reason:</b>
                  </Typography>
                </Grid>
                <Grid lg={4} md={4}>
                  <FieldCombo
                    name='cancellation_reason'
                    variant='outlined'
                    size='small'
                    options={CANCEL_REASON}
                    InputLabelProps={{
                      shrink: true,
                    }}
                    inputProps={{
                      'data-cy': 'mandi.gateIn.selectCancelReason',
                    }}
                  />
                </Grid>
              </Grid>
            )}
            {!data?.farmer_name && (
              <Grid container style={{ marginTop: '1rem' }}>
                <Grid lg={4} md={4} />
                <TextField
                  name='comment'
                  size='large'
                  label='Other Comments'
                  placeholder='Comments'
                  variant='outlined'
                  value={values?.comment}
                  onChange={handleChange}
                  multiline
                  InputLabelProps={{
                    shrink: true,
                  }}
                  inputProps={{
                    'data-cy': 'mandi.gateIn.otherComments',
                  }}
                />
              </Grid>
            )}
            <ButtonWrapper>
              <AppButton
                startIcon={<SaveIcon />}
                variant='contained'
                size='small'
                color='primary'
                loading={loading}
                onClick={handleSubmit}
                data-cy='mandi.gateIn.saveButton'
              >
                Save
              </AppButton>
            </ButtonWrapper>
          </>
        )}
      </Formik>
    </CustomModal>
  );
};

export default CancelTokenModal;
