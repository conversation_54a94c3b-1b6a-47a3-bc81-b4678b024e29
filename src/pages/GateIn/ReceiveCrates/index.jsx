import React from 'react';

import { Button, Grid, Typography } from '@mui/material';

const ReceiveCrates = ({
  pendingItemAmount,
  setOpenReceiveCratesModal,
  grayText,
  dataCy = {},
}) => (
  <Grid>
    <Button
      color='primary'
      size='small'
      variant='contained'
      style={{ marginRight: '3rem' }}
      onClick={() => setOpenReceiveCratesModal(true)}
      disabled={pendingItemAmount <= 0}
      {...dataCy}
    >
      Receive Crates
    </Button>
    <Grid item>
      <Typography variant='caption' className={grayText}>
        {pendingItemAmount} Crates Pending
      </Typography>
    </Grid>
  </Grid>
);

export default ReceiveCrates;
