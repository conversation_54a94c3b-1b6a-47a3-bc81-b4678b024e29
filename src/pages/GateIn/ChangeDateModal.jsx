import React from 'react';

import { Grid, Typography } from '@mui/material';

import { useSiteValue } from 'App/SiteContext';
import { AppButton, Modal as CustomModal } from 'Components';
import { getDateMonthFormat } from 'Utilities/dateUtils';

import useAtomicStyles from '../../theme/AtomicCss';

const ChangeDateModal = ({ newAuctionDate, onClose = () => {}, ...props }) => {
  const { auctionDate, setAuctionDate } = useSiteValue();

  const { marginRight2, marginLeft2 } = useAtomicStyles();

  const submitDate = () => {
    if (newAuctionDate) {
      setAuctionDate(newAuctionDate);
    }
    onClose();
  };

  return (
    <CustomModal
      contentSize
      onClose={onClose}
      open={newAuctionDate}
      dataCy={{ 'data-cy': 'mandi.changeDate.closeModal' }}
      footerComponent={
        <Grid container spacing={1} justifyContent='flex-end'>
          <Grid item>
            <AppButton
              size='small'
              variant='contained'
              onClick={submitDate}
              className={[marginRight2]}
              color='inherit'
              data-cy='mandi.gateIn.changeDate'
            >
              Change Date to{' '}
              <b
                style={{
                  color: '#008080',
                  paddingLeft: '0.2rem',
                }}
              >
                {` ${getDateMonthFormat(newAuctionDate)}`}
              </b>
            </AppButton>
          </Grid>
          <Grid item>
            <AppButton
              size='small'
              variant='contained'
              color='primary'
              className={[marginLeft2]}
              onClick={onClose}
              data-cy='mandi.gateIn.proceed'
            >
              Proceed
            </AppButton>
          </Grid>
        </Grid>
      }
      {...props}
    >
      <Typography>
        Current Auction Date is{' '}
        <b style={{ color: 'red' }}>{getDateMonthFormat(auctionDate)}</b>. Do
        you want to proceed?
      </Typography>
    </CustomModal>
  );
};

export default ChangeDateModal;
