import { useEffect } from 'react';

import { Add as AddIcon, Delete as DeleteIcon } from '@mui/icons-material';
import {
  Card,
  CardContent,
  Button,
  IconButton,
  Typography,
  Box,
  Grid,
  Divider,
} from '@mui/material';
import { useFormikContext } from 'formik';
import PropTypes from 'prop-types';

import { FieldInput, FieldSelect } from 'Components/FormFields';
import { validateRequired } from 'Utilities/formvalidation';

import { getLotsGroup, getSkuSizeMap } from './utils';

const AppleBoxInwardEntry = ({
  index = 0,
  products = [],
  skuSizes = [],
  skus = [],
  packTypes = [],
  disabled = false,
  naValue = 'N/A', // This will be generated by backend
}) => {
  const INITIAL_ROW = {
    id: 0,
    product_id: '',
    grade: '',
    farmer_marka: '',
    mandi_pack_id: '',
    mandi_number: '',
    items: [], // Will contain {units: '', id: sku_id}
  };
  const { values, setFieldValue } = useFormikContext();
  const lotsGroup = getLotsGroup(skus);

  const skuSizeMap = getSkuSizeMap(skus);

  // Initialize items array for a row based on skuSizes
  const initializeRowItems = (productId, group_name) => {
    return skuSizes.map(skuSize => ({
      sku_id: skuSizeMap[productId]?.[group_name]?.[skuSize.id] || skuSize.id,
      units: '',
    }));
  };

  // Map API data to form structure based on sku_id
  const mapApiDataToFormStructure = (apiItems, productId, group_name) => {
    if (!apiItems || !Array.isArray(apiItems))
      return initializeRowItems(productId, group_name);

    // Create a map of sku_id to units from API data
    const apiDataMap = {};
    apiItems.forEach(item => {
      if (item.sku_id && item.units !== undefined) {
        apiDataMap[item.sku_id] = {
          units: item.units,
          id: item.id,
        };
      }
    });

    // Map to form structure based on skuSizes order
    const mappedItems = skuSizes.map(skuSize => {
      const expectedSkuId =
        skuSizeMap[productId]?.[group_name]?.[skuSize.id] || skuSize.id;
      const apiData = apiDataMap[expectedSkuId]; // Get the data object or undefined
      const mappedItem = {
        sku_id: expectedSkuId,
        units: apiData?.units || '',
        id: apiData?.id || '',
      };

      return mappedItem;
    });

    console.log('Final mapped items:', mappedItems);
    return mappedItems;
  };

  // Get current rows from Formik values or use initial row
  const rows = values?.farmer_details?.[index]?.items || [INITIAL_ROW];

  // Calculate total boxes for a row
  const calculateTotalBoxes = (items = []) => {
    if (!Array.isArray(items)) return 0;
    return items.reduce((sum, item) => {
      if (!item || typeof item.units === 'undefined') return sum;
      return sum + (parseInt(item.units) || 0);
    }, 0);
  };

  // Add new row
  const addRow = () => {
    const newRow = {
      product_id: '',
      grade: '',
      farmer_marka: '',
      mandi_pack_id: '',
      mandi_number: '',
      items: [],
    };
    const updatedRows = [...rows, newRow];
    setFieldValue(`farmer_details.${index}.items`, updatedRows);
  };

  // Initialize items when product is selected
  const handleProductChange = (rowIndex, productId) => {
    setFieldValue(
      `farmer_details.${index}.items.${rowIndex}.product_id`,
      productId
    );
    if (productId) {
      // Clear grade and items when product changes
      setFieldValue(`farmer_details.${index}.items.${rowIndex}.grade`, '');
      setFieldValue(`farmer_details.${index}.items.${rowIndex}.items`, []);
    } else {
      setFieldValue(`farmer_details.${index}.items.${rowIndex}.items`, []);
    }
  };

  const handleGradeChange = (rowIndex, productId, grade) => {
    setFieldValue(`farmer_details.${index}.items.${rowIndex}.grade`, grade);

    if (productId && grade) {
      const initializedItems = initializeRowItems(productId, grade);
      setFieldValue(
        `farmer_details.${index}.items.${rowIndex}.items`,
        initializedItems
      );
    } else {
      setFieldValue(`farmer_details.${index}.items.${rowIndex}.items`, []);
    }
  };

  // Remove row
  const removeRow = rowIndex => {
    if (rows?.length > 1) {
      const updatedRows = rows.filter((_, idx) => idx !== rowIndex);
      setFieldValue(`farmer_details.${index}.items`, updatedRows);
    }
  };

  // Initialize formik values
  useEffect(() => {
    if (!values?.farmer_details?.[index]?.items) {
      setFieldValue(`farmer_details.${index}.items`, [INITIAL_ROW]);
    } else {
      // Ensure existing rows have properly initialized items arrays
      const currentItems = values.farmer_details[index].items;
      const updatedItems = currentItems.map(item => {
        if (item.product_id && item.grade) {
          // If items exist (from API), map them properly based on sku_id
          if (item.items && item.items.length > 0) {
            return {
              ...item,
              items: mapApiDataToFormStructure(
                item.items,
                item.product_id,
                item.grade
              ),
            };
          }
          // If no items, initialize empty structure
          return {
            ...item,
            items: initializeRowItems(item.product_id, item.grade),
          };
        }
        return item;
      });

      // Only update if there were changes
      const hasChanges = updatedItems.some(
        (item, idx) =>
          JSON.stringify(item) !== JSON.stringify(currentItems[idx])
      );

      if (hasChanges) {
        setFieldValue(`farmer_details.${index}.items`, updatedItems);
      }
    }
  }, [index, setFieldValue, values?.farmer_details, skuSizes.length]);

  return (
    <Box sx={{ mt: 2, mb: 2 }}>
      {rows?.map((row, rowIndex) => (
        <Card key={rowIndex} sx={{ mb: 2, p: 1 }}>
          <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
            {/* Basic Information */}
            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid
                item
                xs={12}
                sm={3}
                md={2}
                display='flex'
                alignItems='center'
                sx={{ minWidth: 'fit-content' }}
              >
                <Typography variant='subtitle2' sx={{ fontWeight: 'bold' }}>
                  Mandi No:
                </Typography>
                <Typography
                  sx={{
                    fontWeight: 'bold',
                    color: 'primary.main',
                    ml: 1,
                    whiteSpace: 'nowrap',
                  }}
                >
                  {row.mandi_number || 'Yet To Be Generated'}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={3} md={3.33} sx={{ flex: 1 }}>
                <FieldSelect
                  name={`farmer_details.${index}.items.${rowIndex}.product_id`}
                  label='Product'
                  options={products.map(product => ({
                    value: product.id,
                    text: product.name || product.code,
                  }))}
                  disabled={disabled && row.mandi_number}
                  validate={validateRequired}
                  size='small'
                  showNone={false}
                  InputLabelProps={{ shrink: false }}
                  onChange={e => handleProductChange(rowIndex, e.target.value)}
                  required
                  fullWidth
                  style={{
                    width: '100%',
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={3} md={3.33} sx={{ flex: 1 }}>
                <FieldSelect
                  name={`farmer_details.${index}.items.${rowIndex}.mandi_pack_id`}
                  label='Pack Type'
                  size='small'
                  fullWidth
                  disabled={disabled && row.mandi_number}
                  showNone={false}
                  options={packTypes.map(type => ({
                    value: type.id,
                    text: type.mandi_packaging_type,
                  }))}
                  InputLabelProps={{ shrink: false }}
                  validate={validateRequired}
                  required
                  style={{
                    width: '100%',
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={3} md={3.33} sx={{ flex: 1 }}>
                <FieldSelect
                  name={`farmer_details.${index}.items.${rowIndex}.grade`}
                  label='Lot'
                  size='small'
                  fullWidth
                  disabled={disabled && row.mandi_number}
                  showNone={false}
                  options={
                    lotsGroup?.map(lot => ({
                      value: lot,
                      text: lot,
                    })) || []
                  }
                  onChange={e =>
                    handleGradeChange(rowIndex, row.product_id, e.target.value)
                  }
                  InputLabelProps={{ shrink: false }}
                  validate={validateRequired}
                  required
                  style={{
                    width: '100%',
                  }}
                />
              </Grid>
            </Grid>

            <Divider sx={{ my: 2 }} />

            {/* SKU Quantities - All in single row */}
            <Typography variant='subtitle2' sx={{ mb: 1, fontWeight: 'bold' }}>
              SKU Quantities
            </Typography>
            <Box
              sx={{
                display: 'flex',
                gap: 0.5,
                my: 1,
                flexWrap: 'nowrap',
                overflowX: 'auto',
              }}
            >
              {skuSizes.map((skuSize, idx) => {
                return (
                  <Box
                    key={idx}
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      minWidth: '55px',
                      maxWidth: '55px',
                    }}
                  >
                    <Typography
                      variant='caption'
                      sx={{
                        fontSize: '0.7rem',
                        fontWeight: 'bold',
                        color: 'text.secondary',
                        textAlign: 'center',
                        lineHeight: 1,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        maxWidth: '55px',
                      }}
                    >
                      {skuSize?.size}
                    </Typography>
                    <FieldInput
                      name={`farmer_details.${index}.items.${rowIndex}.items.${idx}.units`}
                      type='number'
                      size='small'
                      disabled={!row.product_id}
                      value={row?.items?.[idx]?.units || ''}
                      onChange={e => {
                        const currentRow = rows[rowIndex];
                        console.log('SKU Input Change:', {
                          rowIndex,
                          idx,
                          value: e.target.value,
                          currentRow,
                          fieldPath: `farmer_details.${index}.items.${rowIndex}.items.${idx}.units`,
                          currentItems: currentRow?.items,
                        });

                        if (currentRow?.product_id) {
                          setFieldValue(
                            `farmer_details.${index}.items.${rowIndex}.items.${idx}.sku_id`,
                            skuSizeMap[currentRow.product_id]?.[
                              currentRow.grade
                            ]?.[skuSize.id] || skuSize.id
                          );
                        }
                        setFieldValue(
                          `farmer_details.${index}.items.${rowIndex}.items.${idx}.units`,
                          e.target.value
                        );
                      }}
                      sx={{
                        width: '55px',
                        '& .MuiInputBase-input': {
                          padding: '4px 4px',
                          textAlign: 'center',
                          fontSize: '0.8rem',
                        },
                        '& input[type=number]': {
                          MozAppearance: 'textfield',
                        },
                        '& input[type=number]::-webkit-outer-spin-button': {
                          WebkitAppearance: 'none',
                          margin: 0,
                        },
                        '& input[type=number]::-webkit-inner-spin-button': {
                          WebkitAppearance: 'none',
                          margin: 0,
                        },
                      }}
                    />
                  </Box>
                );
              })}
            </Box>

            <Divider sx={{ my: 2 }} />

            {/* Total and Actions */}
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <Typography
                variant='h6'
                sx={{ fontWeight: 'bold', color: 'primary.main' }}
              >
                Total Boxes: {calculateTotalBoxes(row?.items || [])}
              </Typography>
              <IconButton
                size='small'
                onClick={() => removeRow(rowIndex)}
                disabled={(disabled && row.mandi_number) || rows?.length === 1}
                color='error'
                sx={{ ml: 2 }}
              >
                <DeleteIcon />
              </IconButton>
            </Box>
          </CardContent>
        </Card>
      ))}

      <Box sx={{ mt: 2 }}>
        <Button
          startIcon={<AddIcon />}
          onClick={addRow}
          variant='outlined'
          size='small'
        >
          Add Another Mandi
        </Button>
      </Box>
    </Box>
  );
};

AppleBoxInwardEntry.propTypes = {
  index: PropTypes.number,
  products: PropTypes.array,
  skuSizes: PropTypes.array,
  skus: PropTypes.array,
  packTypes: PropTypes.array,
  disabled: PropTypes.bool,
  naValue: PropTypes.string,
};

export default AppleBoxInwardEntry;
