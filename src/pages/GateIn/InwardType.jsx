import React, { useEffect } from 'react';

import {
  FormControlLabel,
  Grid,
  Radio,
  RadioGroup,
  Typography,
} from '@mui/material';
import { useFormikContext } from 'formik';
import { useNavigate } from 'react-router-dom';

import ImageIcons from 'Components/AppIcons/ImageIcons.jsx';
import { FieldSwitch } from 'Components/FormFields';
import { INWARD_TYPE } from 'Utilities/constants';

const InwardType = ({ handleChange, gatein_id }) => {
  const { values, setFieldValue } = useFormikContext();
  const navigate = useNavigate();

  useEffect(() => {
    if (!values.self) {
      navigate('');
    }
  }, [values.self]);

  return (
    <Grid container style={{ marginBottom: '2rem', alignItems: 'center' }}>
      <Grid item lg={1}>
        <Typography
          variant='h6'
          style={{
            paddingRight: '2rem',
            paddingLeft: '1rem',
          }}
        >
          <b>Inward</b>
        </Typography>
      </Grid>
      <Grid item lg={8}>
        <RadioGroup
          row
          defaultValue='inwards'
          name='inwards'
          value={values.inwards}
          onClick={handleChange}
          style={{ alignItems: 'center' }}
          data-cy='mandi.gateIn.radioGroup'
        >
          <FormControlLabel
            value={INWARD_TYPE.CRATES}
            control={
              <Radio
                color='primary'
                size='small'
                inputProps={{
                  'data-cy': `mandi.radiogroup.${INWARD_TYPE.CRATES}`,
                }}
              />
            }
            style={{ margin: '0' }}
            disabled={!!gatein_id}
          />
          <ImageIcons
            name='crates'
            width='40'
            style={{ marginRight: '0.5rem' }}
          />
          <Typography variant='body2' style={{ marginRight: '3rem' }}>
            <b>Crates</b>
          </Typography>
          {!values.self && (
            <>
              <FormControlLabel
                value={INWARD_TYPE.TRUCK}
                control={
                  <Radio
                    color='primary'
                    size='small'
                    inputProps={{
                      'data-cy': `mandi.radiogroup.${INWARD_TYPE.TRUCK}`,
                    }}
                  />
                }
                style={{ margin: '0' }}
                disabled={!!gatein_id}
              />
              <ImageIcons
                name='truck'
                width='40'
                style={{ marginRight: '0.5rem' }}
              />
              <Typography variant='body2'>
                <b>Trolley</b>
              </Typography>
            </>
          )}
        </RadioGroup>
      </Grid>
      {!gatein_id && (
        <Grid
          item
          md={3}
          xs={12}
          style={{ display: 'flex', alignItems: 'center' }}
        >
          <Typography variant='body1'>Farmer</Typography>
          <FieldSwitch
            size='medium'
            label='Self'
            name='self'
            checked={!!values.self}
            labelPlacement='end'
            InputLabelProps={{
              fullWidth: false,
            }}
            onChange={e => setFieldValue('self', e.target.checked)}
            inputProps={{ 'data-cy': 'mandi.gateIn.switch' }}
          />
        </Grid>
      )}
    </Grid>
  );
};

export default InwardType;
