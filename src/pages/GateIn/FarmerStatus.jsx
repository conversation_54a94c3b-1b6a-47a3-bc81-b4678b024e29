import React from 'react';

import { Grid, Typography } from '@mui/material';
import { useTheme } from '@mui/material/styles';

import { DOCUMENT_STATUS } from 'Utilities/constants/kycTypes';

import { useStyles } from './styled';

const FarmerStatus = ({ farmerStatus }) => {
  const {
    bank_detail_attributes = {},
    kyc_docs_attributes = [],
    otp_status = '',
    upi_detail_attributes = {},
  } = farmerStatus || {};
  const classes = useStyles();
  const { palette } = useTheme();

  const statusUI = status => {
    const statusMappings = {
      [DOCUMENT_STATUS.VERIFIED]: {
        color: palette.text.green,
        status: 'Verified',
      },
      [DOCUMENT_STATUS.UNVERIFIED]: {
        color: palette.text.yellow,
        status: 'Unverified',
      },
      [DOCUMENT_STATUS.PENDING]: {
        color: palette.text.red,
        status: 'Pending',
      },
      [DOCUMENT_STATUS.REJECTED]: {
        color: palette.text.red,
        status: 'Rejected',
      },
    };

    const statusData = !status
      ? {
          color: palette.text.red,
          status: 'Not Uploaded',
        }
      : statusMappings[status];

    return (
      <Typography variant='span' style={{ color: statusData.color }}>
        {statusData.status}
      </Typography>
    );
  };

  let kycStatus = Array.from(
    new Set(kyc_docs_attributes.map(kyc => kyc.status))
  );

  if (kycStatus.length === 0) {
    kycStatus = '';
  } else if (kycStatus.includes(DOCUMENT_STATUS.VERIFIED)) {
    kycStatus = DOCUMENT_STATUS.VERIFIED;
  } else if (kycStatus.includes(DOCUMENT_STATUS.UNVERIFIED)) {
    kycStatus = DOCUMENT_STATUS.UNVERIFIED;
  } else {
    kycStatus = DOCUMENT_STATUS.REJECTED;
  }

  return (
    <Grid item md={4}>
      <Grid className={classes.titleContainer}>
        <Typography component='span'>
          <b>Kyc</b>- {statusUI(kycStatus)}
        </Typography>
        <Typography component='span'>
          <b>OTP</b>- {statusUI(otp_status?.split(' ').pop())}
        </Typography>
      </Grid>
      <Grid className={classes.titleContainer}>
        <Typography component='span'>
          <b>UPI</b>- {statusUI(upi_detail_attributes?.status)}
        </Typography>
        <Typography component='span'>
          <b>Bank Details</b>- {statusUI(bank_detail_attributes?.status)}
        </Typography>
      </Grid>
    </Grid>
  );
};

export default FarmerStatus;
