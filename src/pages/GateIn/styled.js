import { Grid, SnackbarContent } from '@mui/material';
import { makeStyles } from '@mui/styles';
import styled from 'styled-components';

export const ItemGridContainer = styled(Grid)`
  padding: ${({ theme }) => theme.spacing(2.5)};

  & > div {
    width: 20%;
    margin: ${({ theme }) => theme.spacing(1, 0.5)};
  }

  ${({ theme }) => theme.breakpoints.down('md')} {
    & > div {
      width: 45%;
      margin: ${({ theme }) => theme.spacing(1)};
    }
  }

  ${({ theme }) => theme.breakpoints.down('xs')} {
    & > div {
      width: 100%;
      margin: ${({ theme }) => theme.spacing(1)};
    }
  }
`;

export const ActionIcons = styled.span`
  display: flex;
  flex: 1;
  align-self: center;
  text-align: right;

  & svg {
    margin: ${({ theme }) => theme.spacing(0, 1)};
    cursor: pointer;
  }
`;

export const Banner = styled(SnackbarContent)`
  position: absolute;
  background: #000;
  color: #fff;
  opacity: 0.8;
  z-index: 9;
  width: 98%;
  top: 3rem;
`;

export const RowGrid = styled(Grid)``;

export const ColumnGrid = styled(Grid)`
  flex-direction: column;
`;

export const GridContainer = styled(Grid)`
  padding: ${({ theme }) => theme.spacing(1)};
  & > div {
    width: 100%;
    margin: ${({ theme }) => theme.spacing(1, 0)};
  }
`;

export const ImageListWrapper = styled.div`
  display: inline-block;
  padding-bottom: ${({ theme }) => theme.spacing(0.5)};
  overflow-x: auto;
  bacground: #eeeeee;

  span {
    position: relative;
    display: flex;
    margin-right: ${({ theme }) => theme.spacing(1)};
    padding: 5px;
    background: #eeeeee;
    border-radius: 4px;

    img {
      width: 100px;
    }

    .cancel-icon {
      position: absolute;
      top: 1px;
      right: -2px;
    }

    .pdf-icon {
      font-size: ${({ theme }) => theme.typography.h2.fontSize};
    }
  }
`;

export const ButtonWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
  margin: 8px 0;
`;

export const useStyles = makeStyles(() => ({
  titleContainer: {
    display: 'flex',
    justifyContent: 'space-between',
  },
}));
