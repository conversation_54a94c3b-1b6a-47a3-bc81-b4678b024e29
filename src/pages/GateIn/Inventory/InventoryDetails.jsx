import React, { useEffect, useState } from 'react';

import { Grid, Typography } from '@mui/material';
import { Formik, useFormikContext } from 'formik';
import { useNavigate } from 'react-router-dom';

import { useSiteValue } from 'App/SiteContext';
import {
  AppButton,
  AppLoader,
  ConfirmationDialog,
  ImageThumb,
} from 'Components';
import { UploadInput } from 'Components/FormFields';
import { ImageListWrapper } from 'Pages/TokenPage/Styled';
import { getDeliveryById } from 'Services/dc';
import { createGateIn, updateGateIn } from 'Services/gateIn';
import { GATEIN_TYPE, STATUS } from 'Utilities/constants/lots';
import fileUpload from 'Utilities/fileUpload';

import DCLots from './DCLots.jsx';
import DeliveryInfo from './DeliveryInfo';
import { DeliveryDetailsWrapper, useStyles } from './styled';

const InventoryDetails = ({
  deliveryDetails = {},
  selectedDeliveryId,
  gatein_id,
}) => {
  const navigate = useNavigate();
  const [isLoading, setLoading] = useState(false);
  const [delivery, setDelivery] = useState({});
  const classes = useStyles();
  const { mandiId, auctionDate } = useSiteValue();
  const { values: inWardValues } = useFormikContext();
  const [successData, setSuccessData] = useState(false);

  const loadDeliveryDetails = () => {
    setLoading(true);
    getDeliveryById(selectedDeliveryId)
      .then(res => {
        setDelivery(res || {});
      })
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    if (selectedDeliveryId) {
      loadDeliveryDetails();
    } else {
      setDelivery(deliveryDetails);
    }
  }, [selectedDeliveryId]);

  const backToHome = () => {
    navigate('/app/home');
  };

  const getShipmentLots = shipments =>
    shipments?.reduce((acc, shipment) => {
      if (shipment.lots) {
        const availableLots = shipment.lots.map(lot => ({
          ...lot,
          parent_lot: {
            shipment_id: shipment.id,
            shipment_identifier: shipment.identifier,
          },
        }));
        acc.push(...availableLots);
      }
      return acc;
    }, []);

  const handleSUbmit = async values => {
    setLoading(true);
    const { stn_photos = [] } = values;
    const stnBill = await fileUpload(stn_photos, 'gatein');
    const stnIds = deliveryDetails.stn_photos?.map(({ id = '' }) => id);
    const stnData = stnIds?.length ? [...stnIds, ...stnBill] : stnBill;

    const DEFAULT_DATA = {
      gatein_id: +gatein_id,
      mandi_id: mandiId,
      gatein_type: GATEIN_TYPE.SELF,
      inward_type: inWardValues.inwards,
      auction_date: auctionDate,
    };
    const farmer_details = delivery?.shipments?.[0]?.lots?.map(
      ({ sku_id, product, weight_in_kgs, nfi_packaging_bom_id, quantity }) => ({
        status: STATUS.TO_BE_GRADED,
        sku_id,
        product_id: product?.id,
        gross_weight_in_kgs: weight_in_kgs,
        units: quantity,
        nfi_packaging_bom_id,
      })
    );

    const farmer_details_update =
      deliveryDetails.farmer_details?.[0]?.items.map(
        ({
          gatein_grading_id,
          gatein_item_id,
          gross_weight_in_kgs,
          lot_id,
          nfi_packaging_bom_id,
          product_id,
          sku_id,
          units,
          status,
        }) => ({
          gatein_grading_id,
          gatein_item_id,
          gross_weight_in_kgs,
          lot_id,
          nfi_packaging_bom_id,
          product_id,
          sku_id,
          units,
          status,
        })
      );

    const data = {
      ...DEFAULT_DATA,
      transporter_id: null,
      transporter_amount: null,
      vehicle_number: null,
      driver_phone_number: null,
      delivery_id: gatein_id ? delivery.delivery_details?.id : delivery.id,
      farmer_details: [
        {
          farmer_id: 0,
          items: gatein_id ? farmer_details_update : farmer_details,
        },
      ],
      stn_photos: stnData,
    };

    const processGateIn = gatein_id ? updateGateIn : createGateIn;
    processGateIn(data)
      .then(({ responseData }) => {
        setSuccessData(responseData);
      })
      .finally(() => setLoading(false));
  };

  const hasTripStarted = !delivery.has_trip_started;

  if (isLoading) {
    return <AppLoader />;
  }

  const removeAttachments = (index, fieldName, attachments, setFieldValue) => {
    attachments.splice(index, 1);
    setFieldValue(fieldName, attachments);
  };

  return (
    <Formik
      enableReinitialize
      initialValues={{ stn_photos: deliveryDetails.stn_photos }}
      onSubmit={handleSUbmit}
    >
      {({ handleSubmit, values, setFieldValue }) => (
        <DeliveryDetailsWrapper>
          <>
            {!delivery?.id && (
              <Typography
                variant='h6'
                component='h6'
                style={{ textAlign: 'center' }}
              >
                Select Inventory
              </Typography>
            )}
            {!!delivery?.id && (
              <Grid
                container
                direction='row'
                spacing={2}
                justify='space-between'
              >
                <Grid item md={12}>
                  {gatein_id && (
                    <Grid container>
                      <Grid item md={6}>
                        <Typography variant='h4'>
                          <b>{delivery.farmer_details?.[0]?.token}</b>
                        </Typography>
                      </Grid>
                    </Grid>
                  )}
                  <Grid container direction='column'>
                    <Typography
                      variant='h6'
                      color='textPrimary'
                      style={{ fontWeight: 'bold' }}
                    >
                      {delivery.identifier}
                      {delivery.status && `(${delivery.status?.toUpperCase()})`}
                    </Typography>
                    <DeliveryInfo
                      delivery={
                        gatein_id ? delivery.delivery_details : delivery
                      }
                    />
                    <DCLots
                      gateinLots={delivery.delivery_details?.dc_lots}
                      shipmentLots={getShipmentLots(delivery.shipments)}
                    />
                  </Grid>
                  <UploadInput
                    accept='image/*, application/pdf'
                    label='Upload'
                    name='stn_photos'
                    multiple={false}
                    size='small'
                    style={{ margin: '1rem' }}
                    disabled={!gatein_id && hasTripStarted}
                    dataCy={{
                      'data-cy': 'mandi.gateIn.inventory.upload',
                    }}
                  />
                  {values.stn_photos && (
                    <ImageListWrapper style={{ display: 'flex' }}>
                      {values.stn_photos.map((file, index) => (
                        <ImageThumb
                          key={index}
                          url={file?.url || file}
                          file={file?.url || file}
                          removeAttachment={() =>
                            removeAttachments(
                              index,
                              'stn_photos',
                              values.stn_photos,
                              setFieldValue
                            )
                          }
                          dataCy={{
                            'data-cy':
                              'mandi.gateIn.inventory.removeAttachments',
                          }}
                        />
                      ))}
                    </ImageListWrapper>
                  )}
                </Grid>
              </Grid>
            )}
          </>
          <div className={classes.footerButton}>
            <AppButton
              variant='contained'
              color='inherit'
              className='margin-horizontal'
              onClick={backToHome}
              data-cy='mandi.gateIn.inventory.cancelButton'
            >
              Cancel
            </AppButton>
            <AppButton
              variant='contained'
              color='primary'
              className='margin-horizontal'
              loading={isLoading}
              disabled={isLoading || (!gatein_id && hasTripStarted)}
              onClick={handleSubmit}
              data-cy='mandi.gateIn.inventory.saveButton'
            >
              Save
            </AppButton>
          </div>
          <ConfirmationDialog
            title={`Self Gate in ${gatein_id ? 'updated' : 'created'}`}
            open={successData}
            onConfirm={backToHome}
          >
            <Grid container style={{ minWidth: '350px', width: '100%' }}>
              <Typography>
                <b>{`Token: ${successData.farmer_details?.[0]?.token}`}</b>
              </Typography>
            </Grid>
          </ConfirmationDialog>
        </DeliveryDetailsWrapper>
      )}
    </Formik>
  );
};

export default InventoryDetails;
