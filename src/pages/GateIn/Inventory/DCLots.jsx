import React, { useEffect, useState } from 'react';

import { Grid } from '@mui/material';

import { AppLoader, Table } from 'Components';

import { COLUMNS } from './InventoryColumn';
import { useStyles } from './styled';

const DCLots = ({ gateinLots, shipmentLots }) => {
  const [isLoading, setLoading] = useState(false);
  const [lots, setLots] = useState([]);
  const classes = useStyles();

  useEffect(() => {
    if (shipmentLots) {
      setLoading(true);
      setLots(shipmentLots);
    } else {
      setLots(gateinLots);
    }
    setLoading(false);
  }, [shipmentLots, gateinLots]);

  if (isLoading) {
    return <AppLoader />;
  }
  const footerData = lots.reduce(
    (total, { quantity_with_partial_weight = 0 }) => {
      total += quantity_with_partial_weight;
      return total;
    },
    0
  );

  return (
    <>
      <Table
        size='small'
        isFooter
        sticky
        hover
        columns={COLUMNS}
        data={lots}
        dataKey='id'
      />
      <Grid container className={classes.totalContainer}>
        <Grid item lg={6} />
        <Grid item lg={6}>
          <Grid container>
            <Grid item lg={6}>
              <b>Total:</b>
            </Grid>
            <Grid item lg={5} className={classes.totalAmount}>
              <b>{footerData}</b>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </>
  );
};

export default DCLots;
