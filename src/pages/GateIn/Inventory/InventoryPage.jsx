import React, { useEffect, useState } from 'react';

import { Grid } from '@mui/material';
import queryString from 'query-string';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

import { useSiteValue } from 'App/SiteContext';
import { Pagination as CustomPagination } from 'Components';
import PageLayout from 'Components/PageLayout';
import Sm from 'Components/Responsive/Sm';
import { getDeliveries } from 'Services/dc';
import { scrollToView } from 'Utilities';
import { PAGE_SIZE } from 'Utilities/constants';

import InventoryList from './Inventory';
import InventoryDetails from './InventoryDetails.jsx';
import { LeftSection, RightSection, useStyles } from './styled';

const InventoryPage = ({ setSuccessData, toggleGateInSuccess }) => {
  const [rightScreen, setRightScreen] = useState(false);
  const [selectedDeliveryId, setSelectedDeliveryId] = useState(null);
  const [deliveriesList, setDeliveriesList] = useState([]);
  const [isLoading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(1);
  const { mandiId } = useSiteValue();
  const classes = useStyles();
  const navigate = useNavigate();
  const { id = '' } = useParams();
  const location = useLocation();

  const { page: pageNo = 1 } = queryString.parse(location.search, {
    arrayFormat: 'separator',
    arrayFormatSeparator: '|',
    parseNumbers: true,
  });

  const selectCard = async id => {
    await setSelectedDeliveryId(id);
    await scrollToView(document.querySelector('.selectedCard'));
    // Commented to stop auto select with route change
    // navigate(`inventory/${id}${location.search}`);
  };

  const selectMobileCard = async id => {
    await setSelectedDeliveryId(id);
    // navigate(`inventory/${id}${location.search}`);
  };

  const loadDeliveriesList = page => {
    setDeliveriesList([]);
    getDeliveries({
      limit: PAGE_SIZE,
      offset: (page - 1) * PAGE_SIZE,
    }).then(setDataToState);
  };

  useEffect(() => {
    loadDeliveriesList(1);
    setSelectedDeliveryId(null);
  }, [mandiId]);

  const setDataToState = res => {
    setLoading(true);
    const { items = [], total_count = 0 } = res || {};
    setDeliveriesList(items);
    setTotalCount(Math.ceil(total_count / PAGE_SIZE) || 0);

    if (items.length > 0) {
      const selectedDiD = items.find(item => item.id === +id) || items[0];
      selectCard(selectedDiD.id || null);
      selectMobileCard(selectedDiD.id || null);
    }
    setLoading(false);
  };

  useEffect(() => {
    setPage(pageNo);
  }, [location]);

  const handleChangePage = (_, newPage) => {
    setPage(newPage);
    setLoading(true);
    setDeliveriesList([]);
    setSelectedDeliveryId(null);
    loadDeliveriesList(newPage);
    navigate(`${location.pathname}?page=${newPage}`);
  };

  useEffect(() => {
    if (selectedDeliveryId) {
      setRightScreen(true);
    }
  }, [selectedDeliveryId]);

  const backHandler = () => {
    setRightScreen(false);
    setSelectedDeliveryId(null);
  };

  return (
    <Grid className={classes.InventoryContainer}>
      <PageLayout.Body flexDirection='row'>
        <Sm
          backHandler={backHandler}
          rightScreen={rightScreen}
          selectedId={selectedDeliveryId}
          rightSection={
            <RightSection>
              <InventoryDetails
                selectedDeliveryId={selectedDeliveryId}
                setSuccessData={setSuccessData}
                toggleGateInSuccess={toggleGateInSuccess}
              />
            </RightSection>
          }
          leftSection={
            <LeftSection>
              <InventoryList
                isLoading={isLoading}
                selectedDeliveryId={selectedDeliveryId}
                setSelectedDeliveryId={setSelectedDeliveryId}
                deliveries={deliveriesList}
              />
              {totalCount > 1 && (
                <CustomPagination
                  count={totalCount}
                  page={page}
                  shape='circular'
                  onChange={handleChangePage}
                  justify='center'
                />
              )}
            </LeftSection>
          }
        >
          <PageLayout.Body flexDirection='row'>
            <LeftSection>
              <InventoryList
                isLoading={isLoading}
                selectedDeliveryId={selectedDeliveryId}
                setSelectedDeliveryId={selectCard}
                deliveries={deliveriesList}
              />
              {totalCount > 1 && (
                <CustomPagination
                  count={totalCount}
                  page={page}
                  shape='circular'
                  onChange={handleChangePage}
                  justify='center'
                />
              )}
            </LeftSection>
            <InventoryDetails selectedDeliveryId={selectedDeliveryId} />
          </PageLayout.Body>
        </Sm>
      </PageLayout.Body>
    </Grid>
  );
};

export default InventoryPage;
