import React from 'react';

import { Card, CardContent, CardHeader, Grid, Typography } from '@mui/material';
import clsx from 'clsx';

import { getFormattedDateTime } from 'Utilities/dateUtils';

import { useStyles } from './styled';

const InventoryCard = ({ delivery, selectedDeliveryId, onClickDelivery }) => {
  const classes = useStyles();

  return (
    <Card
      key={delivery.id}
      className={clsx(classes.root, {
        [classes.selectedCard]:
          selectedDeliveryId && delivery.id === selectedDeliveryId,
        selectedCard:
          selectedDeliveryId &&
          delivery.id === parseInt(selectedDeliveryId, 10),
      })}
      raised
      elevation={4}
    >
      <CardHeader
        className={classes.header}
        title={
          <Typography
            variant='subtitle1'
            component='h3'
            color='primary'
            style={{ fontWeight: 'bold', cursor: 'pointer', padding: '.3, 0' }}
            onClick={() => onClickDelivery(delivery.id)}
          >
            {delivery.identifier}
          </Typography>
        }
      />
      <CardContent className={classes.content}>
        <Grid container direction='column' spacing={0}>
          <Grid item xs={12}>
            <Typography
              variant='body2'
              component='label'
              color='textPrimary'
              className={classes.detailsTitle}
            >
              Delivery weight:
            </Typography>
            <Typography variant='body2' component='label' color='textPrimary'>
              {delivery.total_delivery_weight_in_kgs || 0} Kgs
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <Typography
              variant='body2'
              component='label'
              color='textPrimary'
              className={classes.detailsTitle}
            >
              Vehicle Number:
            </Typography>
            <Typography variant='body2' component='label' color='textPrimary'>
              {delivery.vehicle_details_json?.number}
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <Typography
              variant='body2'
              component='label'
              color='textPrimary'
              className={classes.detailsTitle}
            >
              Dispatched Time:
            </Typography>
            <Typography variant='body2' component='label' color='textPrimary'>
              {getFormattedDateTime(
                delivery.last_location_vehicle_dispatch_time
              )}
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <Typography
              variant='body2'
              component='label'
              color='textPrimary'
              className={classes.detailsTitle}
            >
              Trip Id:
            </Typography>
            <Typography variant='body2' component='label' color='textPrimary'>
              {delivery.trip_id}
            </Typography>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default InventoryCard;
