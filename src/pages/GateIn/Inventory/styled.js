import { Grid } from '@mui/material';
import { makeStyles } from '@mui/styles';
import styled from 'styled-components';

export const useStyles = makeStyles(theme => ({
  root: {
    minHeight: '150px',
    margin: '8px 0',
    border: '2px solid transparent',
    height: 'max-content',
    display: 'flex',
    flexDirection: 'column',
  },
  selectedCard: {
    border: `2px solid ${theme.palette.primary.light}`,
  },
  header: {
    padding: '4px 2px 4px 8px',
  },
  avtarIcon: {
    width: theme.spacing(5),
    height: theme.spacing(5),
    margin: theme.spacing(0.5),
    fontSize: 14,
    fontWeight: 'bold',
    color: theme.palette.primary.contrastText,
    backgroundColor: theme.palette.primary.light,
  },
  content: {
    padding: '0 2px 0 8px',
    flex: 1,
  },
  locationName: {
    display: 'flex',
  },
  bullet: {
    display: 'inline-block',
    margin: '0 2px',
    transform: 'scale(0.8)',
  },
  locationIcon: {
    marginRight: '2px',
  },
  title: {
    fontSize: 14,
  },
  pos: {
    marginBottom: 12,
  },
  footer: {
    justifyContent: 'space-between',
    padding: 4,
  },
  detailsTitle: {
    fontWeight: 'bold',
    marginRight: '4px',
  },
  from: {
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    width: '15rem',
    display: 'inline-block',
    verticalAlign: 'bottom',
  },
  footerButton: {
    position: 'absolute',
    bottom: '0',
    right: '0',
    background: 'white',
    width: '100%',
    zIndex: 10,
    padding: '1rem',
    textAlign: 'right',
  },
  InventoryContainer: {
    display: 'flex',
    flexDirection: 'column',
    height: '60vh',
    overflow: 'hidden',
  },
  totalContainer: {
    margin: '10px 0',
    fontSize: '1rem',
  },
  totalAmount: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
}));

export const LeftSection = styled.div`
  display: flex;
  min-width: 300px;
  width: 25%;
  height: 100%;
  flex-direction: column;
  overflow: auto;

  ${props => props.theme.breakpoints.down('md')} {
    align-self: center;
    width: 98%;
  }
`;

export const RightSection = styled.div`
  min-width: 300px;
  width: 25%;
  height: 100%;
  flex-direction: column;
  overflow: auto;

  ${props => props.theme.breakpoints.down('md')} {
    width: 0;
    min-width: 100%;
    overflow-x: hidden;
  }

  .MuiGrid-container {
    display: inline;
  }
`;

export const DeliveryDetailsWrapper = styled.div`
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: auto;
  margin-left: ${props => props.theme.spacing(1)};
  min-width: 410px;
  padding: ${props => props.theme.spacing(1, 1)};
  background-color: ${props => props.theme.palette.background.paper};

  ${props => props.theme.breakpoints.down('md')} {
    min-width: auto;
  }
`;

export const CardWrapper = styled.div`
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: auto;
  height: 100%;
  padding: ${props => props.theme.spacing(0, 0.5)};
`;

export const DeliveryContainer = styled(Grid)`
  padding: ${props => props.theme.spacing(1)};

  & > div {
    width: 32%;
    margin: ${props => props.theme.spacing(0.5, 0)};
  }

  ${props => props.theme.breakpoints.down('md')} {
    & > div {
      width: 47%;
      margin: ${props => props.theme.spacing(0.5, 0)};
    }
  }

  ${props => props.theme.breakpoints.down('md')} {
    & > div {
      width: 100%;
      margin: ${props => props.theme.spacing(0.5, 0)};
    }
  }
`;
