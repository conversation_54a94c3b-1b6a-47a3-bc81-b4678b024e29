import React from 'react';

import { Grid, Typography } from '@mui/material';

import { getFormattedDateTime } from 'Utilities/dateUtils';

import { DeliveryContainer } from './styled';

const DeliveryInfo = ({ delivery = {} }) => (
  <DeliveryContainer container direction='row' spacing={0}>
    <Grid item xs={12} md={6} lg={6}>
      <Typography
        variant='body2'
        component='label'
        color='textPrimary'
        style={{ fontWeight: 'bold', marginRight: '4px' }}
      >
        Delivery Weight:
      </Typography>
      <Typography variant='body2' component='span' color='textPrimary'>
        {delivery.total_delivery_weight_in_kgs || delivery.gross_weight} Kgs
      </Typography>
    </Grid>
    <Grid item xs={12} md={6} lg={6}>
      <Typography
        variant='body2'
        component='label'
        color='textPrimary'
        style={{ fontWeight: 'bold', marginRight: '4px' }}
      >
        PO/TO:
      </Typography>
      <Typography variant='body2' component='span' color='textPrimary'>
        {delivery.shipments?.[0]?.identifier ||
          delivery.shipments?.[0]?.po_identifier ||
          delivery.source_identifier}
      </Typography>
    </Grid>
    <Grid item xs={6}>
      <Typography
        variant='body2'
        component='label'
        color='textPrimary'
        style={{ fontWeight: 'bold', marginRight: '4px' }}
      >
        Dispatch Time:
      </Typography>
      <Typography variant='body2' component='span' color='textPrimary'>
        {getFormattedDateTime(
          delivery.vehicle_dispatch_time ||
            delivery.last_location_vehicle_dispatch_time
        )}
      </Typography>
    </Grid>
    <Grid item xs={6}>
      <Typography
        variant='body2'
        component='label'
        color='textPrimary'
        style={{ fontWeight: 'bold', marginRight: '4px' }}
      >
        Trip Id:
      </Typography>
      <Typography variant='body2' component='span' color='textPrimary'>
        {delivery.trip_id || ''}
      </Typography>
    </Grid>
    <Grid item xs={6}>
      <Typography
        variant='body2'
        component='label'
        color='textPrimary'
        style={{ fontWeight: 'bold', marginRight: '4px' }}
      >
        Source Address:
      </Typography>
      <Typography variant='body2' component='span' color='textPrimary'>
        {delivery.shipments?.[0]?.sender_address || delivery.source_address}
      </Typography>
    </Grid>
  </DeliveryContainer>
);

export default DeliveryInfo;
