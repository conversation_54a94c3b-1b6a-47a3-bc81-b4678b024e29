import React from 'react';

import { Typography } from '@mui/material';

export const COLUMNS = [
  {
    key: 'product',
    header: 'Product',
    align: 'center',
    render: ({ rowData }) => rowData.product?.name || rowData.product_name,
    style: {
      maxWidth: '70px',
    },
  },
  {
    key: 'identifier',
    header: ' Sku',
    align: 'center',
    render: ({ rowData }) => rowData.identifier || rowData.label,
    style: {
      maxWidth: '90px',
    },
  },
  {
    key: 'units',
    header: 'Units',
    align: 'center',
    render: ({ rowData }) => (
      <>
        {rowData.quantity_with_partial_weight}
        <Typography
          component='div'
          variant='caption'
          className='disabled-text'
          color='textPrimary'
        >
          {`P: (${rowData.partial_weight || 0})`}
        </Typography>
      </>
    ),
    style: {
      maxWidth: '70px',
    },
  },
];
