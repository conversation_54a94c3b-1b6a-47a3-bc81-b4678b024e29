import React from 'react';

import { Typography } from '@mui/material';

import { AppLoader } from 'Components';

import InventoryCard from './InventoryCard';
import { CardWrapper } from './styled';

const InventoryList = ({
  selectedDeliveryId,
  setSelectedDeliveryId,
  deliveries,
  isLoading,
}) => {
  const onClickDelivery = deliveryId => {
    setSelectedDeliveryId(deliveryId);
  };

  return (
    <CardWrapper>
      {deliveries.map(t => (
        <InventoryCard
          key={t.id}
          delivery={t}
          onClickDelivery={onClickDelivery}
          selectedDeliveryId={selectedDeliveryId}
        />
      ))}
      {deliveries.length === 0 && (
        <Typography
          variant='h6'
          component='h6'
          style={{ textAlign: 'center', alignSelf: 'center', flex: 1 }}
        >
          No Inventory Available
        </Typography>
      )}
      {isLoading && <AppLoader />}
    </CardWrapper>
  );
};

export default InventoryList;
