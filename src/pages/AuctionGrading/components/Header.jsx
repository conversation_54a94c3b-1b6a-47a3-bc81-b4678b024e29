import { CheckCircle } from '@mui/icons-material';
import { Box, Typography } from '@mui/material';

const Header = ({
  grade,
  token,
  farmerMarka,
  units,
  totalGrades,
  currentGrade,
  isSummary,
  gradeNames = [],
  onGradeClick,
  maxReachedIndex = 0,
  lotsDetails = [],
  onSummaryClick,
  packType,
  isAllGradesComplete = false,
}) => (
  <Box p={2} bgcolor='#F8F9FA' borderRadius={4} mx={2} my={2}>
    <Box display='flex' justifyContent='space-between'>
      <Box display='flex' gap={2}>
        <Box>
          <Typography variant='body2'>Current Token</Typography>
          <Typography variant='h5' fontWeight='bold'>
            {token}
          </Typography>
        </Box>
        {!isSummary && (
          <>
            <Box>
              <Typography variant='body2'>Grade</Typography>
              <Typography variant='h6' fontWeight='bold'>
                {grade}
              </Typography>
            </Box>
            <Box>
              <Typography variant='body2'>Pack Type</Typography>
              <Typography variant='h6' fontWeight='bold'>
                {packType}
              </Typography>
            </Box>
          </>
        )}
      </Box>
      {!isSummary && (
        <Box>
          {farmerMarka && (
            <Typography>
              Farmer Marka: <strong>{farmerMarka}</strong>
            </Typography>
          )}
          <Typography>
            No. of Boxes: <strong>{units}</strong>
          </Typography>
        </Box>
      )}
      {isSummary && (
        <Typography
          color='primary'
          fontWeight='bold'
          fontSize='h6.fontSize'
          display='flex'
          alignItems='center'
          gap={1}
        >
          <CheckCircle />
          All Done
        </Typography>
      )}
    </Box>
    <Box display='flex' gap={0.5}>
      {Array(totalGrades)
        .fill(0)
        .map((_, i) => {
          // When in summary mode, no grade should appear active
          const isActive = !isSummary && i === currentGrade - 1;
          const fontSize =
            totalGrades > 6 ? '0.7rem' : totalGrades > 4 ? '0.8rem' : '0.9rem';
          const gradeName = gradeNames[i] || `Grade ${i + 1}`;

          // Check if this grade has data (customer_id and price) regardless of current position
          const lotData = lotsDetails[i];
          const hasData =
            lotData &&
            lotData.customer_id &&
            (lotData.price || lotData.price === '0');

          // Determine background color based on data presence and state
          let backgroundColor;
          if (isActive) {
            backgroundColor = 'primary.main'; // Current grade
          } else if (hasData) {
            backgroundColor = 'rgba(229, 231, 235, 1)'; // Has data - completed style
          } else {
            backgroundColor = 'rgba(229, 231, 235, 0.32)'; // No data - pending style
          }

          // Determine font styles based on state
          let fontColor;
          let fontWeight;

          if (isActive) {
            fontColor = 'rgba(255, 255, 255, 1)'; // Current grade - white
            fontWeight = 600;
          } else if (hasData) {
            fontColor = 'rgba(0, 0, 0, 1)'; // Has data - black, bold
            fontWeight = 600;
          } else {
            fontColor = 'rgba(0, 0, 0, 0.6)'; // No data - black with opacity
            fontWeight = 500;
          }

          return (
            <Box
              key={i}
              sx={{
                width: '100%',
                height: 42,
                borderRadius: 2,
                mt: 1,
                backgroundColor,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: fontColor,
                fontSize,
                fontWeight,
                transition: 'all 0.3s ease',
                padding: '9.5px',
                gap: '9.5px',
                cursor: 'pointer',
                '&:hover': {
                  opacity: 0.8,
                },
              }}
              onClick={() => {
                if (onGradeClick) {
                  onGradeClick(i);
                }
              }}
            >
              {gradeName}
            </Box>
          );
        })}

      {/* Summary Tab */}
      <Box
        sx={{
          width: '100%',
          height: 42,
          borderRadius: 2,
          mt: 1,
          backgroundColor: isSummary
            ? 'primary.main' // Active summary
            : isAllGradesComplete
              ? 'rgba(229, 231, 235, 1)' // Ready for summary
              : 'rgba(229, 231, 235, 0.32)', // Not ready
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: isSummary
            ? 'rgba(255, 255, 255, 1)' // Active - white
            : isAllGradesComplete
              ? 'rgba(0, 0, 0, 1)' // Ready - black
              : 'rgba(0, 0, 0, 0.4)', // Not ready - muted
          fontSize:
            totalGrades > 6 ? '0.7rem' : totalGrades > 4 ? '0.8rem' : '0.9rem',
          fontWeight: isSummary || isAllGradesComplete ? 600 : 500,
          transition: 'all 0.3s ease',
          padding: '9.5px',
          gap: '9.5px',
          cursor: isAllGradesComplete ? 'pointer' : 'not-allowed',
          opacity: isAllGradesComplete ? 1 : 0.6,
          '&:hover': {
            opacity: isAllGradesComplete ? 0.8 : 0.6,
          },
        }}
        onClick={() => {
          if (isAllGradesComplete && onSummaryClick) {
            onSummaryClick();
          }
        }}
      >
        Summary
      </Box>
    </Box>
  </Box>
);

export default Header;
