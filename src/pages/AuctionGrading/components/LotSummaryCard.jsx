import { Edit } from '@mui/icons-material';
import {
  Box,
  Chip,
  Divider,
  IconButton,
  Typography,
} from '@mui/material';
import PropTypes from 'prop-types';

import { CUSTOMER_NAMES } from 'Utilities/constants';

const LotSummaryCard = ({ lot, index, totalLots, onEdit }) => {
  return (
    <Box
      sx={{
        bgcolor: '#F3F4F6',
        py: 2,
        px: 4,
        mx: 'auto',
        mb: index === totalLots - 1 ? 0 : 2,
        borderRadius: 4,
        width: 'max-content',
        minWidth: '600px',
      }}
    >
      {/* Header with Grade, Mandi Number, Price, and Customer */}
      <Box display='flex' alignItems='center' gap={3} mb={2}>
        <Typography
          variant='h6'
          color='primary'
          sx={{ minWidth: 80 }}
        >
          {lot.grade}
        </Typography>

        <Typography sx={{ minWidth: 140 }}>
          Mandi No:{' '}
          <Typography
            component='span'
            fontWeight='bold'
            fontSize='h6.fontSize'
          >
            {lot.mandiNumber?.mandi_number || 'N/A'}
          </Typography>
        </Typography>

        <Divider flexItem orientation='vertical' />

        <Typography sx={{ minWidth: 120 }}>
          Price:{' '}
          <Typography
            component='span'
            fontWeight='bold'
            fontSize='h6.fontSize'
          >
            ₹{lot.price}
          </Typography>
        </Typography>

        <Divider flexItem orientation='vertical' />

        <Typography sx={{ minWidth: 220 }}>
          Customer:{' '}
          <Typography
            component='span'
            fontWeight='bold'
            fontSize='h6.fontSize'
            sx={{ minWidth: 100 }}
          >
            {lot.customer_short_code ||
              CUSTOMER_NAMES[lot.customer_id]?.customer_short_code}
          </Typography>
        </Typography>

        <IconButton onClick={() => onEdit(index)}>
          <Edit />
        </IconButton>
      </Box>

      {/* SKU Details - Show price and units for each SKU size */}
      {lot.lots && lot.lots.length > 0 && (
        <Box>
          <Typography
            variant='subtitle2'
            sx={{
              mb: 1,
              fontWeight: 'bold',
              color: 'text.secondary',
            }}
          >
            SKU Details:
          </Typography>
          <Box display='flex' flexWrap='wrap' gap={1}>
            {lot.lots.map((skuLot, skuIndex) => {
              return (
                <Chip
                  key={skuIndex}
                  label={`${skuLot.sku_size_name}: ₹${skuLot.price} × ${skuLot.units} units`}
                  variant='outlined'
                  size='small'
                  sx={{
                    bgcolor: 'white',
                    fontWeight: 'medium',
                    '& .MuiChip-label': {
                      fontSize: '0.75rem',
                    },
                  }}
                />
              );
            })}
          </Box>
        </Box>
      )}
    </Box>
  );
};

LotSummaryCard.propTypes = {
  lot: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    grade: PropTypes.string,
    price: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    customer_short_code: PropTypes.string,
    customer_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    mandiNumber: PropTypes.shape({
      mandi_number: PropTypes.string,
    }),
    lots: PropTypes.arrayOf(
      PropTypes.shape({
        sku_size_name: PropTypes.string,
        price: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        units: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      })
    ),
  }).isRequired,
  index: PropTypes.number.isRequired,
  totalLots: PropTypes.number.isRequired,
  onEdit: PropTypes.func.isRequired,
};

export default LotSummaryCard;
