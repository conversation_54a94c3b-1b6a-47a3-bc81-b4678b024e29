import { ArrowBack } from '@mui/icons-material';
import { Box, Button, Paper } from '@mui/material';
import AppButton from 'Components/AppButton';

const Footer = ({
  handleNext,
  handleCancel,
  handleBack,
  showBackButton,
  isSummary,
  loading = false
}) => (
  <Paper elevation={2} sx={{ p: 2 }}>
    <Box display="flex" justifyContent="space-between" alignItems="center">
      <Box>
        {showBackButton && (
          <Button
            size="large"
            variant="outlined"
            startIcon={<ArrowBack />}
            onClick={handleBack}
          >
            Back
          </Button>
        )}
      </Box>
      <Box>
        <Button
          size="large"
          variant="contained"
          color="inherit"
          onClick={handleCancel}
        >
          Cancel
        </Button>
        <AppButton
          size="large"
          variant="contained"
          onClick={handleNext}
          sx={{ ml: 2 }}
          loading={loading}
          disabled={loading}
        >
          {isSummary ? 'Submit' : 'Save and Next'}
        </AppButton>
      </Box>
    </Box>
  </Paper>
);

export default Footer;
