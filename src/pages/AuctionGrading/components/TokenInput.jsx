import { useState } from 'react';

import { Box, Typography } from '@mui/material';

import PageLayout from 'Components/PageLayout/index.jsx';
import ScreenNumpad from 'Components/ScreenNumpad';

const TokenInput = ({ onSubmit, loading = false }) => {
  const [token, setToken] = useState('');

  const handleChange = value => {
    setToken(prev => prev + value);
  };
  const handleClear = () => {
    setToken(prev => prev.slice(0, -1));
  };

  const handleSubmit = () => {
    onSubmit(token);
  };

  return (
    <PageLayout title='Auction' showLiveLink={false} showSelectMandi>
      <Box
        display='flex'
        flexDirection='column'
        alignItems='center'
        justifyContent='center'
        height='100%'
      >
        <Typography mb={1} variant='h6' fontWeight='bold'>
          Enter Token
        </Typography>
        <Box
          display='flex'
          alignItems='center'
          boxShadow={2}
          px={2}
          mb={4}
          sx={{
            width: 320,
            height: 80,
          }}
        >
          <Typography
            variant='h4'
            fontWeight='bold'
            display='flex'
            alignItems='center'
          >
            <Typography
              component='span'
              variant='h5'
              fontWeight='bold'
              sx={{ color: 'gray', mr: 1 }}
            >
              C-
            </Typography>
            {token}
          </Typography>
        </Box>
        <ScreenNumpad
          onSubmit={handleSubmit}
          onClear={handleClear}
          onChange={handleChange}
          loading={loading}
        />
      </Box>
    </PageLayout>
  );
};

export default TokenInput;
