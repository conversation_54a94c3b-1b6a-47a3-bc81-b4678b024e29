import React from 'react';

import {
  Box,
  Typography,
  TextField,
  Card,
  Grid,
  Avatar,
  InputAdornment,
} from '@mui/material';
import { useFormikContext } from 'formik';
import PropTypes from 'prop-types';

const SkuComponent = ({ lots, onSkuUpdate, isEditable = true }) => {
  const { values, setFieldValue } = useFormikContext();

  // Handle price input change
  const handlePriceChange = (lot, value) => {
    if (onSkuUpdate) {
      // Find the lot index in the lots array
      const lotIndex = lots.findIndex(l => l.sku_id === lot.sku_id);
      onSkuUpdate(lotIndex, lot.sku_id, 'price', value, setFieldValue);
    }
  };

  if (!lots || lots.length === 0) {
    return (
      <Box p={2}>
        <Typography variant='body2' color='text.secondary'>
          No SKU data available
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', gap: 1, width: '100%' }}>
      {lots.map((lot, index) => {
        // Get the current value from Formik or fallback to lot.price
        const fieldPath = `lots.${index}.price`;
        const currentValue = values?.lots?.[index]?.price ?? lot.price ?? '';
        
        return (
          <Box
            key={lot.sku_id}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              flex: 1,
              minWidth: '70px',
              maxWidth: '120px',
            }}
          >
            {/* SKU Name */}
            <Typography
              variant='caption'
              sx={{
                fontSize: '0.7rem',
                fontWeight: 'bold',
                color: 'text.secondary',
                textAlign: 'center',
                lineHeight: 1,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: '100%',
                mb: 0.5,
              }}
            >
              {lot.sku_size_name}
            </Typography>

            {/* Price Input */}
            <TextField
              type='number'
              value={currentValue}
              onChange={e => handlePriceChange(lot, e.target.value)}
              size='small'
              disabled={!isEditable}
              InputProps={{
                startAdornment: (
                  <InputAdornment position='start'>₹</InputAdornment>
                ),
              }}
              sx={{
                width: '100%',
                '& .MuiInputBase-input': {
                  padding: '4px 4px',
                  textAlign: 'center',
                  fontSize: '0.8rem',
                },
                '& input[type=number]': {
                  MozAppearance: 'textfield',
                },
                '& input[type=number]::-webkit-outer-spin-button': {
                  WebkitAppearance: 'none',
                  margin: 0,
                },
                '& input[type=number]::-webkit-inner-spin-button': {
                  WebkitAppearance: 'none',
                  margin: 0,
                },
              }}
            />
          </Box>
        );
      })}
    </Box>
  );
};

SkuComponent.propTypes = {
  lots: PropTypes.array.isRequired,
  onSkuUpdate: PropTypes.func,
  isEditable: PropTypes.bool,
};

export default SkuComponent;
