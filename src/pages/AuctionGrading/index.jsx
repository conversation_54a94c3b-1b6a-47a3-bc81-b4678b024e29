import { useEffect, useState } from 'react';

import { useLocation } from 'react-router-dom';

import { useSiteValue } from 'App/SiteContext';
import useNotify from 'Hooks/useNotify';
import { getFarmerAuctionLots } from 'Services/farmerLots';
import { getSkuList } from 'Services/regrade';
import { getAuctionCustomer } from 'Services/users';

import AuctionDetailCapture from './components/AuctionDetailCapture';
import TokenInput from './components/TokenInput';
import { AUCTION_GRADING_STEPS } from './const';
import {
  formatLotsData,
  getCustomerShortCodes,
  formatMandiNumberData,
} from './utils';

const AuctionGrading = () => {
  const location = useLocation();
  const state = location.state || {};

  const notify = useNotify();
  const { mandiId, auctionDate } = useSiteValue();
  const [step, setStep] = useState(AUCTION_GRADING_STEPS.TOKEN_INPUT);
  const [token, setToken] = useState('');
  const [customers, setCustomers] = useState([]);
  const [details, setDetails] = useState([]);
  const [mandiNumberInfo, setMandiNumberInfo] = useState([]);
  const [loading, setLoading] = useState(false);
  const [skuData, setSkuData] = useState([]);

  useEffect(() => {
    getAuctionCustomer({ mandi_id: mandiId }).then(res => {
      setCustomers(getCustomerShortCodes(res.responseData));
    });

    // Fetch SKU data for the mandi
    getSkuList(mandiId)
      .then(res => {
        setSkuData(res.items || []);
      })
      .catch(error => {
        console.error('Error fetching SKU data:', error);
      });
  }, [mandiId]);

  const handleSubmit = token => {
    setLoading(true);
    getFarmerAuctionLots({
      token: `C-${token}`,
      auction_date: auctionDate,
      mandi_id: mandiId,
      include_discount_view: true,
    })
      .then(res => {
        console.log('API Response:', res);
        console.log('SKU Data:', skuData);

        try {
          const lots = formatLotsData(res.responseData, skuData);
          console.log('Formatted lots:', lots);

          const mandiNumberInfo = formatMandiNumberData(res.responseData, lots);
          console.log('Formatted mandi number info:', mandiNumberInfo);

          if (lots.length === 0) {
            notify('No Lots Available', 'error');
          } else {
            setToken(token);
            setDetails(mandiNumberInfo);
            setMandiNumberInfo(
              Object.values(res.responseData)[0]?.mandi_number_details || {}
            );
            setStep(AUCTION_GRADING_STEPS.AUCTION_DETAIL_CAPTURE);
          }
        } catch (processingError) {
          console.error('Error processing data:', processingError);
          notify('Error processing auction data', 'error');
        }
      })
      .catch(apiError => {
        console.error('API Error:', apiError);
        notify('Error fetching auction lots', 'error');
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleCancel = () => {
    setStep(AUCTION_GRADING_STEPS.TOKEN_INPUT);
    setToken('');
    setLoading(false);
  };

  return (
    <>
      {step === AUCTION_GRADING_STEPS.TOKEN_INPUT && (
        <TokenInput onSubmit={handleSubmit} loading={loading} />
      )}
      {step === AUCTION_GRADING_STEPS.AUCTION_DETAIL_CAPTURE && (
        <AuctionDetailCapture
          token={token}
          customers={customers}
          lotsInfo={details}
          mandiNumberInfo={mandiNumberInfo}
          skuData={skuData}
          handleCancel={handleCancel}
        />
      )}
    </>
  );
};

export default AuctionGrading;
