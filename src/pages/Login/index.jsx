import React, { useState, useEffect } from 'react';

import {
  Box,
  Container,
  Typography,
  Paper,
  CircularProgress,
  Backdrop,
  Snackbar,
  Alert,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { GoogleLogin } from '@react-oauth/google';
import { jwtDecode } from 'jwt-decode';
import { useNavigate } from 'react-router-dom';

import { useAuth } from 'Contexts/AuthContext';
import { googleLogin } from 'Services/users';
import { getUserData } from 'Utilities/localStorage';

// Styled components
const LoginContainer = styled(Container)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  minHeight: '100vh',
  padding: theme.spacing(2),
}));

const LoginCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  maxWidth: 400,
  width: '100%',
  borderRadius: theme.shape.borderRadius,
}));

const Logo = styled('img')({
  width: 180,
  marginBottom: 24,
});

/**
 * Login page component with Google Sign-In
 * @returns {JSX.Element} Login page
 */
const LoginPage = () => {
  const { loginSuccess } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const userInfo = getUserData();

  // Handle Google login success - matched with b2r-frontend implementation
  const handleGoogleLoginSuccess = async response => {
    try {
      setLoading(true);
      setError('');

      if (!response?.credential) {
        throw new Error(
          'Google authentication failed - no credential returned'
        );
      }

      // Decode Google token to get basic user information (client-side only)
      const decodedToken = jwtDecode(response.credential);
      console.log('Decoded Google token:', {
        email: decodedToken.email,
        name: decodedToken.name,
      });

      // Send only the id_token to the backend - IMPORTANT: don't send any other fields
      // This matches exactly how the b2r-frontend is implementing the authentication
      const loginResponse = await googleLogin({
        id_token: response.credential,
      });

      console.log('Login successful, processing backend response');

      // The token is in the response headers, not in the body
      console.log('Raw login response:', loginResponse);
      console.log('Response headers:', loginResponse.headers);

      if (!loginResponse) {
        throw new Error('Empty response from authentication server');
      }

      // Extract authorization token from headers
      const authHeader = loginResponse.headers['authorization'];
      console.log('Authorization header:', authHeader);

      if (!authHeader) {
        console.error('No authorization header found in response');
        throw new Error(
          'Invalid response from authentication server: missing token'
        );
      }

      // Token format should be "Bearer eyJhbGciOiJ..." - we need to extract the token part
      const token = authHeader.replace('Bearer ', '');

      // Create a response object with the token in the expected format
      const authResponse = {
        authentication_token: token,
        // Add some basic user info that will be enriched later by the profile API
        email: decodedToken.email,
        name: decodedToken.name,
        picture: decodedToken.picture,
      };

      console.log(
        'Authentication successful, token received from backend:',
        token.substring(0, 10) + '...'
      );

      // The constructed authResponse contains the authentication token and user info
      console.log('Proceeding with login and redirection');
      loginSuccess(authResponse);
    } catch (err) {
      console.error('Google login error:', err);
      setError(
        err.response?.data?.message ||
          err.message ||
          'Failed to log in with Google. Please try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  // Handle Google login error
  const handleGoogleLoginError = error => {
    console.error('Google login error:', error);

    // Check for FedCM error
    if (
      error?.toString().includes('FedCM') ||
      window.console.logs?.some(log => log.includes('FedCM'))
    ) {
      setError(
        'Google login failed. FedCM may be disabled in your browser settings. Please enable third-party cookies or try a different browser.'
      );
    } else {
      setError(
        'Google login failed. Please try again or use a different browser.'
      );
    }
  };

  // Handle error alert close
  const handleCloseError = () => {
    setError('');
  };

  useEffect(() => {
    if (userInfo) {
      navigate('/app');
    }
  }, []);

  if (userInfo) {
    return null;
  }

  return (
    <LoginContainer maxWidth='xl'>
      <LoginCard elevation={3}>
        <Logo src='/logo.png' alt='Apple FruitX' />

        <Typography variant='h5' component='h1' gutterBottom align='center'>
          Apple FruitX Portal
        </Typography>

        <Typography
          variant='body2'
          color='textSecondary'
          align='center'
          sx={{ mb: 4 }}
        >
          Sign in with your Google account to continue
        </Typography>

        <Box
          position='relative'
          sx={{ width: '100%', display: 'flex', justifyContent: 'center' }}
        >
          <Backdrop
            open={loading}
            sx={{ position: 'absolute', zIndex: 1, borderRadius: 1 }}
          >
            {loading && (
              <CircularProgress size={24} sx={{ color: 'common.white' }} />
            )}
          </Backdrop>

          <GoogleLogin
            theme='filled_blue'
            size='large'
            shape='pill'
            text='signin_with'
            onSuccess={handleGoogleLoginSuccess}
            onError={handleGoogleLoginError}
            useOneTap={false}
          />
        </Box>
      </LoginCard>

      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={handleCloseError}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseError}
          severity='error'
          sx={{ width: '100%' }}
        >
          {error}
        </Alert>
      </Snackbar>
    </LoginContainer>
  );
};

export default LoginPage;
