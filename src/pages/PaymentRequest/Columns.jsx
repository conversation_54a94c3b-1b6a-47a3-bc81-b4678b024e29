import React from 'react';

import { Delete, NoteTwoTone } from '@mui/icons-material';
import { Button, Grid, Link, Typography } from '@mui/material';

import { CustomTooltip } from 'Components';
import { toFixedNumber } from 'Utilities';
import { currency } from 'Utilities/currencyFormatter.jsx';
import { getFormattedDateTime } from 'Utilities/dateUtils';

const DATA = [
  {
    header: 'PR ID',
    key: 'id',
    render: ({ data, rowData: { category_label = '' } = {} }) => {
      return (
        <>
          <div
            style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
          >
            <Typography component='div' color='primary'>
              <b>PR-{data}</b>
            </Typography>
          </div>
          <Typography component='div' variant='caption' color='textPrimary'>
            <b>{category_label}</b>
          </Typography>
          <Typography component='div' variant='caption' color='textPrimary'>
            <b>Type: Bill</b>
          </Typography>
        </>
      );
    },
  },
  {
    header: 'Vendor',
    key: 'vendor',
    style: { width: '12rem' },
    render: ({ rowData: { vendor } }) => {
      return <Typography component='div'>{vendor?.name}</Typography>;
    },
  },
  {
    header: 'Value',
    key: 'amount_raised',
    style: { width: '10rem' },
    render: ({ rowData: { amount, bill } }) => {
      return (
        <>
          <Typography component='div'>
            {currency(toFixedNumber(amount, 2))}
          </Typography>
          <Link color='primary' href={bill} target='_blank'>
            <NoteTwoTone />
            <Typography component='div' variant='caption'>
              Bill
            </Typography>
          </Link>
        </>
      );
    },
  },
  {
    header: 'Request',
    key: 'amount',
    style: { width: '12rem' },
    render: ({ rowData: { created_date, creator_name = '' } }) => {
      return (
        <>
          <Typography component='div'>{creator_name}</Typography>
          <Typography component='div'>
            {getFormattedDateTime(created_date)}
          </Typography>
        </>
      );
    },
  },
  {
    header: 'Balance',
    key: 'outstanding_advance',
    style: { width: '10rem' },
    render: ({ rowData: { outstanding_advance = 0 } }) => {
      return (
        <Typography>
          {currency(toFixedNumber(outstanding_advance, 2))}
        </Typography>
      );
    },
  },
  {
    header: 'Due Date',
    key: 'due_date',
    render: ({ data }) => {
      return (
        <Typography component='div'>{getFormattedDateTime(data)}</Typography>
      );
    },
  },
];

export const PENDING = [
  ...DATA,
  {
    header: 'Action/Details',
    key: 'farmer_name',
    render: ({
      rowData,
      rowData: { creator_name = '', created_date = '' },
      props: {
        approvePaymentRequest = () => {},
        rejectPaymentRequest = () => {},
        openConfirmationPopup = () => {},
        isApprover,
        color,
      },
    }) => {
      return (
        <Grid style={{ display: 'flex', alignItems: 'center' }}>
          <CustomTooltip
            creator_name={creator_name}
            created_date={created_date}
          />
          {!isApprover && (
            <Button
              component='div'
              color='primary'
              cursor='pointer'
              onClick={() => openConfirmationPopup(rowData?.id)}
            >
              <Delete className={`${[color.red_2]}`} />
            </Button>
          )}
          {isApprover && (
            <div
              style={{
                marginLeft: '1rem',
              }}
            >
              <Button
                size='small'
                color='primary'
                style={{
                  width: '100px',
                  marginBottom: '10px',
                  display: 'block',
                }}
                className={`${[color.green_3]} ${[color.green_4]}`}
                onClick={() => approvePaymentRequest(rowData)}
              >
                Approve
              </Button>
              <Button
                size='small'
                style={{
                  width: '100px',
                }}
                className={`${[color.red_1]} ${[color.red_2]}`}
                onClick={() => rejectPaymentRequest(rowData)}
              >
                Reject
              </Button>
            </div>
          )}
        </Grid>
      );
    },
  },
];

export const APPROVED = [
  ...DATA,
  {
    header: 'Action/Details',
    key: 'farmer_name',
    render: ({
      rowData: { creator_name = '', created_date = '', approver_name = '' },
    }) => {
      return (
        <CustomTooltip
          creator_name={creator_name}
          created_date={created_date}
          approver_name={approver_name}
          data='Approved'
        />
      );
    },
  },
];

export const IN_PROCESS = [
  ...DATA,
  {
    header: 'Action/Details',
    key: 'farmer_name',
    render: ({
      rowData: { creator_name = '', created_date = '', approver_name = '' },
    }) => {
      return (
        <CustomTooltip
          creator_name={creator_name}
          created_date={created_date}
          approver_name={approver_name}
          data='Approved'
        />
      );
    },
  },
];

export const PAID_NOT_VERIFIED = [
  ...DATA,
  {
    header: 'Action/Details',
    key: 'farmer_name',
    render: ({
      rowData: {
        id,
        creator_name = '',
        created_date = '',
        approver_name = '',
        finance_approver_name = '',
        paid_date = '',
      },
      props: { openConfirmationPopup = () => {}, isApprover, color },
    }) => {
      return (
        <Grid style={{ display: 'flex', alignItems: 'center' }}>
          <CustomTooltip
            creator_name={creator_name}
            created_date={created_date}
            is_paid_and_approved
            approver_name={approver_name}
            paid_date={paid_date}
          />
          {!isApprover && (
            <Button
              component='div'
              color='primary'
              onClick={() => openConfirmationPopup(id)}
            >
              <Delete className={`${[color.red_2]}`} />
            </Button>
          )}
        </Grid>
      );
    },
  },
];

export const PAID = [
  ...DATA,
  {
    header: 'Action/Details',
    key: 'farmer_name',
    render: ({
      rowData: {
        creator_name = '',
        created_date = '',
        approver_name = '',
        paid_date = '',
      },
    }) => {
      return (
        <CustomTooltip
          creator_name={creator_name}
          created_date={created_date}
          is_paid_and_approved
          approver_name={approver_name}
          paid_date={paid_date}
        />
      );
    },
  },
];

export const REJECTED = [
  ...DATA,
  {
    header: 'Action/Details',
    key: 'farmer_name',
    render: ({
      rowData: { id, creator_name = '', created_date = '', rejector_name = '' },
      props: { openConfirmationPopup = () => {}, isApprover, color },
    }) => {
      return (
        <Grid style={{ display: 'flex', alignItems: 'center' }}>
          <CustomTooltip
            creator_name={creator_name}
            created_date={created_date}
            rejector_name={rejector_name}
            data='Rejected'
          />
          {!isApprover && (
            <Button
              component='div'
              color='primary'
              onClick={() => openConfirmationPopup(id)}
            >
              <Delete className={`${[color.red_2]}`} />
            </Button>
          )}
        </Grid>
      );
    },
  },
];
