import React from 'react';
import { Tooltip } from '@mui/material';
import { InfoOutlined } from '@mui/icons-material';
import Table from 'Components/Table';

const PROCESS_COLUMN = [
  {
    key: 'bill_header',
    header: '<PERSON>',
    align: 'center'
  },
  {
    key: 'total_charge_value',
    header: 'Charge Value',
    align: 'center',
    render: ({ data }) => {
      return data || 0;
    }
  }
];

const ProcessCharges = ({ process_charge_deductions = {} }) => {
  return (
    <>
      <Tooltip
        arrow
        placement="left-start"
        title={
          <Table
            data={process_charge_deductions.charges || []}
            columns={PROCESS_COLUMN}
            header={true}
          />
        }
      >
        <div>
          Process Charges
          <InfoOutlined />:
        </div>
      </Tooltip>
      <div>-{process_charge_deductions.total_deduction}</div>
    </>
  );
};

export default ProcessCharges;
