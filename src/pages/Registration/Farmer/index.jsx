import { useState } from 'react';

import { Paper } from '@mui/material';
import { Form, Formik } from 'formik';
import { useNavigate } from 'react-router-dom';

import { AppButton } from 'Components';
import PageLayout from 'Components/PageLayout';
import useNotify from 'Hooks/useNotify';
import { registerPartner } from 'Services/register';
import { UPI_STATUS } from 'Utilities/constants/registration';
import imageDirectUpload from 'Utilities/directUpload';

import BankDetail from '../components/BankDetails.jsx';
import KycDetail from '../components/KycDetail.jsx';
import PersonalDetail from '../components/PersonalDetail.jsx';
import UpiDetails from '../components/UpiDetails.jsx';
import registrationFileUpload from '../registrationFileUpload';

const FarmerRegistrationForm = ({
  initialValues = {},
  onSubmit,
  toggleModal,
  handleFarmerDetails,
  isModal = false,
}) => {
  const NotificationBar = useNotify();
  const [isLoading, setLoading] = useState(false);
  const navigate = useNavigate();
  const [otpResponse, setOtpResponse] = useState('');

  const submitHandler = async values => {
    setLoading(true);
    const postData = { ...values };
    if (!postData.upi_detail_attributes.upi_id_number) {
      delete postData.upi_detail_attributes;
    }
    const { kyc_docs_attributes = [], bank_detail_attributes = {} } = values;

    if (bank_detail_attributes?.bank_attachments?.length) {
      await Promise.all(
        bank_detail_attributes?.bank_attachments?.map(value =>
          value ? imageDirectUpload(value) : Promise.resolve()
        )
      ).then(res => {
        bank_detail_attributes.bank_attachments = res
          .filter(Boolean)
          .map(({ data }) => data?.signed_id);
      });
    }

    const fileData = await registrationFileUpload(kyc_docs_attributes);
    const { kyc_docs = [] } = await fileData;

    const requestBody = {
      partner: {
        ...postData,
        kyc_docs_attributes: kyc_docs,
        bank_detail_attributes,
        roles: 'Farmer',
        otp_request_id: otpResponse || null,
      },
    };

    const processFarmer = onSubmit || registerPartner;

    processFarmer(requestBody)
      .then(res => {
        if (res && !onSubmit) {
          handleFarmerDetails && handleFarmerDetails(res);
        }
        NotificationBar(
          `Farmer ${onSubmit ? 'updated' : 'created'} successfully.`
        );
        !toggleModal && backHandler();
      })
      .finally(() => {
        toggleModal && toggleModal();
        setLoading(false);
      });
  };

  const backHandler = () => {
    navigate(-1);
  };

  const formContent = (
    <Formik
      enableReinitialize
      initialValues={{
        ...initialValues,
        bank_detail_attributes: initialValues?.bank_detail_attributes
          ? initialValues?.bank_detail_attributes
          : {
              account_number: '',
              account_number_confirmation: '',
              ifsc: '',
            },
        kyc_docs_attributes: initialValues?.kyc_docs_attributes?.length
          ? initialValues.kyc_docs_attributes
          : [{ doc_category: '', doc_type: '', kyc_attachments: '' }],
        upi_detail_attributes: initialValues?.upi_detail_attributes
          ? initialValues?.upi_detail_attributes
          : {
              upi_id_number: '',
              status: UPI_STATUS.PENDING,
            },
      }}
      onSubmit={submitHandler}
    >
      {({ handleSubmit, handleReset }) => (
        <Form>
          {isModal ? (
            <>
              <Paper
                elevation={0}
                style={{ flex: 1, padding: '1rem', marginBottom: '1rem' }}
              >
                <PersonalDetail setOtpResponse={setOtpResponse} />
                <KycDetail />
                <BankDetail />
                <UpiDetails />
              </Paper>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  gap: '1rem',
                  padding: '1rem 0',
                }}
              >
                <AppButton
                  variant='contained'
                  color='inherit'
                  data-cy='mandi.farmerRegistration.cancelButton'
                  onClick={() => {
                    handleReset();
                    toggleModal && toggleModal();
                  }}
                >
                  Cancel
                </AppButton>
                <AppButton
                  variant='contained'
                  loading={isLoading}
                  type='submit'
                  data-cy='mandi.farmerRegistration.saveButton'
                  onClick={handleSubmit}
                >
                  Save
                </AppButton>
              </div>
            </>
          ) : (
            <>
              <PageLayout.Body>
                <Paper elevation={0} style={{ flex: 1, padding: '1rem' }}>
                  <PersonalDetail setOtpResponse={setOtpResponse} />
                  <KycDetail />
                  <BankDetail />
                  <UpiDetails />
                </Paper>
              </PageLayout.Body>
              <PageLayout.Footer>
                <AppButton
                  variant='contained'
                  color='inherit'
                  sx={{ mr: 1 }}
                  data-cy='mandi.farmerRegistration.cancelButton'
                  onClick={() => {
                    backHandler();
                  }}
                >
                  Cancel
                </AppButton>
                <AppButton
                  variant='contained'
                  loading={isLoading}
                  type='submit'
                  data-cy='mandi.farmerRegistration.saveButton'
                  onClick={handleSubmit}
                >
                  Save
                </AppButton>
              </PageLayout.Footer>
            </>
          )}
        </Form>
      )}
    </Formik>
  );

  return isModal ? (
    formContent
  ) : (
    <PageLayout title='Farmer Registration'>{formContent}</PageLayout>
  );
};

export default FarmerRegistrationForm;
