import React, { useState } from 'react';

import { Paper, Typography } from '@mui/material';
import { Form, Formik } from 'formik';
import { useNavigate } from 'react-router-dom';

import { AppButton } from 'Components';
import { FieldInput } from 'Components/FormFields';
import PageLayout from 'Components/PageLayout';
import useNotify from 'Hooks/useNotify';
import { registerPartner } from 'Services/register';
import imageDirectUpload from 'Utilities/directUpload';

import KycDetail from '../components/KycDetail';
import PersonalDetail from '../components/PersonalDetail';
import { StyledTextInput } from '../components/StyledTextinput';
import registrationFileUpload from '../registrationFileUpload';

const CustomerRegistrationForm = ({ initialValues = {}, onSubmit }) => {
  const [isLoading, setLoading] = useState(false);
  const NotificationBar = useNotify();
  const navigate = useNavigate();

  const submitHandler = async values => {
    setLoading(true);
    const postData = { ...values };
    const { kyc_docs_attributes, bank_detail_attributes } = values;

    if (bank_detail_attributes?.bank_attachments?.length) {
      await Promise.all(
        bank_detail_attributes?.bank_attachments?.map(value => {
          return value ? imageDirectUpload(value) : Promise.resolve();
        })
      ).then(res => {
        postData.bank_detail_attributes = {
          ...bank_detail_attributes,
          bank_attachments: res
            .filter(Boolean)
            .map(({ data }) => data?.signed_id),
        };
      });
    }

    const fileData = await registrationFileUpload(kyc_docs_attributes);
    const { kyc_docs = [] } = await fileData;

    const requestBody = {
      partner: {
        ...postData,
        kyc_docs_attributes: kyc_docs,
        roles: 'Customer',
      },
    };

    const processCustomer = onSubmit || registerPartner;

    processCustomer(requestBody)
      .then(() => {
        NotificationBar(
          `Customer ${onSubmit ? 'updated' : 'created'} successfully.`
        );
        backHandler();
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const backHandler = () => {
    navigate('/app/home');
  };

  return (
    <PageLayout title='Customer Registration'>
      <Formik
        enableReinitialize
        initialValues={{
          ...initialValues,
          kyc_docs_attributes: initialValues?.kyc_docs_attributes?.length
            ? initialValues.kyc_docs_attributes
            : [{ doc_type: '', kyc_attachments: '' }],
        }}
        onSubmit={submitHandler}
      >
        {({ handleSubmit, handleReset }) => (
          <>
            <Form>
              <PageLayout.Body>
                <Paper style={{ flex: 1, padding: '1rem' }}>
                  <PersonalDetail />
                  <StyledTextInput>
                    <Typography variant='subtitle1' component='div'>
                      POC Name:
                    </Typography>
                    <div>
                      <FieldInput
                        name='name'
                        size='small'
                        label='Name'
                        placeholder='Name'
                        variant='outlined'
                        style={{ width: '100%' }}
                        InputLabelProps={{
                          shrink: true,
                        }}
                      />
                    </div>
                  </StyledTextInput>

                  <KycDetail />
                </Paper>
              </PageLayout.Body>
              <PageLayout.Footer>
                <AppButton
                  variant='contained'
                  color='inherit'
                  className='margin-horizontal'
                  onClick={() => {
                    handleReset();
                    backHandler();
                  }}
                >
                  Cancel
                </AppButton>
                <AppButton
                  variant='contained'
                  color='primary'
                  className='margin-horizontal'
                  loading={isLoading}
                  type='submit'
                  onClick={handleSubmit}
                >
                  Save
                </AppButton>
              </PageLayout.Footer>
            </Form>
          </>
        )}
      </Formik>
    </PageLayout>
  );
};

export default CustomerRegistrationForm;
