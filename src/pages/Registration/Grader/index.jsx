import { useState } from 'react';

import { Paper, Typography } from '@mui/material';
import { Form, Formik } from 'formik';
import { useNavigate } from 'react-router-dom';

import { useSiteValue } from 'App/SiteContext';
import { AppButton } from 'Components';
import { FieldCombo } from 'Components/FormFields';
import PageLayout from 'Components/PageLayout';
import useNotify from 'Hooks/useNotify';
import { getPartnerList, registerPartner } from 'Services/register';
import { filterOptions } from 'Utilities';
import { UPI_STATUS } from 'Utilities/constants/registration';

import KycDetail from '../components/KycDetail';
import PersonalDetail from '../components/PersonalDetail';
import { StyledTextInput } from '../components/StyledTextinput';
import UpiDetails from '../components/UpiDetails';
import registrationFileUpload from '../registrationFileUpload';
import { StyledBankTile } from '../styles';

const GraderRegistrationForm = ({ initialValues = {}, onSubmit }) => {
  const NotificationBar = useNotify();
  const [isLoading, setLoading] = useState(false);
  const [vendors, setVendors] = useState([]);
  const { mandiList } = useSiteValue();
  const navigate = useNavigate();
  const [otpResponse, setOtpResponse] = useState('');

  const submitHandler = async values => {
    setLoading(true);
    const postData = { ...values };
    if (!postData.upi_detail_attributes.upi_id_number) {
      delete postData.upi_detail_attributes;
    }
    const { kyc_docs_attributes, mandis } = values;

    const fileData = await registrationFileUpload(kyc_docs_attributes);
    const { kyc_docs = [] } = await fileData;

    const requestBody = {
      partner: {
        ...postData,
        kyc_docs_attributes: kyc_docs,
        mandi_ids: mandis?.map(({ id }) => id),
        roles: 'Grader',
        otp_request_id: otpResponse || null,
      },
    };

    const processGrader = onSubmit || registerPartner;

    processGrader(requestBody)
      .then(() => {
        NotificationBar(
          `Grader ${onSubmit ? 'updated' : 'created'} successfully.`
        );
        backHandler();
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const backHandler = () => {
    const redirectToPath = onSubmit ? 'registration/partner-list' : 'home';
    navigate(`/app/${redirectToPath}`);
  };

  const getUpdateVendors = async q => {
    const { items } = await getPartnerList('vendor', { q });
    setVendors(items);
  };

  return (
    <PageLayout title='Grader Registration'>
      <Formik
        enableReinitialize
        initialValues={{
          ...initialValues,
          mandis: mandiList.filter(mandi =>
            initialValues?.mandis?.find(m => m.id === mandi.id)
          ),
          kyc_docs_attributes: initialValues?.kyc_docs_attributes?.length
            ? initialValues.kyc_docs_attributes
            : [{ doc_category: '', doc_type: '', kyc_attachments: '' }],
          upi_detail_attributes: initialValues?.upi_detail_attributes
            ? initialValues?.upi_detail_attributes
            : {
                upi_id_number: '',
                status: UPI_STATUS.PENDING,
              },
        }}
        onSubmit={submitHandler}
      >
        {({ handleSubmit, handleReset }) => (
          <>
            <PageLayout.Body>
              <Form>
                <Paper style={{ flex: 1, padding: '1rem' }}>
                  <PersonalDetail setOtpResponse={setOtpResponse} />
                  <StyledBankTile>
                    <Typography variant='subtitle1' component='div'>
                      Vendor:
                    </Typography>
                    <div
                      style={{
                        width: '22%',
                      }}
                    >
                      <FieldCombo
                        name='vendor'
                        label='Vendor'
                        size='small'
                        variant='outlined'
                        options={vendors}
                        filterOptions={filterOptions}
                        InputLabelProps={{
                          shrink: true,
                        }}
                        inputProps={{
                          'data-cy': 'mandi-registration-vendor',
                        }}
                        onChangeInput={getUpdateVendors}
                      />
                    </div>
                  </StyledBankTile>
                  <StyledTextInput>
                    <Typography variant='subtitle1' component='div'>
                      Mandi:
                    </Typography>
                    <div>
                      <FieldCombo
                        name='mandis'
                        label='Mandi'
                        size='small'
                        variant='outlined'
                        multiple
                        InputLabelProps={{
                          shrink: true,
                        }}
                        style={{ width: '50%' }}
                        showNone={false}
                        inputProps={{
                          'data-cy': 'mandi-registration-mandi',
                        }}
                        options={mandiList || []}
                      />
                    </div>
                  </StyledTextInput>
                  <KycDetail />
                  <UpiDetails />
                </Paper>
              </Form>
            </PageLayout.Body>
            <PageLayout.Footer>
              <AppButton
                variant='contained'
                color='inherit'
                className='margin-horizontal'
                data-cy='mandi-registration-grader-cancel'
                onClick={() => {
                  handleReset();
                  backHandler();
                }}
              >
                Cancel
              </AppButton>
              <AppButton
                variant='contained'
                color='primary'
                className='margin-horizontal'
                loading={isLoading}
                type='submit'
                data-cy='mandi-registration-grader-save'
                onClick={handleSubmit}
              >
                Save
              </AppButton>
            </PageLayout.Footer>
          </>
        )}
      </Formik>
    </PageLayout>
  );
};

export default GraderRegistrationForm;
