import { Paper } from '@mui/material';
import { Formik } from 'formik';
import { useNavigate } from 'react-router-dom';

import { AppButton } from 'Components';
import { FieldRadio } from 'Components/FormFields';
import PageLayout from 'Components/PageLayout';
import { REGISTRATION_TYPES } from 'Utilities/constants/registration';

import { FieldWrapper } from './Styled';

const initialValues = {
  registrationCategory: REGISTRATION_TYPES.FARMER.value,
};

const RegistrationType = () => {
  const navigate = useNavigate();

  const handleNext = values => {
    navigate(`/app/registration/${values.registrationCategory}`);
  };

  const backHandler = () => {
    navigate(-1);
  };

  return (
    <PageLayout title='Registration Type'>
      <Formik
        enableReinitialize
        initialValues={initialValues}
        onSubmit={handleNext}
      >
        {({ handleSubmit }) => (
          <>
            <PageLayout.Body>
              <Paper elevation={0} sx={{ flex: 1 }}>
                <FieldWrapper>
                  <FieldRadio
                    name='registrationCategory'
                    size='small'
                    label=''
                    options={Object.values(REGISTRATION_TYPES)}
                    className='registration_category'
                    data-cy='mandi.registrationType.radioGroup'
                  />
                </FieldWrapper>
              </Paper>
            </PageLayout.Body>
            <PageLayout.Footer>
              <AppButton
                variant='contained'
                color='inherit'
                onClick={backHandler}
                sx={{ mr: 1 }}
                data-cy='mandi.registrationType.cancelButton'
              >
                Cancel
              </AppButton>
              <AppButton
                variant='contained'
                type='submit'
                onClick={handleSubmit}
                data-cy='mandi.registrationType.nextButton'
              >
                Next
              </AppButton>
            </PageLayout.Footer>
          </>
        )}
      </Formik>
    </PageLayout>
  );
};

export default RegistrationType;
