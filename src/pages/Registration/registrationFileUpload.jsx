import imageDirectUpload from 'Utilities/directUpload';

const registrationFileUpload = async kyc_docs_attributes => {
  const kyc_docs = await Promise.all(
    kyc_docs_attributes
      ?.filter(({ id }) => !id)
      ?.map(async item => {
        const {
          kyc_attachments,
          doc_category,
          doc_type,
          identification_number,
          fleet_size,
          financial_year,
        } = item;
        const data = {
          ...(kyc_attachments?.[0]?.name ? { kyc_attachments } : {}),
        };

        await Promise.all(
          Object.entries(data).map(async ([key, items]) => {
            return await Promise.all(
              items?.map(item => imageDirectUpload(item))
            ).then(res => {
              data[key] = res
                .filter(Boolean)
                .map(({ data: dt }) => dt?.signed_id);
            });
          })
        );

        return new Promise(resolve => {
          resolve({
            doc_type,
            doc_category,
            identification_number,
            fleet_size,
            financial_year,
            ...(Object.entries(data).reduce((acc, [key, value]) => {
              if (value.length) {
                acc[key] = [value[0]];
              }
              return acc;
            }, {}) || {}),
          });
        });
      })
  );

  return new Promise(resolve => {
    resolve({ kyc_docs });
  });
};

export default registrationFileUpload;
