import { Typography } from '@mui/material';
import { useFormikContext } from 'formik';

import { ImageThumb } from 'Components';
import { FieldInput, UploadInput } from 'Components/FormFields';
import { mergeValidator } from 'Utilities';
import { DOCUMENT_STATUS } from 'Utilities/constants/kycTypes';
import {
  validateEquals,
  validateIfsc,
  validateRequired,
} from 'Utilities/formvalidation';

import { GridContainer, ImageListWrapper } from '../Styled';
import { ImageStatus, ImageThumbWrapper, StyledBankTile } from '../styles';

const BankDetail = () => {
  const { values, setFieldValue } = useFormikContext();
  const { bank_attachments, status } = values?.bank_detail_attributes || {};

  return (
    <div>
      <Typography variant='h6' component='div' style={{ marginBottom: '12px' }}>
        <b>BANK Details:</b>
      </Typography>
      <StyledBankTile>
        <Typography variant='subtitle1' component='div'>
          Account Number:
        </Typography>
        <div>
          <FieldInput
            name='bank_detail_attributes.account_number'
            size='small'
            required
            variant='outlined'
            InputLabelProps={{
              shrink: true,
            }}
            inputProps={{
              'data-cy': 'mandi-registration-accountNumber',
            }}
          />
        </div>
      </StyledBankTile>
      <StyledBankTile>
        <Typography variant='subtitle1' component='div'>
          Account Number:
          <br />
          Confirmation
        </Typography>
        <div>
          <FieldInput
            name='bank_detail_attributes.account_number_confirmation'
            validate={
              values?.bank_detail_attributes?.account_number &&
              mergeValidator(
                validateRequired,
                validateEquals(values?.bank_detail_attributes?.account_number)
              )
            }
            size='small'
            required
            variant='outlined'
            InputLabelProps={{
              shrink: true,
            }}
            inputProps={{
              'data-cy': 'mandi-registration-accountNumberConfirmation',
            }}
          />
        </div>
      </StyledBankTile>
      <StyledBankTile>
        <Typography variant='subtitle1' component='div'>
          IFSC Code:
        </Typography>
        <div>
          <FieldInput
            name='bank_detail_attributes.ifsc'
            size='small'
            required
            validate={
              values?.bank_detail_attributes?.account_number && validateIfsc
            }
            variant='outlined'
            inputProps={{
              style: { textTransform: 'uppercase' },
              'data-cy': 'mandi-registration-ifscCode',
            }}
            InputLabelProps={{
              shrink: true,
            }}
            onChange={e => {
              setFieldValue(
                'bank_detail_attributes.ifsc',
                e.target.value.toUpperCase()
              );
            }}
          />
        </div>
      </StyledBankTile>
      <StyledBankTile>
        <Typography variant='subtitle1' component='div'>
          Beneficiary Name:
        </Typography>
        <div>
          <FieldInput
            name='bank_detail_attributes.beneficiary_name'
            size='small'
            required
            variant='outlined'
            InputLabelProps={{
              shrink: true,
            }}
            inputProps={{
              'data-cy': 'mandi-registration-beneficiaryName',
            }}
          />
        </div>
      </StyledBankTile>
      <StyledBankTile>
        <div>
          <Typography variant='subtitle1' component='div'>
            Bank Attachments:
          </Typography>
        </div>

        <UploadInput
          accept='image/*, application/pdf'
          name='bank_detail_attributes.bank_attachments'
          multiple
          required
          inputProps={{
            'data-cy': 'mandi-registration-bankAttachments',
          }}
        />

        {bank_attachments && bank_attachments?.length > 0 && (
          <GridContainer
            container
            direction='row'
            alignItems='center'
            spacing={0}
          >
            <ImageListWrapper>
              {bank_attachments?.map((_file, index) => (
                <ImageThumbWrapper key={index}>
                  <ImageThumb file={_file} url={_file} />
                  <ImageStatus verified={status === DOCUMENT_STATUS.VERIFIED}>
                    {status}
                  </ImageStatus>
                </ImageThumbWrapper>
              ))}
            </ImageListWrapper>
          </GridContainer>
        )}
      </StyledBankTile>
    </div>
  );
};

export default BankDetail;
