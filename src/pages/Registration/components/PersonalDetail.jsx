import { useEffect, useRef, useState } from 'react';

import { Edit } from '@mui/icons-material';
import { Box, Button, InputAdornment, Typography } from '@mui/material';
import { useFormikContext } from 'formik';

import { useSiteValue } from 'App/SiteContext';
import { AppButton, OTP } from 'Components';
import { FieldInput, FieldSelect } from 'Components/FormFields';
import { sendOtp, verifyOtp } from 'Services/otp';
import { getDistrictsList, getStatesList } from 'Services/register';
import { mergeValidator } from 'Utilities';
import { LANGUAGE_OPTIONS } from 'Utilities/constants/lots';
import {
  validateLanguage,
  validatePhone,
  validateRequired,
} from 'Utilities/formvalidation';

import { StyledTextInput } from './StyledTextinput';

const PersonalDetail = ({ setOtpResponse }) => {
  const { values } = useFormikContext();
  const [states, setState] = useState();
  const [districts, setDistricts] = useState();
  const [showOtpPage, setShowOtpPage] = useState(false);
  const [isOtpIncorrect, setIsOtpIncorrect] = useState(false);
  const otpVerifiedRef = useRef(false);
  const verifyOtpId = useRef();
  const { userInfo } = useSiteValue;

  useEffect(() => {
    getStatesList().then(({ states = [] }) => {
      setState(states);
    });
  }, []);

  useEffect(() => {
    if (values.otp_request_details?.id) {
      otpVerifiedRef.current = true;
    }
    if (values?.state) {
      (async () => {
        const { districts } = await getDistrictsList(values?.state);
        setDistricts(districts);
      })();
    }
  }, [values?.state]);

  const handleOtp = otp => {
    const data = {
      otp,
      id: verifyOtpId.current,
    };
    verifyOtp(data)
      .then(({ responseData = {} }) => {
        otpVerifiedRef.current = true;
        setOtpResponse(responseData.id);
        setShowOtpPage(false);
      })
      .catch(() => {
        setIsOtpIncorrect(true);
      });
  };

  const sendOtpToUser = () => {
    const localVal =
      LANGUAGE_OPTIONS.find(i => i.value === values.preferred_language)?.key ||
      'en';
    const data = {
      phone_number: values.phone_number,
      transaction_type: 'VENDOR_REGISTRATION',
      locale: localVal,
      current_user_id: userInfo?.id,
    };
    sendOtp(data).then(({ responseData = {} }) => {
      const { id } = responseData;
      verifyOtpId.current = id;
    });
  };

  const handleSendOtp = () => {
    if (!otpVerifiedRef.current) {
      setShowOtpPage(true);
      sendOtpToUser();
    }
  };

  const editPhoneNumber = () => {
    otpVerifiedRef.current = false;
    setState([]);
  };

  return (
    <div>
      {!!showOtpPage && (
        <OTP
          sendOtpToUser={sendOtpToUser}
          handleClose={() => setShowOtpPage(false)}
          handleOtp={handleOtp}
          isOtpIncorrect={isOtpIncorrect}
          setIsOtpIncorrect={setIsOtpIncorrect}
          phoneNumber={values.phone_number}
        />
      )}
      <StyledTextInput>
        <Typography variant='subtitle1' component='div'>
          Name:
        </Typography>
        <div>
          <FieldInput
            name='name'
            size='small'
            label=''
            placeholder='Enter Name'
            variant='outlined'
            validate={validateRequired}
            InputLabelProps={{
              shrink: true,
            }}
            inputProps={{
              'data-cy': 'mandi.registration.name',
            }}
            fullWidth
          />
        </div>
      </StyledTextInput>
      <StyledTextInput>
        <Typography variant='subtitle1' component='div'>
          Phone:
        </Typography>{' '}
        <div style={{ display: 'flex' }}>
          <Box display='flex' width='45%'>
            <FieldInput
              name='phone_number'
              size='small'
              label='Phone'
              required
              validate={mergeValidator(validateRequired, validatePhone)}
              variant='outlined'
              style={{
                width: '100%',
                opacity: otpVerifiedRef.current ? 0.4 : 1,
              }}
              disabled={otpVerifiedRef.current}
              inputProps={{
                maxLength: 10,
                'data-cy': 'mandi.registration.phone',
              }}
              InputLabelProps={{
                required: true,
                shrink: true,
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position='start'>+91</InputAdornment>
                ),
              }}
            />
            {otpVerifiedRef.current && (
              <Button onClick={editPhoneNumber}>
                <Edit />
              </Button>
            )}
          </Box>
          <AppButton
            variant='contained'
            color='primary'
            className='normal-text'
            disabled={
              !values?.preferred_language ||
              values.phone_number?.length !== 10 ||
              otpVerifiedRef.current
            }
            data-cy='mandi.registration.sendOtp'
            onClick={handleSendOtp}
          >
            {otpVerifiedRef.current ? 'Verified' : 'Send Code'}
          </AppButton>

          <FieldInput
            name='whatsapp_number'
            size='small'
            label='Whatsapp'
            variant='outlined'
            style={{ flexGrow: 1 }}
            InputLabelProps={{
              shrink: true,
            }}
            validate={validatePhone}
            InputProps={{
              startAdornment: (
                <InputAdornment position='start'>+91</InputAdornment>
              ),
            }}
            inputProps={{
              maxLength: 10,
              'data-cy': 'mandi.registration.whatsapp',
            }}
          />
        </div>
      </StyledTextInput>
      <StyledTextInput>
        <Typography variant='subtitle1' component='div'>
          Location:
        </Typography>
        <div style={{ display: 'flex' }}>
          <FieldSelect
            name='state'
            size='small'
            label='State'
            variant='outlined'
            showNone={false}
            SelectDisplayProps={{ 'data-cy': 'mandi.registration.state' }}
            options={
              states?.map(state => ({ text: state, value: state })) || []
            }
            InputLabelProps={{
              shrink: true,
            }}
          />
          <FieldSelect
            name='district'
            size='small'
            label='District'
            variant='outlined'
            disabled={!values.state}
            SelectDisplayProps={{ 'data-cy': 'mandi.registration.district' }}
            options={
              districts?.map(district => ({
                text: district,
                value: district,
              })) || []
            }
            InputLabelProps={{
              shrink: true,
            }}
          />
          <FieldInput
            name='village'
            size='small'
            label='Village'
            variant='outlined'
            style={{ width: '100%' }}
            inputProps={{
              'data-cy': 'mandi.registration.village',
            }}
            InputLabelProps={{
              shrink: true,
            }}
          />
          <FieldInput
            name='mandal'
            size='small'
            label='Mandal'
            variant='outlined'
            style={{ width: '100%' }}
            inputProps={{
              'data-cy': 'mandi.registration.mandal',
            }}
            InputLabelProps={{
              shrink: true,
            }}
          />
        </div>
      </StyledTextInput>
      <StyledTextInput>
        <Typography variant='subtitle1' component='div'>
          Language:
        </Typography>
        <div>
          <FieldSelect
            name='preferred_language'
            size='small'
            label='Language'
            variant='outlined'
            showNone={false}
            style={{ width: '50%' }}
            SelectDisplayProps={{ 'data-cy': 'mandi.registration.language' }}
            options={LANGUAGE_OPTIONS?.map(
              ({ text = 'English', value = 1 }) => ({ text, value })
            )}
            required
            validate={validateLanguage}
            InputLabelProps={{
              shrink: true,
            }}
          />
        </div>
      </StyledTextInput>
    </div>
  );
};

export default PersonalDetail;
