import { useEffect, useRef } from 'react';

import { Typography } from '@mui/material';
import { FieldArray, useFormikContext, getIn } from 'formik';

import { AppButton, ImageThumb } from 'Components';
import { FieldInput, FieldSelect, UploadInput } from 'Components/FormFields';
import {
  DOCUMENT_STATUS,
  KYC_TYPE,
  KYC_CATEGORY,
  FLEET_SIZE,
} from 'Utilities/constants/kycTypes';
import { validateRequired } from 'Utilities/formvalidation';

import { GridContainer, ImageListWrapper } from '../Styled';
import { ImageStatus, ImageThumbWrapper, JustifyFileSection } from '../styles';

const KycFieldReset = ({ index }) => {
  const { values, setFieldValue } = useFormikContext();
  const prevDocCategoryRef = useRef();
  const prevDocTypeRef = useRef();

  const docCategory = getIn(
    values,
    `kyc_docs_attributes.${index}.doc_category`
  );
  const docType = getIn(values, `kyc_docs_attributes.${index}.doc_type`);

  const prevDocCategory = prevDocCategoryRef.current;
  const prevDocType = prevDocTypeRef.current;

  useEffect(() => {
    if (prevDocCategory && prevDocCategory !== docCategory) {
      setFieldValue(`kyc_docs_attributes.${index}.doc_type`, '');
      setFieldValue(`kyc_docs_attributes.${index}.identification_number`, '');
      setFieldValue(`kyc_docs_attributes.${index}.fleet_size`, '');
      setFieldValue(`kyc_docs_attributes.${index}.financial_year`, '');
    }

    if (prevDocType && prevDocType !== docType) {
      setFieldValue(`kyc_docs_attributes.${index}.identification_number`, '');
    }

    prevDocCategoryRef.current = docCategory;
    prevDocTypeRef.current = docType;
  }, [docCategory, docType, index, setFieldValue]);

  return null;
};

const KycCategoryOptions = Object.values(KYC_CATEGORY).map(value => ({
  text: value,
  value,
}));

const currentYear = new Date().getFullYear();
const financialYears = Array.from({ length: 5 }, (_, i) => {
  const start = currentYear - i;
  const end = start + 1;
  return {
    text: `${start}-${String(end).slice(-2)}`,
    value: `${start}-${String(end).slice(-2)}`,
  };
});

const KycDetail = () => {
  const { values } = useFormikContext();
  const { kyc_docs_attributes } = values;
  const isLargeFleetTransporter = index => {
    return (
      kyc_docs_attributes?.[index]?.doc_category ===
        KYC_CATEGORY.TRANSPORTER_DECLARATION &&
      kyc_docs_attributes?.[index]?.fleet_size === FLEET_SIZE[0].value
    );
  };

  return (
    <div>
      <Typography variant='h6' component='div' style={{ marginBottom: '12px' }}>
        <b>KYC Details:</b>
      </Typography>
      <FieldArray
        name='kyc_docs_attributes'
        render={arrayHelpers => (
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            {kyc_docs_attributes?.map((kyc, index) => {
              const docCategory = kyc?.doc_category;
              let docTypeOptions = [];

              if (docCategory === KYC_CATEGORY.MSME_DOCUMENT) {
                docTypeOptions = Object.entries(KYC_TYPE.MSME_DOCUMENT).map(
                  ([key, value]) => ({
                    text: value,
                    value: value,
                  })
                );
              } else if (docCategory === KYC_CATEGORY.ADDRESS_PROOF) {
                docTypeOptions = Object.entries(KYC_TYPE.ADDRESS_PROOF).map(
                  ([key, value]) => ({
                    text: value,
                    value: value,
                  })
                );
              }

              return (
                <div style={{ marginBottom: '12px' }} key={index}>
                  <KycFieldReset index={index} />
                  <JustifyFileSection>
                    <Typography variant='subtitle1' component='div'>
                      Document Category:
                    </Typography>
                    <div>
                      <FieldSelect
                        name={`kyc_docs_attributes.${index}.doc_category`}
                        size='small'
                        noLabel
                        placeholder='Category'
                        variant='outlined'
                        showNone={false}
                        options={KycCategoryOptions}
                        disabled={kyc.id}
                        InputLabelProps={{ shrink: true }}
                        style={{ width: '200px' }}
                        SelectDisplayProps={{
                          'data-cy': `mandi-registration-kycCategory-${index}`,
                        }}
                      />
                    </div>
                  </JustifyFileSection>

                  {docTypeOptions.length > 0 && (
                    <JustifyFileSection>
                      <Typography variant='subtitle1' component='div'>
                        Document Type:
                      </Typography>
                      <div>
                        <FieldSelect
                          name={`kyc_docs_attributes.${index}.doc_type`}
                          size='small'
                          noLabel
                          placeholder='Category'
                          variant='outlined'
                          showNone={false}
                          options={docTypeOptions}
                          disabled={kyc.id}
                          validate={validateRequired}
                          InputLabelProps={{ shrink: true }}
                          style={{ width: '200px' }}
                          SelectDisplayProps={{
                            'data-cy': `mandi-registration-kycType-${index}`,
                          }}
                        />
                      </div>
                    </JustifyFileSection>
                  )}

                  {values?.kyc_docs_attributes?.[index]?.doc_category ===
                    KYC_CATEGORY.TRANSPORTER_DECLARATION && (
                    <>
                      <JustifyFileSection>
                        <Typography variant='subtitle1' component='div'>
                          Type:
                        </Typography>
                        <div>
                          <FieldSelect
                            name={`kyc_docs_attributes.${index}.fleet_size`}
                            size='small'
                            noLabel
                            placeholder='Transporter Type'
                            variant='outlined'
                            showNone={false}
                            options={FLEET_SIZE}
                            disabled={kyc.id}
                            validate={validateRequired}
                            InputLabelProps={{ shrink: true }}
                            style={{ width: '200px' }}
                            SelectDisplayProps={{
                              'data-cy': `mandi-registration-fleetSize-${index}`,
                            }}
                          />
                        </div>
                      </JustifyFileSection>

                      <JustifyFileSection>
                        <Typography variant='subtitle1' component='div'>
                          Financial Year:
                        </Typography>
                        <div>
                          <FieldSelect
                            name={`kyc_docs_attributes.${index}.financial_year`}
                            size='small'
                            noLabel
                            placeholder='Financial Year'
                            variant='outlined'
                            showNone={false}
                            options={financialYears}
                            disabled={kyc.id}
                            validate={validateRequired}
                            InputLabelProps={{ shrink: true }}
                            style={{ width: '200px' }}
                            SelectDisplayProps={{
                              'data-cy': `mandi-registration-financialYear-${index}`,
                            }}
                          />
                        </div>
                      </JustifyFileSection>
                    </>
                  )}

                  {((values?.kyc_docs_attributes?.[index]?.doc_category ===
                    KYC_CATEGORY.ADDRESS_PROOF &&
                    values?.kyc_docs_attributes?.[index]?.doc_type ===
                      KYC_TYPE.ADDRESS_PROOF.GST_CERT) ||
                    values?.kyc_docs_attributes?.[index]?.doc_category ===
                      KYC_CATEGORY.PAN) && (
                    <JustifyFileSection>
                      <Typography variant='subtitle1' component='div'>
                        {values.kyc_docs_attributes[index].doc_category ===
                        KYC_CATEGORY.PAN
                          ? 'PAN Number'
                          : 'GST Number'}
                      </Typography>
                      <div>
                        <FieldInput
                          name={`kyc_docs_attributes.${index}.identification_number`}
                          size='small'
                          required
                          variant='outlined'
                          disabled={kyc.id}
                          validate={validateRequired}
                          InputLabelProps={{ shrink: true, required: true }}
                          inputProps={{
                            'data-cy': `mandi-registration-identificationNumber-${index}`,
                          }}
                          style={{ width: '200px' }}
                        />
                      </div>
                    </JustifyFileSection>
                  )}

                  <JustifyFileSection>
                    {kyc_docs_attributes?.[index]?.kyc_attachments &&
                      kyc_docs_attributes?.[index]?.kyc_attachments?.length && (
                        <>
                          <Typography variant='subtitle1' component='div'>
                            File:
                          </Typography>
                          <GridContainer
                            container
                            direction='row'
                            alignItems='center'
                            spacing={0}
                            style={{ padding: 0 }}
                          >
                            <ImageListWrapper style={{ margin: 0 }}>
                              {kyc_docs_attributes?.[
                                index
                              ]?.kyc_attachments?.map((_file, index) => (
                                <ImageThumbWrapper key={index}>
                                  <ImageThumb file={_file} url={_file} />
                                  <ImageStatus
                                    verified={
                                      kyc?.status === DOCUMENT_STATUS.VERIFIED
                                    }
                                  >
                                    {kyc?.status}
                                  </ImageStatus>
                                </ImageThumbWrapper>
                              ))}
                            </ImageListWrapper>
                          </GridContainer>
                        </>
                      )}
                  </JustifyFileSection>

                  {kyc_docs_attributes?.[index]?.status ===
                    DOCUMENT_STATUS.REJECTED && (
                    <JustifyFileSection>
                      <Typography variant='subtitle1' component='div'>
                        Comments:
                      </Typography>
                      <Typography
                        variant='subtitle1'
                        component='div'
                        style={{ marginLeft: '10px' }}
                      >
                        {kyc_docs_attributes?.[index]?.reject_reason}
                      </Typography>
                    </JustifyFileSection>
                  )}

                  {!kyc.id && (
                    <div style={{ display: 'flex' }}>
                      <AppButton
                        color='error'
                        variant='outlined'
                        className='margin-horizontal'
                        onClick={() => arrayHelpers.remove(index)}
                        data-cy={`mandi-registration-removeKYC-${index}`}
                      >
                        - Remove Kyc
                      </AppButton>
                      <div>
                        <UploadInput
                          accept='image/*, application/pdf'
                          name={`kyc_docs_attributes.${index}.kyc_attachments`}
                          multiple
                          disabled={!kyc_docs_attributes?.[index]?.doc_category}
                          required={!isLargeFleetTransporter(index)}
                          validate={
                            kyc_docs_attributes?.[index]?.doc_category &&
                            !isLargeFleetTransporter(index) &&
                            validateRequired
                          }
                          data-cy={`mandi-registration-uploadKYC-${index}`}
                        />
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
            <div>
              <AppButton
                color='primary'
                variant='contained'
                className='margin-horizontal'
                data-cy='mandi-registration-addKYC'
                onClick={() =>
                  arrayHelpers.push({
                    doc_category: '',
                    doc_type: '',
                    kyc_attachments: '',
                  })
                }
              >
                + Add Kyc
              </AppButton>
            </div>
          </div>
        )}
      />
    </div>
  );
};

export default KycDetail;
