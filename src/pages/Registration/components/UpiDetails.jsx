import { Typography } from '@mui/material';
import { useFormikContext } from 'formik';

import { FieldInput } from 'Components/FormFields';
import { UPI_VERIFICATION_REGEX } from 'Utilities/constants/upi';

import { style, StyledBankTile } from '../styles';

const UpiDetails = () => {
  const { upiWrapper, upiTitle } = style();
  const { setFieldValue: setUpi } = useFormikContext();

  const validateUpi = val => {
    if (!!val && !UPI_VERIFICATION_REGEX.test(val)) {
      return 'Wrong Upi Id';
    }
  };

  const handleUpiInput = e => {
    setUpi('upi_detail_attributes.upi_id_number', e.target.value.trim());
  };

  return (
    <div className={upiWrapper}>
      <Typography variant='h6' component='div'>
        <b>UPI Details:</b>
      </Typography>
      <StyledBankTile>
        <Typography variant='subtitle1' component='div' className={upiTitle}>
          UPI ID:
        </Typography>
        <div>
          <FieldInput
            name='upi_detail_attributes.upi_id_number'
            size='small'
            required
            validate={validateUpi}
            variant='outlined'
            InputLabelProps={{
              shrink: true,
            }}
            inputProps={{
              'data-cy': 'mandi-registration-upiId',
            }}
            onChange={handleUpiInput}
          />
        </div>
      </StyledBankTile>
    </div>
  );
};

export default UpiDetails;
