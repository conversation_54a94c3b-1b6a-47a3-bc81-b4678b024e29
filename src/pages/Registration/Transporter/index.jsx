import { useState } from 'react';

import { Paper, Typography } from '@mui/material';
import { Form, Formik } from 'formik';
import { useNavigate } from 'react-router-dom';

import { useSiteValue } from 'App/SiteContext';
import { AppButton } from 'Components';
import PageLayout from 'Components/PageLayout';
import useNotify from 'Hooks/useNotify';
import { registerPartner } from 'Services/register';
import { UPI_STATUS } from 'Utilities/constants/registration';
import imageDirectUpload from 'Utilities/directUpload';

import BankDetail from '../components/BankDetails';
import KycDetail from '../components/KycDetail';
import PersonalDetail from '../components/PersonalDetail';
import { StyledTextInput } from '../components/StyledTextinput';
import UpiDetails from '../components/UpiDetails';
import registrationFileUpload from '../registrationFileUpload';

const TransporterRegistrationForm = ({
  initialValues = {},
  onSubmit,
  toggleModal,
  handleTransporterDetails = () => {},
  isModal = false,
}) => {
  const NotificationBar = useNotify();
  const [isLoading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { mandiId, mandiList } = useSiteValue();
  const [otpResponse, setOtpResponse] = useState('');

  const currentMandi = mandiList.find(({ id }) => id === mandiId);

  const submitHandler = async values => {
    setLoading(true);
    const postData = {
      ...values,
      transporter_type: 'farmer_gatein',
      source: currentMandi?.name,
    };
    if (!postData.upi_detail_attributes.upi_id_number) {
      delete postData.upi_detail_attributes;
    }
    const { kyc_docs_attributes = [], bank_detail_attributes = {} } = values;

    if (bank_detail_attributes?.bank_attachments?.length) {
      await Promise.all(
        bank_detail_attributes?.bank_attachments?.map(value =>
          value ? imageDirectUpload(value) : Promise.resolve()
        )
      ).then(res => {
        bank_detail_attributes.bank_attachments = res
          .filter(Boolean)
          .map(({ data }) => data?.signed_id);
      });
    }

    const fileData = await registrationFileUpload(kyc_docs_attributes);
    const { kyc_docs = [] } = await fileData;

    const requestBody = {
      partner: {
        ...postData,
        kyc_docs_attributes: kyc_docs,
        bank_detail_attributes,
        roles: 'Transporter',
        otp_request_id: otpResponse || null,
      },
    };

    const processTransporter = onSubmit || registerPartner;

    processTransporter(requestBody)
      .then(res => {
        if (res && !onSubmit) {
          handleTransporterDetails && handleTransporterDetails(res);
        }
        NotificationBar(
          `Transporter ${onSubmit ? 'updated' : 'created'} successfully.`
        );
        !toggleModal && backHandler();
      })
      .finally(() => {
        toggleModal && toggleModal();
        setLoading(false);
      });
  };

  const backHandler = () => {
    const redirectToPath = onSubmit ? 'registration/partner-list' : 'home';
    navigate(`/app/${redirectToPath}`);
  };

  const formContent = (
    <Formik
      enableReinitialize
      initialValues={{
        ...initialValues,
        bank_detail_attributes: initialValues?.bank_detail_attributes
          ? initialValues?.bank_detail_attributes
          : {
              account_number: '',
              account_number_confirmation: '',
              ifsc: '',
            },
        kyc_docs_attributes: initialValues?.kyc_docs_attributes?.length
          ? initialValues.kyc_docs_attributes
          : [
              {
                doc_category: '',
                doc_type: '',
                fleet_size: '',
                financial_year: '',
                kyc_attachments: '',
              },
            ],
        upi_detail_attributes: initialValues?.upi_detail_attributes
          ? initialValues?.upi_detail_attributes
          : {
              upi_id_number: '',
              status: UPI_STATUS.PENDING,
            },
      }}
      onSubmit={submitHandler}
    >
      {({ handleSubmit, handleReset }) => (
        <Form>
          {isModal ? (
            <>
              <Paper style={{ flex: 1, padding: '1rem', marginBottom: '1rem' }}>
                <PersonalDetail setOtpResponse={setOtpResponse} />
                <StyledTextInput>
                  <Typography variant='subtitle1' component='div'>
                    Transporter Type
                  </Typography>
                  <div data-cy='mandi-registration-transporterType'>
                    <b>Farmer GateIn</b>
                  </div>
                </StyledTextInput>
                <StyledTextInput>
                  <Typography variant='subtitle1' component='div'>
                    Source
                  </Typography>
                  <div data-cy='mandi-registration-source'>
                    <b>{currentMandi?.name}</b>
                  </div>
                </StyledTextInput>
                <KycDetail />
                <BankDetail />
                <UpiDetails />
              </Paper>
              <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '1rem', padding: '1rem 0' }}>
                <AppButton
                  variant='contained'
                  color='inherit'
                  data-cy='mandi-registration-transporter-cancel'
                  onClick={() => {
                    handleReset();
                    toggleModal && toggleModal();
                  }}
                >
                  Cancel
                </AppButton>
                <AppButton
                  variant='contained'
                  color='primary'
                  loading={isLoading}
                  type='submit'
                  data-cy='mandi-registration-transporter-save'
                  onClick={handleSubmit}
                >
                  Save
                </AppButton>
              </div>
            </>
          ) : (
            <>
              <PageLayout.Body>
                <Paper style={{ flex: 1, padding: '1rem' }}>
                  <PersonalDetail setOtpResponse={setOtpResponse} />
                  <StyledTextInput>
                    <Typography variant='subtitle1' component='div'>
                      Transporter Type
                    </Typography>
                    <div data-cy='mandi-registration-transporterType'>
                      <b>Farmer GateIn</b>
                    </div>
                  </StyledTextInput>
                  <StyledTextInput>
                    <Typography variant='subtitle1' component='div'>
                      Source
                    </Typography>
                    <div data-cy='mandi-registration-source'>
                      <b>{currentMandi?.name}</b>
                    </div>
                  </StyledTextInput>
                  <KycDetail />
                  <BankDetail />
                  <UpiDetails />
                </Paper>
              </PageLayout.Body>
              <PageLayout.Footer>
                <AppButton
                  variant='contained'
                  color='inherit'
                  className='margin-horizontal'
                  data-cy='mandi-registration-transporter-cancel'
                  onClick={() => {
                    handleReset();
                    backHandler();
                  }}
                >
                  Cancel
                </AppButton>
                <AppButton
                  variant='contained'
                  color='primary'
                  className='margin-horizontal'
                  loading={isLoading}
                  type='submit'
                  data-cy='mandi-registration-transporter-save'
                  onClick={handleSubmit}
                >
                  Save
                </AppButton>
              </PageLayout.Footer>
            </>
          )}
        </Form>
      )}
    </Formik>
  );

  return isModal ? formContent : (
    <PageLayout title='Transporter Registration'>
      {formContent}
    </PageLayout>
  );
};

export default TransporterRegistrationForm;
