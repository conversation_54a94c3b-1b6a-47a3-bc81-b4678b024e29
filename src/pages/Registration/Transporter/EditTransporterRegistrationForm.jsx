import React, { useEffect, useState } from 'react';

import { useParams } from 'react-router-dom';

import { AppLoader } from 'Components';
import { getPartnerDetails, updatePartner } from 'Services/register';

import RegistrationForm from '.';

const EditTransporterRegistrationForm = () => {
  const params = useParams();
  const { id } = params;
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (id) {
      setLoading(true);
      getPartnerDetails(id)
        .then(res => {
          if (res) {
            setData({
              ...res,
              ...res.location,
            });
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [id]);

  const onSubmit = async reqBody => {
    await updatePartner(id, reqBody);
  };

  if (loading) {
    return <AppLoader />;
  }

  return (
    <RegistrationForm
      initialValues={data}
      onSubmit={reqBody => onSubmit(reqBody)}
    />
  );
};

export default EditTransporterRegistrationForm;
