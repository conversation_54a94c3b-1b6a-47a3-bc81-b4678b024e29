import React, { useEffect, useState } from 'react';

import { useParams } from 'react-router-dom';

import { getPartnerDetails, updatePartner } from 'Services/register';

import RegistrationForm from '.';

const EditSupplierRegistrationForm = () => {
  const params = useParams();
  const { id } = params;
  const [data, setData] = useState([]);

  useEffect(() => {
    (async () => {
      const res = await getPartnerDetails(id);
      const initial = {
        ...res,
        ...res.location,
      };
      setData(initial);
    })();
  }, [id]);

  const onSubmit = async reqBody => {
    await updatePartner(id, reqBody);
  };

  return (
    <RegistrationForm
      initialValues={data}
      onSubmit={reqBody => onSubmit(reqBody)}
    />
  );
};

export default EditSupplierRegistrationForm;
