import { useState } from 'react';

import { Paper } from '@mui/material';
import { Form, Formik } from 'formik';
import { useNavigate } from 'react-router-dom';

import { AppButton } from 'Components';
import PageLayout from 'Components/PageLayout';
import useNotify from 'Hooks/useNotify';
import { registerPartner } from 'Services/register';
import { UPI_STATUS } from 'Utilities/constants/registration';
import imageDirectUpload from 'Utilities/directUpload';

import BankDetail from '../components/BankDetails';
import KycDetail from '../components/KycDetail';
import PersonalDetail from '../components/PersonalDetail';
import UpiDetails from '../components/UpiDetails';
import registrationFileUpload from '../registrationFileUpload';

const SupplierRegistrationForm = ({ initialValues = {}, onSubmit }) => {
  const navigate = useNavigate();
  const NotificationBar = useNotify();
  const [isLoading, setLoading] = useState(false);
  const [otpResponse, setOtpResponse] = useState('');

  const submitHandler = async values => {
    setLoading(true);
    const postData = { ...values };
    if (!postData.upi_detail_attributes.upi_id_number) {
      delete postData.upi_detail_attributes;
    }
    const { kyc_docs_attributes, bank_detail_attributes } = values;

    if (bank_detail_attributes?.bank_attachments?.length) {
      await Promise.all(
        bank_detail_attributes?.bank_attachments?.map(value =>
          value ? imageDirectUpload(value) : Promise.resolve()
        )
      ).then(res => {
        bank_detail_attributes.bank_attachments = res
          .filter(Boolean)
          .map(({ data }) => data?.signed_id);
      });
    }

    const fileData = await registrationFileUpload(kyc_docs_attributes);
    const { kyc_docs = [] } = await fileData;

    const requestBody = {
      partner: {
        ...postData,
        kyc_docs_attributes: kyc_docs,
        bank_detail_attributes,
        roles: 'Supplier',
        otp_request_id: otpResponse || null,
      },
    };

    const processSupplier = onSubmit || registerPartner;

    processSupplier(requestBody)
      .then(() => {
        NotificationBar(
          `Supplier ${onSubmit ? 'updated' : 'created'} successfully.`
        );
        backHandler();
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const backHandler = () => {
    const redirectToPath = onSubmit ? 'registration/partner-list' : 'home';
    navigate(`/app/${redirectToPath}`);
  };
  console.log(initialValues, 'initialValues');

  return (
    <PageLayout title='Supplier Registration'>
      <Formik
        enableReinitialize
        initialValues={{
          ...initialValues,
          bank_detail_attributes: initialValues?.bank_detail_attributes
            ? initialValues?.bank_detail_attributes
            : {
                account_number: '',
                account_number_confirmation: '',
                ifsc: '',
              },
          kyc_docs_attributes: initialValues?.kyc_docs_attributes?.length
            ? initialValues.kyc_docs_attributes
            : [
                {
                  doc_type: '',
                  kyc_attachments: '',
                  identification_number: '',
                },
              ],
          upi_detail_attributes: initialValues?.upi_detail_attributes
            ? initialValues?.upi_detail_attributes
            : {
                upi_id_number: '',
                status: UPI_STATUS.PENDING,
              },
        }}
        onSubmit={submitHandler}
      >
        {({ handleSubmit, handleReset }) => (
          <Form>
            <PageLayout.Body>
              <Paper style={{ flex: 1, padding: '1rem' }}>
                <PersonalDetail setOtpResponse={setOtpResponse} />
                <KycDetail />
                <BankDetail />
                <UpiDetails />
              </Paper>
            </PageLayout.Body>
            <PageLayout.Footer>
              <AppButton
                variant='contained'
                color='inherit'
                className='margin-horizontal'
                data-cy='mandi-registration-supplier-cancel'
                onClick={() => {
                  handleReset();
                  backHandler();
                }}
              >
                Cancel
              </AppButton>
              <AppButton
                variant='contained'
                color='primary'
                className='margin-horizontal'
                loading={isLoading}
                type='submit'
                data-cy='mandi-registration-supplier-save'
                onClick={handleSubmit}
              >
                Save
              </AppButton>
            </PageLayout.Footer>
          </Form>
        )}
      </Formik>
    </PageLayout>
  );
};

export default SupplierRegistrationForm;
