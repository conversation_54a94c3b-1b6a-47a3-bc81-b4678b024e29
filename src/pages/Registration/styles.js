import { makeStyles } from '@mui/styles';
import styled from 'styled-components';

export const JustifyFileSection = styled.div`
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  & > div:first-child {
    width: 60px !important;
  }
`;

export const StyledBankTile = styled.div`
  display: flex;
  font-size: 16px;
  align-items: center;
  margin-bottom: 16px;
  gap: 16px;
  & > div:first-child {
    min-width: 120px;
  }

  & > div:nth-child(2) {
    gap: 16px;
    /* flex-grow: 1; */
  }
`;

export const ImageThumbWrapper = styled.div`
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  position: relative;
`;

export const ImageStatus = styled.div`
  position: absolute;
  transform: rotate(-40deg);
  top: 1.5rem;
  font-weight: 700;
  font-size: 1rem;
  border-radius: 4px;
  border: 1px solid;
  border-color: ${({ verified }) => (verified ? '#15ce14' : '#ce2828')};
  color: ${({ verified }) => (verified ? '#15ce14' : '#ce2828')};
`;

export const style = makeStyles(() => ({
  upiWrapper: {
    margin: '1rem 0 3rem 0 !important',
  },
  upiTitle: {
    width: '8rem',
  },
}));
