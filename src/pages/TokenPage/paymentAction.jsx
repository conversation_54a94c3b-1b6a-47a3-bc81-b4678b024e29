import React, { useEffect, useState } from 'react';

import { Grid, Typography } from '@mui/material';
import { Formik } from 'formik';

import { useSiteValue } from 'App/SiteContext';
import { AppButton, Modal as CustomModal } from 'Components';
import {
  FieldDatePicker as FieldDatepicker,
  FieldInput,
  FieldSwitch,
} from 'Components/FormFields';
import { updatePaymentRequest } from 'Services/payments';
import { PR_STATUS, PRIORITY } from 'Utilities/constants/paymentRequest';
import { validateRequired } from 'Utilities/formvalidation';

const PaymentAction = ({ data, callBack }) => {
  const { userInfo } = useSiteValue();
  const [openApproval, setOpenApproval] = useState(false);
  const [openReject, setOpenReject] = useState(false);
  const [selectedData, setSelectedData] = useState({});
  const [initialData, setInitialData] = useState({});
  const requestId = data?.lots_data?.[0]?.payment_request_id;

  const approvePaymentRequest = data => {
    setSelectedData(data);
    setOpenApproval(true);
  };

  const rejectPaymentRequest = data => {
    setSelectedData(data);
    setOpenReject(true);
  };

  const updateData = (
    { approve = false, reject = false },
    { priority, due_date, reject_reason } = {}
  ) => {
    let updatedValues;
    if (approve) {
      updatedValues = {
        priority: priority ? PRIORITY.HIGH : PRIORITY.LOW,
        due_date,
        approved_date: Date.now(),
        approver_id: userInfo.id,
        status: PR_STATUS.APPROVED,
      };
    } else if (reject) {
      updatedValues = {
        rejected_date: Date.now(),
        rejector_id: userInfo.id,
        status: PR_STATUS.REJECTED,
        reject_reason,
      };
    }
    updatePaymentRequest(updatedValues, requestId)
      .then(callBack)
      .finally(() => {
        setSelectedData({});
        if (approve) {
          setOpenApproval(false);
        } else if (reject) {
          setOpenReject(false);
        }
      });
  };

  const closeModal = modal => {
    return () => modal(false);
  };

  useEffect(() => {
    setInitialData({
      due_date: data?.due_date || '',
      priority: data?.priority === PRIORITY.HIGH,
    });
  }, [data]);

  return (
    <div>
      <Grid container alignItems='center' spacing={1}>
        <>
          <Grid item lg={9} sm={12}>
            <AppButton
              onClick={() => approvePaymentRequest(data)}
              color='primary'
              variant='contained'
              fullWidth
              size='small'
            >
              Approve
            </AppButton>
          </Grid>
          <Grid item lg={9} sm={12}>
            <AppButton
              onClick={() => rejectPaymentRequest(data)}
              color='error'
              variant='contained'
              size='small'
              fullWidth
            >
              Reject
            </AppButton>
          </Grid>
        </>
      </Grid>
      <CustomModal
        title='Approve this payment request'
        open={openApproval}
        onClose={closeModal(setOpenApproval)}
      >
        <Grid container direction='column' spacing={0}>
          <Formik
            initialValues={initialData}
            onSubmit={values => updateData({ approve: true }, values)}
          >
            {({ handleSubmit, values }) => (
              <Grid container>
                {selectedData?.customer && (
                  <Typography variant='body1'>
                    Approve this Payment Request as Paid by Customer?
                  </Typography>
                )}
                {!selectedData?.customer && (
                  <>
                    <Grid container style={{ marginTop: '1rem' }}>
                      <Grid item md={4} xs={12}>
                        <Typography variant='body1'>High Priority</Typography>
                      </Grid>
                      <Grid
                        item
                        md={8}
                        xs={12}
                        style={{ display: 'flex', alignItems: 'center' }}
                      >
                        <Typography variant='body1'>Low</Typography>
                        <FieldSwitch
                          size='medium'
                          label=''
                          name='priority'
                          checked={values.priority}
                          labelPlacement='end'
                          InputLabelProps={{
                            fullWidth: false,
                          }}
                        />
                      </Grid>
                    </Grid>
                    <Grid container style={{ marginTop: '1rem' }}>
                      <Grid item md={4} xs={12}>
                        <Typography variant='body1'>Due Date</Typography>
                      </Grid>
                      <Grid item md={8} xs={12}>
                        <FieldDatepicker
                          name='due_date'
                          variant='inline'
                          placeholder='Due Date'
                          autoOk
                          inputVariant='outlined'
                          format='DD/MM/YYYY'
                          textFieldProps={{
                            size: 'small',
                          }}
                        />
                      </Grid>
                    </Grid>
                  </>
                )}
                <Grid
                  container
                  style={{ marginTop: '1rem' }}
                  justify='flex-end'
                >
                  <AppButton
                    variant='contained'
                    color='primary'
                    style={{ margin: '0 1rem' }}
                    onClick={handleSubmit}
                  >
                    {selectedData?.customer && <>Yes</>}
                    {!selectedData?.customer && <>Save</>}
                  </AppButton>
                </Grid>
              </Grid>
            )}
          </Formik>
        </Grid>
      </CustomModal>
      <CustomModal
        title='Warning'
        open={openReject}
        onClose={closeModal(setOpenReject)}
      >
        <Typography
          variant='body1'
          gutterBottom
          style={{ marginBottom: '1rem' }}
        >
          Are you sure you want to reject this payment request ?
        </Typography>
        <Formik
          initialValues={{}}
          onSubmit={values => updateData({ reject: true }, values)}
          enableReinitialize
        >
          {({ handleSubmit }) => (
            <>
              <FieldInput
                name='reject_reason'
                size='small'
                label='Reject Reason'
                placeholder='Reject Reason'
                variant='outlined'
                required
                validate={validateRequired}
                multiline
                rows={3}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <Grid container style={{ marginTop: '1rem' }} justify='flex-end'>
                <AppButton
                  variant='contained'
                  color='primary'
                  className='margin-horizontal'
                  size='small'
                  onClick={handleSubmit}
                >
                  Yes
                </AppButton>
              </Grid>
            </>
          )}
        </Formik>
      </CustomModal>
    </div>
  );
};

export default PaymentAction;
