import { useEffect, useState } from 'react';

import { ExpandLess, ExpandMore, Save as SaveIcon } from '@mui/icons-material';
import { Box, Grid, InputAdornment, Paper, Typography } from '@mui/material';
import { FieldArray, Formik } from 'formik';
import {
  calFlatCharges,
  getFarmerOtherCharges,
  getPromoDiscount,
  mandiCalFlatCharges,
} from 'Utilities/paymentRequest';

import { useSiteValue } from 'App/SiteContext';
import { AppButton, ImageThumb, Modal as CustomModal, Table } from 'Components';
import {
  FieldCheckbox,
  FieldDatePicker as FieldDatepicker,
  FieldInput,
  UploadInput,
} from 'Components/FormFields';
import { capitalize, toFixedNumber } from 'Utilities';
import { TOKEN_TABS } from 'Utilities/constants/lots';
import { addDaysToDate } from 'Utilities/dateUtils';
import { validateMaxOrEquals } from 'Utilities/formvalidation';

import useAtomicStyles from '../../theme/AtomicCss';

import DiscountPopUp from './DiscountPopUp';
import {
  ButtonWrapper,
  ImageListWrapper,
  RecordGridContainer,
  useStyles,
} from './Styled';
import { COLUMN_ITEM, COLUMNS_PACK } from './tokenColumns';

const today = new Date().setHours(0, 0, 0, 0);

const PackhouseCharges = ({ packhouse_amount }) => (
  <>
    <Grid sm={6} md={6}>
      <Typography variant='body1'>
        <b>Packhouse Bill Charges:</b>
      </Typography>
    </Grid>
    <Grid sm={1} md={1} />
    <Grid sm={3} md={3}>
      <Typography variant='body1'>
        <b>₹ {toFixedNumber(packhouse_amount, 2)}</b>
      </Typography>
    </Grid>
  </>
);

const PaymentRequestModal = ({
  open,
  closeModal,
  data,
  submitPayementRequest,
  loading,
  prData,
  payment_term_days = 0,
  advance_amount_adjustment = [],
  otherCharges = {},
  mandi,
  flat_charges,
  farmer_material_charges = [],
  tab,
}) => {
  const classes = useStyles();
  const dueDate = addDaysToDate(today, 0); // addDaysToDate(today, payment_term_days || 0);
  const [initialData, setInitialData] = useState({
    payment_request_bill: '',
    bill: '',
  });
  const [expand, setExpand] = useState(false);
  const [totalExpense, setTotalExpense] = useState(0);
  const [applyDiscount, setApplyDiscount] = useState(false);
  const [showDiscountPopUp, setShowDiscountPopUp] = useState(true);
  const { farmer_flat_charges } = mandi;
  const { mandiConfig: { enable_pack_auction = false } = {} } = useSiteValue();
  const { lightGray, grayBorder } = useAtomicStyles();

  const mandiCharge = farmer_flat_charges?.map(item => ({
    item,
    charge_value: '',
  }));

  const submitForm = values => {
    submitPayementRequest(values, totalExpense);
  };

  useEffect(() => {
    setInitialData({
      due_date: prData?.due_date || dueDate,
      has_cash_advance_paid: !!otherCharges.cash_advance_amount || false,
      bill: prData && prData?.bill,
      advance_amount_adjustment,
      flat_charges,
      mandi_flat_charge: mandiCharge,
      cash_advance_paid: otherCharges.cash_advance_amount || '',
      advance_reciept: prData && prData?.cash_advance_reciept,
    });
  }, [prData, payment_term_days, otherCharges]);

  const advanceAdjustment = ({ advance_amount_adjustment = [] }) =>
    advance_amount_adjustment?.reduce((total, { amount = 0 }) => {
      total += amount;
      return total;
    }, 0);

  const {
    subtotal,
    farmer_commission = 0,
    farmer_commission_discount = 0,
    warehousing_cost = 0,
    discount = 0,
    transportation_cost = 0,
    deductions = 0,
    farmer_other_charges = [],
    farmer_token_discounts = [],
    packaging_type_charges = 0,
    packaging_types,
    process_charge_deductions = {},
    satellite_mandi_transportation_cost = 0,
  } = otherCharges;

  const totalunits = value =>
    value?.reduce((sum, item) => sum + (item?.units || 0), 0);

  const packhouse_amount = Array.isArray(deductions)
    ? deductions?.[0]?.amount || 0
    : 0;

  const farmerOtherCharges = getFarmerOtherCharges(farmer_other_charges);

  const farmerTokenDiscounts = getPromoDiscount(farmer_token_discounts);

  const totalFarmerMaterialCharges = farmer_material_charges?.reduce(
    (acc, curr) => acc + (curr.amount || 0),
    0
  );

  const totalAmount =
    subtotal -
    transportation_cost -
    packhouse_amount -
    farmerOtherCharges -
    farmer_commission +
    farmer_commission_discount -
    warehousing_cost +
    farmerTokenDiscounts +
    discount -
    (process_charge_deductions.total_deduction || 0) -
    satellite_mandi_transportation_cost -
    totalFarmerMaterialCharges;

  const calculateTotalExpense = (
    array = flat_charges,
    { value = null, index = null }
  ) => {
    let total =
      farmer_commission -
      farmer_commission_discount +
      warehousing_cost -
      discount +
      transportation_cost +
      farmerOtherCharges -
      farmerTokenDiscounts +
      (process_charge_deductions.total_deduction || 0) +
      satellite_mandi_transportation_cost;

    array?.forEach((item, i) => {
      if (i === index) {
        total += +value;
        return;
      }
      total += +item.charge_value;
    });

    setTotalExpense(total);
  };

  useEffect(() => {
    calculateTotalExpense(flat_charges, {});
  }, [flat_charges]);

  const calcTotalAmount = values =>
    totalAmount -
    (calFlatCharges(values) || mandiCalFlatCharges(values)) -
    (advanceAdjustment(values) || 0);

  const isDiscountPopUp =
    data?.net_amount < 0 &&
    data?.negative_bill_discount > 0 &&
    showDiscountPopUp &&
    tab === TOKEN_TABS.SOLD;

  return (
    <CustomModal
      title={
        <>
          {`Payment Request (Token: ${data?.token})`}
          <Typography>Farmer Name: {data?.farmer_name}</Typography>
        </>
      }
      open={open}
      onClose={() => closeModal(setInitialData)}
      contentSize
      screenHeight='80%'
    >
      {isDiscountPopUp ? (
        <DiscountPopUp
          data={data}
          closeModal={closeModal}
          setApplyDiscount={setApplyDiscount}
          setShowDiscountPopUp={setShowDiscountPopUp}
          setInitialData={setInitialData}
        />
      ) : (
        <Formik
          initialValues={{
            ...initialData,
            advance_amount_adjustment,
            flat_charges,
            mandi_flat_charge: mandiCharge,
            farmer_material_charges,
          }}
          onSubmit={submitForm}
          enableReinitialize
        >
          {({
            handleSubmit,
            values,
            setFieldValue,
            getFieldProps,
            handleChange,
          }) => (
            <>
              <Grid container>
                <Grid sm={6} md={6}>
                  <Typography variant='body1'>
                    <b>Sub Total</b>
                  </Typography>
                </Grid>
                <Grid sm={1} md={1} />
                <Grid sm={3} md={3}>
                  <Typography variant='body1'>
                    <b>₹ {subtotal || 0}</b>
                  </Typography>
                </Grid>
              </Grid>
              <Grid container className={classes.total}>
                <Grid sm={6} md={6}>
                  <Typography variant='body1'>
                    <b>Total Expenses</b>
                  </Typography>
                </Grid>
                <Grid sm={1} md={1} />
                <Grid sm={3} md={3}>
                  <Typography variant='body1'>
                    <b>₹ {toFixedNumber(totalExpense)}</b>
                  </Typography>
                </Grid>
              </Grid>
              <Grid container className={classes.container}>
                <Grid container>
                  <Grid sm={6} md={6}>
                    <Typography variant='body1'>Farmer Comission</Typography>
                  </Grid>
                  <Grid item md={1} sm={1}>
                    +
                  </Grid>
                  <Grid item md={3} sm={3}>
                    <Typography variant='body1'>
                      ₹ {toFixedNumber(farmer_commission) || 0}
                    </Typography>
                  </Grid>
                </Grid>
                <Grid container>
                  <Grid sm={6} md={6}>
                    <Typography variant='body1'>
                      Discount on Farmer Comission
                    </Typography>
                  </Grid>
                  <Grid sm={1} md={1}>
                    <Typography variant='body1'>-</Typography>
                  </Grid>
                  <Grid sm={3} md={3}>
                    <Typography variant='body1'>
                      ₹ {toFixedNumber(farmer_commission_discount) || 0}
                    </Typography>
                  </Grid>
                </Grid>
                <Grid container>
                  <Grid sm={6} md={6}>
                    <Typography variant='body1'>Warehouse Charges</Typography>
                  </Grid>
                  <Grid sm={1} md={1}>
                    <Typography variant='body1'>+</Typography>
                  </Grid>
                  <Grid sm={3} md={3}>
                    <Typography variant='body1'>
                      ₹ {toFixedNumber(warehousing_cost) || 0}
                    </Typography>
                  </Grid>
                </Grid>
                <Grid container>
                  <Grid sm={6} md={6}>
                    <Typography variant='body1'>
                      Discount on Warehouse Charges
                    </Typography>
                  </Grid>
                  <Grid sm={1} md={1}>
                    <Typography variant='body1'>-</Typography>
                  </Grid>
                  <Grid sm={3} md={3}>
                    <Typography variant='body1'>
                      ₹ {toFixedNumber(discount) || 0}
                    </Typography>
                  </Grid>
                </Grid>
                <Grid container>
                  <Grid sm={6} md={6}>
                    <Typography variant='body1'>Transportation Cost</Typography>
                  </Grid>
                  <Grid sm={1} md={1}>
                    <Typography variant='body1'>+</Typography>
                  </Grid>
                  <Grid sm={3} md={3}>
                    <Typography variant='body1'>
                      ₹ {toFixedNumber(transportation_cost) || 0}
                    </Typography>
                  </Grid>
                </Grid>
                <Grid container>
                  <Grid sm={6} md={6}>
                    <Typography variant='body1'>Process Charge</Typography>
                  </Grid>
                  <Grid sm={1} md={1}>
                    <Typography variant='body1'>+</Typography>
                  </Grid>
                  <Grid sm={3} md={3}>
                    <Typography variant='body1'>
                      ₹{' '}
                      {toFixedNumber(
                        process_charge_deductions.total_deduction,
                        2
                      )}
                    </Typography>
                  </Grid>
                </Grid>
                {(farmer_other_charges || []).map(
                  ({ charge_type, charge_value = 0, discount = 0 }) => (
                    <>
                      <Grid container key={charge_type}>
                        <Grid sm={6} md={6}>
                          <Typography>{charge_type}</Typography>
                        </Grid>
                        <Grid sm={1} md={1}>
                          <Typography variant='body1'>-</Typography>
                        </Grid>
                        <Grid sm={3} md={3}>
                          <Typography variant='body1'>
                            ₹ {toFixedNumber(charge_value, 2)}
                          </Typography>
                        </Grid>
                      </Grid>
                      {discount > 0 && (
                        <Grid container key={charge_type}>
                          <Grid sm={6} md={6}>
                            <Typography>Discount on {charge_type}</Typography>
                          </Grid>
                          <Grid sm={1} md={1}>
                            <Typography variant='body1'>-</Typography>
                          </Grid>
                          <Grid sm={3} md={3}>
                            <Typography variant='body1'>
                              ₹ {toFixedNumber(discount, 2)}
                            </Typography>
                          </Grid>
                        </Grid>
                      )}
                    </>
                  )
                )}
                {(farmer_token_discounts || []).map(
                  ({
                    discount_type,
                    discount_value = 0,
                    discount_percent = 0,
                  }) => (
                    <Grid container key={discount_type}>
                      <Grid sm={6} md={6}>
                        <Typography>
                          {discount_type === 'ScratchCardDiscount'
                            ? `PromoDiscount (${discount_percent}% Off)`
                            : discount_type}
                        </Typography>
                      </Grid>
                      <Grid sm={1} md={1}>
                        <Typography variant='body1'>-</Typography>
                      </Grid>
                      <Grid sm={3} md={3}>
                        <Typography variant='body1'>
                          ₹ {toFixedNumber(discount_value, 2)}
                        </Typography>
                      </Grid>
                    </Grid>
                  )
                )}
                {satellite_mandi_transportation_cost > 0 && (
                  <Grid container>
                    <Grid sm={6} md={6}>
                      <Typography variant='body1'>
                        Satellite Mandi Transportation Cost
                      </Typography>
                    </Grid>
                    <Grid sm={1} md={1}>
                      <Typography variant='body1'>+</Typography>
                    </Grid>
                    <Grid sm={3} md={3}>
                      <Typography variant='body1'>
                        ₹{' '}
                        {toFixedNumber(satellite_mandi_transportation_cost, 2)}
                      </Typography>
                    </Grid>
                  </Grid>
                )}
                <Grid container>
                  <Grid item sm={12} md={12}>
                    {farmer_material_charges?.map(({ name, amount, id }) => (
                      <Grid container alignItems='center' key={id}>
                        <Grid item sm={6} md={6}>
                          <Typography variant='body1'>
                            Unreturned Crates ({name})
                          </Typography>
                        </Grid>
                        <Grid item sm={1} md={1}>
                          <Typography variant='body1'>+</Typography>
                        </Grid>
                        <Grid item sm={2} md={2}>
                          <Typography variant='body1'>
                            ₹ {toFixedNumber(amount, 2)}
                          </Typography>
                        </Grid>
                      </Grid>
                    ))}
                  </Grid>
                </Grid>
                <Grid container>
                  <Grid sm={6} md={6}>
                    <Typography variant='body1'>Flat Charges</Typography>
                  </Grid>
                </Grid>

                {values?.flat_charges?.length > 0 && (
                  <Grid container>
                    <FieldArray
                      name='flat_charges'
                      render={() =>
                        values?.flat_charges?.map((item, index) => (
                          <>
                            <Grid
                              sm={6}
                              md={6}
                              key={index}
                              className={classes.subCharge}
                            >
                              <Typography variant='body1'>
                                {capitalize(item?.charge_type)}
                              </Typography>
                            </Grid>
                            <Grid sm={1} md={1}>
                              <Typography variant='body1'>-</Typography>
                            </Grid>
                            <Grid sm={3} md={3}>
                              <FieldInput
                                name={`flat_charges.${index}.charge_value`}
                                size='small'
                                type='number'
                                style={{ width: '100%' }}
                                InputLabelProps={{
                                  shrink: true,
                                }}
                                onChange={e => {
                                  handleChange(e);
                                  calculateTotalExpense(values?.flat_charges, {
                                    value: e.target.value,
                                    index,
                                  });
                                }}
                                InputProps={{
                                  startAdornment: (
                                    <InputAdornment position='start'>
                                      ₹
                                    </InputAdornment>
                                  ),
                                }}
                              />
                            </Grid>
                          </>
                        ))
                      }
                    />
                  </Grid>
                )}
                {!values?.flat_charges?.length &&
                  values?.mandi_flat_charge?.length > 0 && (
                    <Grid container>
                      <FieldArray
                        name='mandi_flat_charge'
                        render={() =>
                          values?.mandi_flat_charge?.map((i, index) => (
                            <>
                              <Grid
                                sm={6}
                                md={6}
                                key={index}
                                className={classes.subCharge}
                              >
                                <Typography variant='body1'>
                                  {capitalize(i?.item)}
                                </Typography>
                              </Grid>
                              <Grid sm={1} md={1}>
                                <Typography variant='body1'>-</Typography>
                              </Grid>
                              <Grid sm={3} md={3}>
                                <FieldInput
                                  name={`mandi_flat_charge.${index}.charge_value`}
                                  size='small'
                                  type='number'
                                  style={{ width: '100%' }}
                                  InputLabelProps={{
                                    shrink: true,
                                  }}
                                  onChange={e => {
                                    handleChange(e);
                                    calculateTotalExpense(
                                      values?.mandi_flat_charge,
                                      {
                                        value: e.target.value,
                                        index,
                                      }
                                    );
                                  }}
                                  InputProps={{
                                    startAdornment: (
                                      <InputAdornment position='start'>
                                        ₹
                                      </InputAdornment>
                                    ),
                                  }}
                                />
                              </Grid>
                            </>
                          ))
                        }
                      />
                    </Grid>
                  )}
              </Grid>
              {!enable_pack_auction ? (
                <Grid container style={{ paddingTop: 20, paddingBottom: 20 }}>
                  <PackhouseCharges packhouse_amount={packhouse_amount} />
                </Grid>
              ) : packhouse_amount === 0 ? (
                <></>
              ) : (
                <Box>
                  <Box
                    style={{
                      margin: '2rem 0 1rem 0',
                      padding: 10,
                      borderRadius: 5,
                    }}
                    className={lightGray}
                  >
                    <Grid container>
                      <PackhouseCharges packhouse_amount={packhouse_amount} />
                      <Grid sm={2} md={2}>
                        <Box onClick={() => setExpand(!expand)}>
                          {expand ? <ExpandLess /> : <ExpandMore />}
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                  {expand && (
                    <Paper elevation={1} style={{ padding: 5 }}>
                      <Grid container display='flex'>
                        <Grid item sm={7} style={{ padding: 10 }}>
                          <Table
                            columns={COLUMN_ITEM}
                            data={packaging_type_charges}
                            footerSummarydata={{
                              total: 'Total',
                              totalAmount: packhouse_amount,
                            }}
                            isFooter
                            elevation={0}
                          />
                        </Grid>
                        <Grid
                          item
                          sm={5}
                          style={{
                            borderRadius: 2,
                            padding: 10,
                          }}
                          className={grayBorder}
                        >
                          <Table
                            columns={COLUMNS_PACK}
                            data={packaging_types}
                            elevation={0}
                            footerSummarydata={{
                              total: 'Total',
                              totalUnit: totalunits(packaging_types || []),
                            }}
                            isFooter
                          />
                        </Grid>
                      </Grid>
                    </Paper>
                  )}
                </Box>
              )}
              <Grid container style={{ margin: '1rem 0 0.2rem 0' }}>
                <Grid sm={6} md={6}>
                  <Typography variant='body1'>
                    <b>Total Amount</b>
                  </Typography>
                </Grid>
                <Grid sm={1} md={1} />
                <Grid sm={3} md={3}>
                  <Typography
                    variant='body1'
                    color='primary'
                    data-cy='fruitx.paymentRequestModal.totalAmount'
                  >
                    <b>
                      ₹{' '}
                      {toFixedNumber(
                        totalAmount -
                          (calFlatCharges(values) ||
                            mandiCalFlatCharges(values)),
                        2
                      )}
                    </b>
                  </Typography>
                </Grid>
              </Grid>
              {values.advance_amount_adjustment?.length > 0 && (
                <Grid container>
                  <Grid container style={{ margin: '1rem 0' }}>
                    <Grid item md={4} sm={4}>
                      <Typography
                        variant='button'
                        className='title'
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        Adjustments from other PO
                      </Typography>
                    </Grid>
                    <Grid item md={8} sm={8}>
                      <FieldArray
                        name='advance_amount_adjustment'
                        render={() =>
                          values.advance_amount_adjustment?.map(
                            (
                              {
                                purchase_order_id = '',
                                from_payment_request_id = '',
                                id = '',
                                remaining_amount = 0,
                              },
                              index
                            ) => (
                              <Grid
                                container
                                alignItems='center'
                                spacing={1}
                                key={index}
                              >
                                <Grid item md={4} sm={4}>
                                  <Typography variant='body1'>
                                    from &nbsp;
                                    <> PO-{purchase_order_id} </>
                                    PR-
                                    {from_payment_request_id || id}
                                  </Typography>
                                </Grid>
                                <Grid item md={4} sm={4}>
                                  <FieldInput
                                    name={`advance_amount_adjustment.${index}.amount`}
                                    size='small'
                                    type='number'
                                    placeholder='Advance'
                                    variant='outlined'
                                    validate={validateMaxOrEquals(
                                      remaining_amount
                                    )}
                                    InputProps={{
                                      endAdornment: (
                                        <InputAdornment position='start'>
                                          {`/${remaining_amount}`}
                                        </InputAdornment>
                                      ),
                                    }}
                                  />
                                </Grid>
                                <Grid item md={4} sm={4}>
                                  <FieldCheckbox
                                    size='small'
                                    name={`advance_amount_adjustment.${index}.adjust`}
                                    onChange={e => {
                                      const adjust = getFieldProps(
                                        `advance_amount_adjustment.${index}.adjust`
                                      );
                                      if (!adjust?.value?.adjust) {
                                        setFieldValue(
                                          `advance_amount_adjustment.${index}.amount`,
                                          remaining_amount
                                        );
                                      } else {
                                        setFieldValue(
                                          `advance_amount_adjustment.${index}.amount`,
                                          0
                                        );
                                      }
                                      handleChange(e);
                                    }}
                                    disabled={remaining_amount <= 0}
                                    options={[
                                      {
                                        key: 'adjust',
                                        label: 'Adjust Fully',
                                      },
                                    ]}
                                  />
                                </Grid>
                              </Grid>
                            )
                          )
                        }
                      />
                      <Grid container style={{ margin: '1rem 0' }}>
                        <Grid sm={6} md={6}>
                          <Typography variant='body1'>
                            <b>PR amount after advance adjustment</b>
                          </Typography>
                        </Grid>
                        <Grid sm={3} md={3}>
                          <Typography variant='body1' color='primary'>
                            <b>₹ {toFixedNumber(calcTotalAmount(values), 2)}</b>
                          </Typography>
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              )}
              <Grid container style={{ margin: '0.2rem 0' }}>
                <Grid xs={7}>
                  <Typography variant='body1'>
                    <b>Cash Advance Paid</b>
                  </Typography>
                </Grid>
                <Grid xs={5}>
                  <Typography
                    variant='body1'
                    data-cy='fruitx.paymentRequestModal.cashAdvance'
                  >
                    <b>₹ {otherCharges.cash_advance_amount}</b>
                  </Typography>
                </Grid>
              </Grid>
              {applyDiscount && data.negative_bill_discount > 0 && (
                <Grid container style={{ margin: '0.2rem 0', color: 'red' }}>
                  <Grid xs={7}>
                    <Typography variant='body1'>
                      <b>Discount</b>
                    </Typography>
                  </Grid>
                  <Grid xs={5}>
                    <Typography variant='body1'>
                      <b>₹ {toFixedNumber(calcTotalAmount(values) * -1, 2)}</b>
                    </Typography>
                  </Grid>
                </Grid>
              )}
              <Grid container style={{ margin: '0.2rem 0 1rem 0' }}>
                <Grid xs={7}>
                  <Typography variant='body1'>
                    <b>PR Amount</b>
                  </Typography>
                </Grid>
                <Grid xs={5}>
                  <Typography
                    variant='body1'
                    color='primary'
                    data-cy='fruitx.paymentRequestModal.prAmount'
                  >
                    <b>
                      ₹{' '}
                      {toFixedNumber(
                        totalAmount -
                          (calFlatCharges(values) ||
                            mandiCalFlatCharges(values)) -
                          (advanceAdjustment(values) || 0) -
                          (otherCharges.cash_advance_amount || 0) +
                          (applyDiscount &&
                            data.negative_bill_discount > 0 &&
                            calcTotalAmount(values) * -1),
                        2
                      )}
                    </b>
                  </Typography>
                </Grid>
              </Grid>
              <Grid container style={{ marginTop: '1rem' }}>
                <Grid item md={4} sm={4}>
                  <Typography variant='body1'>
                    <b>Due Date</b>
                  </Typography>
                </Grid>
                <Grid item md={8} sm={8}>
                  <FieldDatepicker
                    name='due_date'
                    placeholder='Due Date'
                    variant='inline'
                    autoOk
                    inputVariant='outlined'
                    format='DD/MM/YYYY'
                    margin='normal'
                    style={{ margin: '0' }}
                    KeyboardButtonProps={{
                      className: 'datepicker-icon',
                    }}
                    textFieldProps={{
                      size: 'small',
                    }}
                  />
                </Grid>
              </Grid>
              <Grid container style={{ marginTop: '1rem' }}>
                <Grid container style={{ marginTop: '1rem' }}>
                  <Grid item md={4} xs={12}>
                    <Typography variant='body1'>
                      <b>Farmer Signed Bill*:</b>
                    </Typography>
                  </Grid>
                  <Grid item md={8} xs={12}>
                    <UploadInput
                      accept='image/*, application/pdf'
                      name='payment_request_bill'
                      label='Attach bill'
                    />
                  </Grid>
                </Grid>
                <RecordGridContainer
                  container
                  direction='row'
                  alignItems='center'
                  spacing={0}
                >
                  <ImageListWrapper>
                    {(values?.bill || values?.payment_request_bill) && (
                      <ImageThumb
                        url={
                          values?.bill ||
                          values?.payment_request_bill?.[
                            values?.payment_request_bill?.length - 1
                          ]
                        }
                        file={
                          values?.payment_request_bill?.[
                            values?.payment_request_bill?.length - 1
                          ]
                        }
                      />
                    )}
                  </ImageListWrapper>
                </RecordGridContainer>
              </Grid>
              <ButtonWrapper>
                <AppButton
                  startIcon={<SaveIcon />}
                  variant='contained'
                  size='small'
                  color='primary'
                  loading={loading}
                  onClick={handleSubmit}
                  disabled={
                    toFixedNumber(
                      calcTotalAmount(values) +
                        (applyDiscount && data.negative_bill_discount > 0
                          ? calcTotalAmount(values) * -1
                          : 0),
                      2
                    ) < 0 ||
                    !otherCharges.can_raise_pr ||
                    loading
                  }
                >
                  Save
                </AppButton>
              </ButtonWrapper>
            </>
          )}
        </Formik>
      )}
    </CustomModal>
  );
};

export default PaymentRequestModal;
