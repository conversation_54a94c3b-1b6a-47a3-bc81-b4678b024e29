import { useState } from 'react';

import {
  InfoRounded,
  Upload,
  Visibility as VisibilityIcon,
  Download as DownloadIcon,
  Receipt as ReceiptIcon,
} from '@mui/icons-material';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogTitle,
  Grid,
  IconButton,
  Link,
  Typography,
} from '@mui/material';

import { AppButton } from 'Components';
import ImageIcons from 'Components/AppIcons/ImageIcons.jsx';
import { confirmRecordArrival } from 'Services/token';
import { toFixedNumber } from 'Utilities';
import { INWARD_TYPE, INWARD_TYPE_PREFIX } from 'Utilities/constants';
import { GATEIN_TYPE, TOKEN_TABS } from 'Utilities/constants/lots';
import { getFormattedDate, getFormattedTime24Hours } from 'Utilities/dateUtils';

import useNotify from '../../hooks/useNotify';
import useRoleBasedAccess from '../../hooks/useRoleBasedAccess';

import { CustomTooltip, UnloadingData } from './Styled';

export const TokenCol = ({
  tab = '',
  data = '',
  gatein_id = '',
  auction_date = '',
  inward_type = '',
  mandi_type = '',
  satellite_name = '',
  rowData = {},
  onDownloadPriceSlip = () => {},
  onDownloadCustomerSlip = () => {},
}) => {
  const noLinkTabs = [
    TOKEN_TABS.PR_RAISED,
    TOKEN_TABS.PAID,
    TOKEN_TABS.PR_APPROVED,
    TOKEN_TABS.CANCELLED,
    TOKEN_TABS.SELF_SOLD,
  ];

  const showPriceSlipDownload = [
    TOKEN_TABS.AUCTION_READY,
    TOKEN_TABS.SOLD,
  ].includes(tab);

  const showCustomerSlipDownload = tab === TOKEN_TABS.SOLD;

  const { unloading_time = '' } = rowData || {};

  const handlePriceSlipDownload = () => {
    onDownloadPriceSlip(rowData);
  };

  const handleCustomerSlipDownload = () => {
    onDownloadCustomerSlip(rowData);
  };

  return (
    <>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <ImageIcons
          name={inward_type?.toLowerCase()}
          style={{ width: '20px', height: '20px' }}
        />
        <div style={{ marginLeft: '0.5rem' }}>
          {noLinkTabs.includes(tab) ? (
            <Typography>{data}</Typography>
          ) : (
            <Typography
              component='a'
              color='primary'
              href={`/app/gate-in/edit/${gatein_id}?auction_date=${auction_date}${
                tab === TOKEN_TABS.SOLD && '&sold=true'
              }`}
            >
              {data}
            </Typography>
          )}
        </div>
        {showPriceSlipDownload && (
          <IconButton
            size='small'
            onClick={handlePriceSlipDownload}
            title='Price Slip'
            style={{ marginLeft: '0.5rem' }}
          >
            <DownloadIcon fontSize='small' color='primary' />
          </IconButton>
        )}
        {showCustomerSlipDownload && (
          <IconButton
            size='small'
            onClick={handleCustomerSlipDownload}
            title='Download Customer Slip'
            style={{ marginLeft: '0.25rem' }}
          >
            <ReceiptIcon fontSize='small' color='secondary' />
          </IconButton>
        )}
      </div>
      <Typography
        component='div'
        variant='caption'
        className='disabled-text'
        color='textPrimary'
      >
        <b>{getFormattedDate(auction_date)}</b>
      </Typography>
      {satellite_name && (
        <Typography color='primary' variant='caption'>
          <b>{satellite_name} </b>
        </Typography>
      )}

      {unloading_time !== 0 && (
        <UnloadingData>
          <Upload style={{ padding: '2px' }} />
          <Typography variant='body1'>
            {getFormattedTime24Hours(unloading_time)}
          </Typography>
        </UnloadingData>
      )}
    </>
  );
};

export const FarmerCol = ({ rowData, getPartnerStatusUI }) => {
  const { farmer_name, farmer_address, farmer_id, lots_data } = rowData;
  const { gatein_type } = lots_data?.[0];

  return (
    <>
      {gatein_type === GATEIN_TYPE.SELF ? (
        <Typography>Self</Typography>
      ) : (
        <Typography
          component='a'
          color='primary'
          href={`/app/registration/farmer/${farmer_id}`}
        >
          {farmer_name}
        </Typography>
      )}

      <Typography component='div'>{farmer_address}</Typography>
      {getPartnerStatusUI(rowData)}
    </>
  );
};

const ProductInfo = ({
  product,
  auction_date,
  token_suffix,
  inward_type,
  id,
  isLink,
}) => (
  <Typography component='div' key={id}>
    {inward_type === INWARD_TYPE.CRATES ? (
      <>
        {product?.product_name} -- {product?.units} Unit |&nbsp;
        {isLink && (
          <Link
            href={`/app/regrade?
            auction_date=${auction_date}
            &token=${token_suffix}
            &product_id=${product?.product_id}
            &type=${INWARD_TYPE_PREFIX[inward_type]}`}
          >
            Regrade
          </Link>
        )}
      </>
    ) : (
      <>
        {product?.product_name} |&nbsp;
        {isLink && (
          <Link
            href={`/app/weighment?
            auction_date=${auction_date}
            &token=${token_suffix}
            &type=${INWARD_TYPE_PREFIX[inward_type]}`}
          >
            Trolley Weighment
          </Link>
        )}
      </>
    )}
  </Typography>
);

const ProductName = ({ product = {}, linkContent }) => (
  <>
    <Typography component='div'>
      {product?.product_name}{' '}
      {linkContent && (
        <>
          <span>|&nbsp;</span> {linkContent}
        </>
      )}
    </Typography>
  </>
);

export const ProductCol = ({
  tab = '',
  auction_date = '',
  lots_data = [],
  inward_type = '',
  token_suffix = '',
}) => {
  const totalUnit = lots_data?.reduce((acc, cur) => {
    acc[cur.product_id]
      ? (acc[cur.product_id].units += cur?.units || 0)
      : (acc[cur.product_id] = { ...cur });
    return acc;
  }, {});
  const totalUnitData = Object.values(totalUnit);

  const totalWeight = lots_data?.reduce((acc, cur) => {
    acc[cur.product_id]
      ? acc[cur.product_id]
      : (acc[cur.product_id] = { ...cur });
    return acc;
  }, {});
  const lotsData = Object.values(totalWeight);

  return (
    <Grid container>
      <Grid item md={12} xs={12}>
        {tab === TOKEN_TABS.TO_BE_GRADED
          ? totalUnitData?.map((item, id) => (
              <ProductInfo
                key={id}
                product={item}
                auction_date={auction_date}
                token_suffix={token_suffix}
                inward_type={inward_type}
                isLink
              />
            ))
          : tab === TOKEN_TABS.AUCTION_READY
            ? lotsData.map((item, id) => (
                <ProductName
                  key={id}
                  product={item}
                  linkContent={
                    <Link
                      href={`/app/auction?auction_date=${auction_date}&token=${token_suffix}&type=${INWARD_TYPE_PREFIX[inward_type]}`}
                    >
                      Auction
                    </Link>
                  }
                />
              ))
            : tab === TOKEN_TABS.CANCELLED
              ? totalUnitData.map((item, id) => (
                  <ProductInfo
                    key={id}
                    product={item}
                    auction_date={auction_date}
                    token_suffix={token_suffix}
                    inward_type={inward_type}
                    isLink={false}
                  />
                ))
              : lotsData?.map((item, id) => (
                  <ProductName key={id} product={item} />
                ))}
      </Grid>
    </Grid>
  );
};

export const UnitQuantityCol = ({
  tab = '',
  lots_data = [],
  auction_date = '',
  token_suffix = '',
  inward_type = '',
}) => {
  const totalWeight = lots_data.reduce((acc, cur) => {
    const { product_id, net_weight = 0, units = 0 } = cur;

    if (acc[product_id]) {
      acc[product_id].net_weight += net_weight;
      acc[product_id].units += units;
    } else {
      acc[product_id] = { ...cur };
    }
    return acc;
  }, {});

  const lotsData = Object.values(totalWeight);

  return (
    <Grid container>
      <Grid item md={12} xs={12}>
        {lotsData?.map((item, id) => {
          const {
            units = 0,
            net_weight = 0,
            weight = 0,
            is_delivered = false,
          } = item;

          const getContent = () => {
            if (tab === TOKEN_TABS.AUCTION_READY) {
              if (inward_type === INWARD_TYPE.CRATES) {
                return `${units} Units / ${toFixedNumber(net_weight)} Kg`;
              }
              return `${weight} Kg`;
            }
            if (tab === TOKEN_TABS.SOLD) {
              if (inward_type !== INWARD_TYPE.TRUCK) {
                return `${units} Units / ${toFixedNumber(net_weight) || 0} Kg`;
              }
              if (inward_type === INWARD_TYPE.TRUCK && is_delivered) {
                return `${toFixedNumber(net_weight) || 0} Kg`;
              }
              return (
                <>
                  Tare weight pending |&nbsp;
                  <Link
                    href={`/app/weighment?auction_date=${auction_date}&token=${token_suffix}&type=${INWARD_TYPE_PREFIX[inward_type]}`}
                  >
                    Trolley Weighment
                  </Link>
                </>
              );
            }
            return `${units} Units / ${toFixedNumber(net_weight)} Kg`;
          };

          return (
            <Typography key={id} component='div'>
              {getContent()}
            </Typography>
          );
        })}
      </Grid>
    </Grid>
  );
};

export const TotalValuesCol = ({
  tab = '',
  openModal = () => {},
  openBreakDownModalFnc = () => {},
  handleFarmerBill = () => {},
  rowData = {},
}) => {
  const {
    lots_data = [],
    inward_type = '',
    net_amount = '',
    negative_bill_discount = 0,
    instant_pay_eligibility,
    instant_pay_reject_reason = '',
  } = rowData;
  const disabled =
    inward_type === INWARD_TYPE.TRUCK && !lots_data?.[0]?.is_delivered;
  return (
    <Grid container direction="column">
      <Grid item md={12} xs={12}>
        <Box sx={{ display: 'flex' }}>
          <Typography
            component='div'
            onClick={() => openBreakDownModalFnc(rowData)}
            style={{
              textDecoration: 'underline',
              cursor: 'pointer',
              width: '40%',
            }}
          >
            {tab !== TOKEN_TABS.SOLD ? (
              <>₹ {toFixedNumber(net_amount + negative_bill_discount, 2)}</>
            ) : (
              <>₹ {toFixedNumber(net_amount, 2)}</>
            )}
          </Typography>
          {tab !== TOKEN_TABS.SELF_SOLD && (
            <Typography component='div'>
              <VisibilityIcon
                color='primary'
                onClick={() => handleFarmerBill(rowData)}
                style={{ cursor: 'pointer' }}
              />
            </Typography>
          )}
        </Box>
      </Grid>
      <Grid style={{ display: 'flex', alignItems: 'center' }}>
        {tab === TOKEN_TABS.SOLD ? (
          <>
            <Button
              color='primary'
              style={{ paddingLeft: 0, textAlign: 'start' }}
              onClick={() => openModal(rowData, net_amount)}
              disabled={disabled}
            >
              Payment Request
            </Button>
            {!instant_pay_eligibility && instant_pay_reject_reason?.length ? (
              <CustomTooltip
                arrow
                title={
                  <>
                    <Typography
                      color='error'
                      style={{ textDecoration: 'underline', fontSize: '14px' }}
                    >
                      Rejection Reason :
                    </Typography>
                    <Typography>{instant_pay_reject_reason}</Typography>
                  </>
                }
              >
                <IconButton>
                  <InfoRounded color='primary' />
                </IconButton>
              </CustomTooltip>
            ) : null}
          </>
        ) : tab === TOKEN_TABS.PR_RAISED ? (
          rowData?.lots_data?.[0]?.purchase_order_id && (
            <Button
              color='primary'
              size='large'
              onClick={() => openModal(rowData)}
              style={{ paddingLeft: 0, whiteSpace: 'nowrap' }}
            >
              PR: {rowData?.lots_data?.[0]?.payment_request_id}
            </Button>
          )
        ) : (
          lots_data?.[0]?.purchase_order_id && (
            <>
              <Typography color='primary' style={{ paddingLeft: 0 }}>
                <b>PR: {lots_data[0].payment_request_id}</b>&nbsp;
                {tab === TOKEN_TABS.PAID && 'Paid'}
              </Typography>
            </>
          )
        )}
      </Grid>
    </Grid>
  );
};

export const ActionCol = ({
  farmerTokenId = '',
  setRecordArrival = () => {},
  recordArrival,
}) => {
  const [openModal, setOpenModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const NotificationBar = useNotify();
  const isGateInRole = useRoleBasedAccess({ checkHasAccess: true });

  const handleSubmit = () => {
    setLoading(true);
    const reqBody = {
      farmer_token_id: farmerTokenId,
    };

    confirmRecordArrival(reqBody).then(({ responseData }) => {
      setOpenModal(false);
      NotificationBar(responseData);
      setRecordArrival(!recordArrival);
      setLoading(false);
    });
  };

  return (
    <>
      {isGateInRole && (
        <Typography
          color='primary'
          onClick={() => {
            setOpenModal(true);
          }}
          style={{ textDecoration: 'underline' }}
        >
          Record Arrival
        </Typography>
      )}

      <Dialog open={openModal}>
        <Box
          display='flex'
          flexDirection='column'
          alignItems='center'
          justifyContent='center'
          style={{ padding: '1rem' }}
        >
          <DialogTitle variant='subtitle1' style={{ padding: '1rem 0' }}>
            <b>Do you want to Confirm Record Arrival ?</b>
          </DialogTitle>
          <DialogActions>
            <AppButton
              onClick={() => setOpenModal(!openModal)}
              disabled={loading}
              variant='contained'
              color='inherit'
            >
              Cancel
            </AppButton>
            <AppButton
              onClick={handleSubmit}
              variant='contained'
              color='primary'
              disabled={loading}
            >
              Confirm
            </AppButton>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};
