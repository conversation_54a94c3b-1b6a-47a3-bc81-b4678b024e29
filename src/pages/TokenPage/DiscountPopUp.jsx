import React from 'react';

import { Typography } from '@mui/material';

import { AppButton } from 'Components';
import { toFixedNumber } from 'Utilities';

const DiscountPopUp = ({
  data,
  closeModal,
  setApplyDiscount,
  setShowDiscountPopUp,
  setInitialData,
}) => {
  return (
    <div style={{ marginTop: '1rem' }}>
      <div style={{ marginBottom: '3rem' }}>
        <div style={{ display: 'flex' }}>
          <Typography>
            <b>Total Amount </b>
          </Typography>
          <Typography color='primary'>
            <b> &nbsp; ₹ {toFixedNumber(data.net_amount, 2)}</b>
          </Typography>
        </div>
        <Typography>
          <b>
            Negative value PR cannot be raised. Do you want to apply discount of
            &nbsp;
            <span style={{ color: 'red' }}>
              ₹ {toFixedNumber(data.net_amount, 2)}
            </span>
          </b>
        </Typography>
      </div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'end',
          gap: 10,
        }}
      >
        <AppButton
          variant='contained'
          color='inherit'
          onClick={() => closeModal(setInitialData)}
        >
          Cancel
        </AppButton>
        <AppButton
          variant='contained'
          color='primary'
          onClick={() => {
            setApplyDiscount(true);
            setShowDiscountPopUp(false);
          }}
        >
          Apply
        </AppButton>
      </div>
    </div>
  );
};

export default DiscountPopUp;
