import { Card } from '@mui/material';
import { styled } from '@mui/material/styles';

export const StyledLink = styled('a')(({ theme }) => ({
  color: theme.palette.text.primary,
  textDecoration: 'none',
  transition: 'color 0.2s ease-in-out',
  '&:hover': {
    color: theme.palette.primary.main,
  },
}));

export const FullWidthCard = styled(Card)(({ theme }) => ({
  width: `calc(100% - ${theme.spacing(2)})`,
  position: 'absolute',
  top: '100%',
  left: theme.spacing(1),
  backgroundColor: theme.palette.background.paper,
  zIndex: 999,
  borderRadius: theme.spacing(1),
  boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
  border: `1px solid ${theme.palette.divider}`,
  marginTop: theme.spacing(1),
  overflow: 'hidden',
  '& a': {
    display: 'block',
    padding: theme.spacing(1.5, 2),
    borderBottom: `1px solid ${theme.palette.divider}`,
    transition: 'background-color 0.2s ease-in-out',
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
    },
    '&:last-child': {
      borderBottom: 'none',
    },
  },
}));

// For backward compatibility, export useStyles that returns class names
export const useStyles = () => ({
  hover: 'styled-link-hover',
  fullWidthElement: 'full-width-card',
});
