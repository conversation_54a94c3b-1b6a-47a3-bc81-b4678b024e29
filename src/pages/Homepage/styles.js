import { Card } from '@mui/material';
import { styled } from '@mui/material/styles';

export const StyledLink = styled('a')(({ theme }) => ({
  color: 'black',
  textDecoration: 'none',
  '&:hover': {
    color: theme.palette.primary.main,
  },
}));

export const FullWidthCard = styled(Card)(({ theme }) => ({
  width: `calc(100% - ${theme.spacing(2)})`,
  position: 'absolute',
  top: '4rem',
  backgroundColor: theme.palette.primary.contrastText,
  zIndex: 999,
  borderRadius: '0 0 5px 5px',
}));

// For backward compatibility, export useStyles that returns class names
export const useStyles = () => ({
  hover: 'styled-link-hover',
  fullWidthElement: 'full-width-card',
});
