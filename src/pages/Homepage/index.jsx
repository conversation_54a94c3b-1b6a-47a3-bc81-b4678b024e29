import { useEffect, useState } from 'react';

import { Grid, Typography } from '@mui/material';
import { Link } from 'react-router-dom';

import { useSiteValue } from 'App/SiteContext';
import PageLayout from 'Components/PageLayout';
import HomeItem from 'Pages/Homepage/HomeItem';
import { ROUTES } from 'Utilities/constants/routeList';
import { USER_PERMISSION } from 'Utilities/constants/userPermission';

import { StyledLink, FullWidthCard } from './styles';

const Homepage = () => {
  const [showOptions, setShowOptions] = useState(false);
  const [matchIndex, setMatchIndex] = useState('');
  const { satelliteList, userInfo } = useSiteValue();
  const [balance, setBalance] = useState(0);

  const dependencyCheck = [showOptions];
  const allDependenciesValid = dependencyCheck.every(dep => dep);

  const allowedTabs = USER_PERMISSION() || [];

  const ALLOWED_TABS_ID = allowedTabs.map(({ id = '' }) => id);
  const ALLOWED_ROUTES = ROUTES.filter(({ id = '' }) =>
    ALLOWED_TABS_ID.includes(id)
  );

  const showDropdown = (route, index) =>
    (allDependenciesValid && route.subRoute && matchIndex === index) ||
    (route.childrenDropdown && showOptions && matchIndex === index);

  return (
    <PageLayout title='Homepage' showSelectMandi>
      <PageLayout.Body style={{ overflowY: 'auto' }}>
        <Grid container spacing={2} style={{ position: 'relative' }}>
          {ALLOWED_ROUTES.map((route, index) => (
            <Grid
              key={index}
              item
              md={4}
              xs={12}
              style={{ position: 'relative' }}
            >
              <HomeItem
                title={route.title}
                url={route.url}
                description={route.description}
                subRoute={route.subRoute}
                route={route}
                setShowOptions={setShowOptions}
                setMatchIndex={setMatchIndex}
                index={index}
                hoverStyle='styled-link-hover'
              />
              {showDropdown(route, index) && (
                <FullWidthCard>
                  {route.children?.map(child => (
                    <Link
                      to={`${route.url + child.url}`}
                      key={`${child.label}`}
                      component={StyledLink}
                      data-cy={`mandi.home.${route.id}`}
                    >
                      <Typography
                        variant='h6'
                        style={{ padding: '0.55rem 1rem' }}
                      >
                        {child.label}
                      </Typography>
                    </Link>
                  ))}
                </FullWidthCard>
              )}
            </Grid>
          ))}
        </Grid>
      </PageLayout.Body>
    </PageLayout>
  );
};

export default Homepage;
