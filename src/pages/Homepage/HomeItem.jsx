import React from 'react';

import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  Box,
  Card,
  CardActionArea,
  CardContent,
  Typography,
} from '@mui/material';
import { Link } from 'react-router-dom';

import { useSiteValue } from 'App/SiteContext';
import { toFixedNumber } from 'Utilities';

const HomeItem = ({
  title,
  description,
  subRoute,
  url,
  hoverStyle,
  route,
  setShowOptions,
  setMatchIndex,
  index,
  ...rest
}) => {
  return (
    <>
      <Card {...rest}>
        <CardActionArea>
          <CardContent
            style={
              route.childrenDropdown
                ? { padding: '7px 16px' }
                : { padding: '16px' }
            }
          >
            <Link
              to={url}
              className={hoverStyle}
              style={{ textDecoration: 'none', color: 'inherit' }}
            >
              <Typography variant='h5'>{title}</Typography>
              <Typography variant='body2'>{description}</Typography>
            </Link>
          </CardContent>
        </CardActionArea>
      </Card>
    </>
  );
};

export default HomeItem;
