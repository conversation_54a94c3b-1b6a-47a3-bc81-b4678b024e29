import React from 'react';

// Lazy load existing components
const Login = React.lazy(() => import('./Login/index.jsx'));
const Homepage = React.lazy(() => import('./Homepage/index.jsx'));
const PlaceholderPage = React.lazy(() => import('./PlaceholderPage.jsx'));
const GateIn = React.lazy(() => import('./GateIn/index.jsx'));

const TokenPage = React.lazy(() => import('./TokenPage/index.jsx'));
const Registration = React.lazy(() => import('./Registration/index.jsx'));

const AuctionGrading = React.lazy(() => import('./AuctionGrading/index.jsx'));


export default {
  LOGIN_PAGE: <Login />,
  HOME_PAGE: Homepage,
  GATE_IN: GateIn,
  REGISTRATION_PAGE: Registration,
  TOKEN_PAGE: TokenPage,
  AUCTION_GRADING: AuctionGrading,
};
