import React from 'react';

import ReactApexChart from 'react-apexcharts';

const RadialChart = props => {
  const chartOptions = {
    series: [props.series],
    options: {
      chart: {
        type: 'radialBar',
        toolbar: {
          show: false,
        },
      },
      plotOptions: {
        radialBar: {
          startAngle: -135,
          endAngle: 135,
          hollow: {
            margin: 0,
            size: '55%',
            background: '#fff',
            image: undefined,
            imageOffsetX: 0,
            imageOffsetY: 0,
            position: 'front',
            dropShadow: {
              enabled: false,
              top: 3,
              left: 0,
              blur: 4,
              opacity: 0.24,
            },
          },
          track: {
            background: '#fff',
            strokeWidth: '67%',
            margin: 0, //! margin is in pixels
            dropShadow: {
              enabled: true,
              top: -3,
              left: 0,
              blur: 4,
              opacity: 0.35,
            },
          },

          dataLabels: {
            show: true,
            name: {
              offsetY: -10,
              show: true,
              color: '#888',
              fontSize: '14px',
            },
            value: {
              formatter(val) {
                return +val;
              },
              color: '#111',
              fontSize: '14px',
              show: true,
            },
          },
        },
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'dark',
          type: 'horizontal',
          shadeIntensity: 0,
          gradientToColors: ['#2d3941'],
          inverseColors: true,
          opacityFrom: 1,
          opacityTo: 1,
          stops: [1, 100],
        },
      },
      stroke: {
        lineCap: 'round',
      },
      labels: [props.label],
    },
  };
  return (
    <div id='card'>
      <div id='chart'>
        <ReactApexChart
          options={chartOptions.options}
          series={chartOptions.series}
          type='radialBar'
          height={170}
        />
      </div>
    </div>
  );
};

export default RadialChart;
