import React from 'react';

import ReactApexChart from 'react-apexcharts';

const LineChart = props => {
  const chartOptions = {
    series: [
      {
        name: 'Arrival',
        data: props.data,
      },
    ],
    options: {
      chart: {
        type: 'line',
        dropShadow: {
          enabled: false,
          color: '#000',
          top: 18,
          left: 7,
          blur: 10,
          opacity: 0.2,
        },
        toolbar: {
          show: false,
        },
      },
      colors: ['#2d3941'],
      dataLabels: {
        enabled: true,
      },
      stroke: {
        curve: 'smooth',
      },
      title: {
        text: '',
        align: 'left',
      },
      grid: {
        borderColor: '#e7e7e7',
        row: {
          colors: ['#f3f3f3', 'transparent'], //! takes an array which will be repeated on columns
          opacity: 0.5,
        },
        xaxis: {
          lines: {
            show: false, //! or just here to disable only x axis grids
          },
        },
        show: false,
      },
      markers: {
        size: 1,
      },
      xaxis: {
        labels: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
        axisBorder: {
          show: false,
        },
        tooltip: {
          enabled: false,
        },
      },
      yaxis: {
        labels: {
          show: false,
        },
        min: 5,
        max: 40,
      },
      legend: {
        position: 'top',
        horizontalAlign: 'right',
        floating: true,
      },
    },
  };

  return (
    <div id='card'>
      <div id='chart'>
        <ReactApexChart
          options={chartOptions.options}
          series={chartOptions.series}
          type='line'
          height={100}
        />
      </div>
    </div>
  );
};

export default LineChart;
