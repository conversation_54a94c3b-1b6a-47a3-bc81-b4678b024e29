// src/config/envConfig.js
const ENV = import.meta.env.VITE_APP_ENV || 'staging';

const config = {
  development: {
    supplyChainService: 'http://localhost:3000',
    CRMUrl: 'http://localhost:3000',
    mandiService: 'http://localhost:6700/mandiservice',
    gateInService: 'http://localhost:8421/gateinservice',
    velensService: 'http://localhost:3001/',
    uiAssets: 'https://qa-assets.vegrow.in/',
    MandiOpsUi: 'https://mandi-staging.vegrow.in/',
  },
  staging: {
    nonFruit: 'https://nonfruitstaging.vegrow.in/',
    supplyChainService: 'https://crmstaging.vegrow.in/',
    CRMUrl: 'https://crmstaging.vegrow.in/',
    mandiService: 'https://mandistaging.vegrow.in/mandiservice',
    gateInService: 'https://gateinstaging.vegrow.in/gateinservice',
    veAuctionUrl: 'https://veauction-db9d0.web.app/',
    otpUrl: 'https://otpstaging.vegrow.in/',
    velensService: 'https://assessment-staging.vegrow.in/',
    uiAssets: 'https://qa-assets.vegrow.in/',
    MandiOpsUi: "https://mandi-staging.vegrow.in/",
  },
  production: {
    supplyChainService: 'https://mandivelynk.vegrow.in/',
    CRMUrl: 'https://mandivelynk.vegrow.in/',
    mandiService: 'https://mandiservice.vegrow.in/mandiservice',
    gateInService: 'https://gateinservice.vegrow.in/gateinservice',
    veAuctionUrl: 'https://fruitx.live/',
    otpUrl: 'https://otpservice.vegrow.in/',
    nonFruit: 'https://nfi.vegrow.in',
    velensService: 'https://assessment.vegrow.in/',
    uiAssets: 'https://assets.vegrow.in/',
    MandiOpsUi: 'https://ops.fruitx.com/',
  },
};

export default config[ENV];
