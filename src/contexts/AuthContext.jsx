import React, { createContext, useContext, useState, useEffect } from 'react';

import { useNavigate } from 'react-router-dom';

import { getUserProfile, userLogout } from 'Services/users';

import routes from '../config/routes';
import {
  getUserData,
  isAuthenticated,
  removeUser,
  saveUserData,
} from '../utilities/localStorage';

// Create authentication context
const AuthContext = createContext();

/**
 * AuthProvider component that manages authentication state
 * @param {Object} props - Component props
 * @returns {JSX.Element} AuthProvider component
 */
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  // Check for existing authentication on mount and redirect if needed
  useEffect(() => {
    console.log('AuthContext mounted - checking authentication');

    const checkAuth = async () => {
      console.log('Running checkAuth()');
      try {
        // Check if user is authenticated based on localStorage
        const isAuth = isAuthenticated();
        console.log('isAuthenticated:', isAuth);

        if (isAuth) {
          // Get user data from localStorage
          const userData = getUserData();
          console.log('Retrieved userData from localStorage:', !!userData);
          setUser(userData);

          try {
            // Fetch current user profile to validate the session
            console.log('Fetching current user profile...');
            const currentUserData = await getUserProfile();
            console.log('Current user profile API response:', currentUserData);

            if (currentUserData) {
              console.log('Saving updated user data');
              // Merge with existing data and save
              const updatedUserData = {
                ...userData,
                ...currentUserData,
                // Ensure we preserve the authentication token
                authentication_token: userData.authentication_token,
              };

              // Update localStorage and state with fresh data
              saveUserData(updatedUserData);
              setUser(updatedUserData);
            }
          } catch (profileError) {
            console.error('Error validating user session:', profileError);

            // If we get a 401 error, the token is invalid - log the user out
            if (profileError?.response?.status === 401) {
              console.warn('Session expired or invalid, redirecting to login');
              removeUser();
              setUser(null);
              // We'll handle the navigation after setting loading to false
            } else {
              // For other errors, continue using cached data
              setUser(userData);
            }
          }
        }
      } catch (error) {
        console.error('Error in auth check:', error);
        // Clear user data if there's an error in the auth check
        removeUser();
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    // Execute the check
    checkAuth();
  }, [navigate]);

  // Handle navigation based on authentication state
  useEffect(() => {
    // Only take action after loading is complete
    if (!loading) {
      const currentPath = window.location.pathname;
      console.log(
        'Auth state determined - Path:',
        currentPath,
        'User:',
        !!user
      );

      if (!user) {
        // If not authenticated and not on the login page, redirect to login
        if (currentPath !== routes.login) {
          console.log('Not authenticated, redirecting to login');
          navigate(routes.login);
        }
      } else {
        // User is authenticated
        // If on login page, redirect to dashboard
        if (currentPath === routes.login) {
          console.log('Already authenticated, redirecting to dashboard');
          navigate(routes.dashboard);
        }
      }
    }
  }, [loading, user, navigate]);

  /**
   * Handle login success
   * @param {Object} userData - User data from login response
   */
  const loginSuccess = async userData => {
    if (!userData) {
      console.error('Login success called with empty data');
      return;
    }

    try {
      console.log('Processing login success with data:', userData);

      // Ensure we have the authentication token in the correct format
      if (!userData.authentication_token && userData.token) {
        console.log('Using token property as authentication_token');
        userData.authentication_token = userData.token;
      }

      // Ensure the authentication token is present
      if (!userData.authentication_token) {
        console.error('No authentication token found in login response!');
        console.log('Response data:', userData);
        return; // Don't proceed with login if no token
      } else {
        console.log(
          'Authentication token found:',
          userData.authentication_token.substring(0, 10) + '...'
        );
      }

      // Create the user object with all required fields
      const userToSave = {
        ...userData,
        // Ensure these fields exist for the isAuthenticated check
        authentication_token: userData.authentication_token,
        email: userData.email || '<EMAIL>',
        name: userData.name || 'User',
        // Add any other required fields
      };

      // Save the enhanced user data with the token
      saveUserData(userToSave);

      // Update the user state
      setUser(userToSave);

      // Double check our user state and localStorage before navigation
      const storedUser = JSON.parse(localStorage.getItem('user') || 'null');
      console.log(
        'Stored user data has token:',
        !!storedUser?.authentication_token
      );

      try {
        // Fetch user profile data after successful login
        console.log('Fetching user profile after login');
        const userProfileData = await getUserProfile();
        console.log('User profile data received:', userProfileData);

        if (userProfileData) {
          // Merge the user profile data with the existing user data
          const completeUserData = {
            ...userToSave,
            ...userProfileData,
            // Ensure we don't lose the authentication token
            authentication_token: userToSave.authentication_token,
          };

          // Update user data with the complete profile
          console.log('Updating user data with profile information');
          saveUserData(completeUserData);
          setUser(completeUserData);
        }
      } catch (profileError) {
        console.error('Error fetching user profile after login:', profileError);
        // Continue with navigation even if profile fetch fails
      }

      // Navigate to dashboard after profile fetch (or any error)
      console.log('Redirecting to dashboard...');
      navigate(routes.dashboard);
    } catch (error) {
      console.error('Error in loginSuccess:', error);
      // Attempt to navigate anyway
      navigate(routes.dashboard);
    }
  };

  /**
   * Handle logout
   */
  const logout = async () => {
    try {
      // Call logout API if user is authenticated
      if (isAuthenticated()) {
        await userLogout();
      }
    } catch (error) {
      console.error('Logout API error:', error);
    } finally {
      // Clear both user data and direct token
      removeUser();
      localStorage.removeItem('authToken');
      setUser(null);
      navigate(routes.login);
    }
  };

  // Create context value
  const authContextValue = {
    user,
    isAuthenticated: !!user,
    loading,
    loginSuccess,
    logout,
  };

  return (
    <AuthContext.Provider value={authContextValue}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Custom hook to use the auth context
 * @returns {Object} Auth context value
 */
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
