import { supplyChainService } from 'Services/base';

export const getPurchaseOrders = params => {
  return supplyChainService.get('/purchase_orders.json', { params });
};

export const getPurchaseOrderById = id => {
  return supplyChainService.get(`purchase_orders/${id}.json`);
};

export const createPurchaseOrder = data => {
  return supplyChainService.post('purchase_orders.json/', data);
};

export const createDirectPurchageOrder = data => {
  return supplyChainService.post('purchase_orders/create_direct_po.json', data);
};

export const updatePurchaseOrder = (data, id) => {
  return supplyChainService.put(`purchase_orders/${id}.json`, data);
};

export const updateDirectPurchaseOrder = (data, id) => {
  return supplyChainService.put(
    `purchase_orders/${id}/update_direct_po.json`,
    data
  );
};

export const deletePurchaseOrder = id => {
  return supplyChainService.delete(`purchase_orders/${id}.json`);
};

export const getPurchaseOrderItems = id => {
  return supplyChainService.get(`purchase_orders/${id}/purchase_items.json`);
};

export const createPurchaseOrderItems = (data, id) => {
  return supplyChainService.post(
    `purchase_orders/${id}/purchase_items/create_many.json`,
    data
  );
};

export const updatePurchaseOrderItems = (data, id) => {
  return supplyChainService.put(
    `purchase_orders/${id}/purchase_items/update_many.json`,
    data
  );
};

export const deletePurchaseOrderItems = (itemId, id) => {
  return supplyChainService.delete(
    `purchase_orders/${id}/purchase_items/${itemId}.json`
  );
};

export const getBuyers = params => {
  return supplyChainService.get('buyers.json/', { params });
};

export const getFarmers = params => {
  return supplyChainService.get('farmers.json/', { params });
};

export const getPartners = params => {
  return supplyChainService.get('vendors.json/', { params });
};

export const getSuppliers = params => {
  return supplyChainService.get('suppliers.json/', { params });
};

export const getFieldExecutive = params => {
  return supplyChainService.get('field_executives.json/', { params });
};

export const getServiceProvider = params => {
  return supplyChainService.get('partners/list.json/', { params });
};

export const getProducts = params => {
  return supplyChainService.get('products.json/', { params });
};

export const getPaymentsById = params => {
  return supplyChainService.get('payments.json/', { params });
};

export const getMicroPockets = params => {
  return supplyChainService.get('micro_pockets.json/', { params });
};

export const getPendingCrates = params => {
  return supplyChainService.get(
    '/nfi/packaging_items/pending_partner_summary.json',
    { params }
  );
};
