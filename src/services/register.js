import { supplyChainService } from 'Services/base';

export const registerPartner = data => {
  return supplyChainService.post('partners.json', data);
};

export const updatePartner = (id, data) => {
  return supplyChainService.put(`partners/${id}.json`, data);
};

export const getPartnerDetails = id => {
  return supplyChainService.get(`partners/${id}`);
};

export const getPartnerList = (role, params) => {
  return supplyChainService.get(`partners/list.json?${role}s=true`, { params });
};

export const getStatesList = () => {
  return supplyChainService.get('locations/states.json');
};

export const getDistrictsList = state => {
  return supplyChainService.get(`locations/districts.json?state=${state}`);
};
