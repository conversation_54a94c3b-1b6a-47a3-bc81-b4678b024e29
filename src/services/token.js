import { gateInService, mandiService } from 'Services/base';

/**
 *
 * @param {*} params
 * @returns
 */
export const getTokenListing = params => {
  return mandiService.get('/auction_lot', { params });
};

/**
 *
 * @param {*} params
 * @returns
 */

export const getToBeGraded = params => {
  return gateInService.get('/lots', { params });
};

/**
 *
 * @param {*} params
 * @returns
 */

export const getTabsCount = params => {
  return mandiService.get('/lotstatuscounts', { params });
};

/**
 *
 * @param {*} data
 * @returns
 */

export const createDirectPO = data => {
  return mandiService.post('/directpo', data);
};

/**
 *
 * @param {*} data
 * @returns
 */

export const confirmRecordArrival = data => {
  return gateInService.put('/farmertoken/recordarrival', data);
};
