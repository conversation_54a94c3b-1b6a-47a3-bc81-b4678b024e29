import axios from 'axios';

import envConfig from '../../config/envConfig';
import routes from '../../config/routes';
import {
  getUserData,
  removeUser,
  getCoordinates,
} from '../../utilities/localStorage';

// API base URLs based on environment
const API = envConfig;

const serviceConfig = {
  timeout: 45000,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
  crossDomain: true,
};

/**
 * Create and configure an axios instance with interceptors for API calls
 * @param {string} baseURL - Base URL for the service
 * @param {Object} options - Additional options
 * @returns {Object} Configured axios instance
 */
const getServiceInstance = (baseURL, options = {}) => {
  const pendingRequests = new Map();

  // Create axios instance with config
  const serviceInstance = axios.create({
    ...serviceConfig,
    baseURL,
  });

  // Request interceptor for API calls
  serviceInstance.interceptors.request.use(
    async config => {
      try {
        console.log(
          `API Request: ${config.method.toUpperCase()} ${config.url}`
        );

        // Get user data from localStorage
        const userData = getUserData();

        // Add auth token to headers if available
        if (userData && userData.authentication_token) {
          console.log('Adding auth token to request');
          config.headers['Authorization'] =
            `Bearer ${userData.authentication_token}`;
        } else {
          console.warn('No auth token available for request to:', config.url);
        }

        // Add dcid header - required for API calls
        config.headers['dcid'] = '147';

        // Add coordinates to headers
        // try {
        //   const coords = await getCoordinates();
        //   if (coords.latitude && coords.longitude) {
        //     config.headers['x-latitude'] = coords.latitude;
        //     config.headers['x-longitude'] = coords.longitude;
        //   }
        // } catch (geoError) {
        //   console.warn('Could not get coordinates:', geoError);
        // }

        return config;
      } catch (error) {
        console.error('Error in request interceptor:', error);
        return config;
      }
    },
    error => {
      console.error('Request interceptor error:', error);
      return Promise.reject(error);
    }
  );

  // Response interceptor
  serviceInstance.interceptors.response.use(
    response => {
      // Return just the data portion of the response
      return response.data;
    },
    error => {
      // Handle request timeout
      if (error.code === 'ECONNABORTED') {
        const timeoutError = {
          status: 504,
          message: 'Request Timed Out',
        };
        return Promise.reject(timeoutError);
      }

      // Handle authentication error - redirect to login
      if (
        error.response &&
        error.response.status === 401 &&
        error.config &&
        error.config.url !== 'login'
      ) {
        removeUser();
        window.location.href = routes.login;
        return Promise.reject(error);
      }

      return Promise.reject(error);
    }
  );

  return serviceInstance;
};

// Export configured service instances
export const supplyChainService = getServiceInstance(API.supplyChainService);
export const mandiService = getServiceInstance(`${API.mandiService}/`);
export const gateInService = getServiceInstance(`${API.gateInService}/`);
export const otpService = getServiceInstance(`${API.otpUrl}`);
export const velensService = getServiceInstance(`${API.velensService}/`);
