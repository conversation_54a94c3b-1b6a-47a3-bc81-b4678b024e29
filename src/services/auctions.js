import { mandiService, supplyChainService } from 'Services/base';

export const getAuctionLots = params =>
  mandiService.get('/auction_lot', { params });

export const updateAuctionLot = params =>
  mandiService.put('/auction_lot', params);

export const getSalesPagesCounts = params =>
  mandiService.get('/lotstatuscounts', { params });

export const getFirebaseAuctionId = params =>
  mandiService.get('/auction', { params });

export const getMandiPriceComparison = params =>
  supplyChainService.get('/mandi_price_comparison_configs', { params });
