import { gateInService } from 'Services/base';

/**
 * Helper method to post a getin object
 * @param {*} params
 * @returns
 */
export const createGateIn = (data, params) => {
  return gateInService.post('/apple-gatein', data, { params });
};

/**
 * Helper method to post a getin object
 * @param {*} params
 * @returns
 */
export const updateGateIn = (data, params) => {
  return gateInService.put('/apple-gatein', data, { params });
};

/**
 * Helper method to get getin object
 * @param {*} params
 * @returns
 */
export const getGateInById = params => {
  return gateInService.get('/apple-gatein', { params });
};

/**
 * Helper method to get refreshed token
 * @param {*} params
 * @returns
 */
export const getToken = params => {
  return gateInService.get('/token', { params });
};

export const cancelToken = data => {
  return gateInService.put('/farmertoken', data);
};
