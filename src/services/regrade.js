import { gateInService, mandiService, supplyChainService } from 'Services/base';

export const getRegradeDetails = params => {
  return gateInService.get('lots?for_grading=true', {
    params,
  });
};

export const getGradeList = params => {
  return supplyChainService.get('graders.json', {
    params,
  });
};

export const getSkuList = mandi_id => {
  return supplyChainService.get(`/mandis/${mandi_id}/skus.json`);
};

export const getSkuSizes = mandi_id => {
  return supplyChainService.get(`/mandis/${mandi_id}/sku_sizes.json`);
};

export const getGateinSkuList = mandi_id => {
  return supplyChainService.get(`/mandis/${mandi_id}/gatein_skus.json`);
};

export const getProducts = mandi_id => {
  return supplyChainService.get(`/mandis/${mandi_id}/products.json`);
};

export const submitRegrade = data => {
  return gateInService.post('grading', data);
};

export const updateRegradeLots = data => {
  return gateInService.put('grading', data);
};

export const deleteRegradeLots = lot_data => {
  return gateInService.delete('grading', { data: { ...lot_data } });
};

export const getPackTypeDetails = params => {
  return mandiService.get('packagingtypes', { params });
};

export const getPackagingTypeDetails = params => {
  return mandiService.get('skupackagingtypemapping', { params });
};

export const getProcessChargeList = params => {
  return mandiService.get('processcharges', { params });
};
