import { mandiService, supplyChainService } from 'Services/base';

/**
 * Helper method to get list of payment request related to vendor remaining amount
 * @param {*} params
 * @returns
 */
export const getVendorAdjustmentListing = params => {
  return supplyChainService.get(
    'payment_requests/get_vendor_ajustment_payment_requests.json',
    { params }
  );
};

/**
 * Helper method to get payment request detail by id
 * @param {*} id
 * @param {*} params
 * @returns
 */
export const getPaymentRequestById = (id, params) => {
  return supplyChainService.get(`payment_requests/${id}.json`, { params });
};

/**
 * Helper to create a new payment request
 * @param {*} params
 * @returns
 */
export const createPaymentRequest = params => {
  return supplyChainService.post('payment_requests.json', params);
};

/**
 * Helper to update a payment request by id
 * @param {*} params
 * @param {*} id
 * @returns
 */
export const updatePaymentRequest = (params, id) => {
  return supplyChainService.put(`payment_requests/${id}.json`, params);
};

/**
 * Helper to delete a payment request
 * @param {*} id
 * @param {*} params
 * @returns
 */
export const deletePaymentRequestById = (id, params) => {
  return supplyChainService.delete(`payment_requests/${id}.json`, params);
};

/**
 *
 * @param {vendor_id} params
 * @returns
 */
export const getAdvancePayment = params => {
  return mandiService.get('/pendingadvancepayment', { params });
};

/**
 *
 * @param {*} data
 * @returns
 */

export const createPR = data => {
  return mandiService.post('/raisepr', data);
};

/**
 *
 * @param {*} data
 * @returns
 */

export const updatePR = (data, id) => {
  return mandiService.put('/raisepr', data);
};

export const getPaymentListing = params => {
  return supplyChainService.get('payment_requests.json', { params });
};

/**
 * Helper to approve or reject a payment request
 * @param {*} id
 * @param {*} params
 * @returns
 */
export const approveOrRejectPayment = (id, params) => {
  return supplyChainService.put(`payment_requests/${id}.json`, params);
};
