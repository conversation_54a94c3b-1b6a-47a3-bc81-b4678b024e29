# Apple FruitX Panel

## Overview

Apple FruitX Panel (mandi-apple-fe) is a comprehensive administration panel built with React and Vite to manage and monitor the Vegrow platform. This application provides a robust, scalable, and maintainable user interface for administrators to perform their tasks efficiently.

## Key Features

- **Dynamic Form Builder**: Build complex forms with conditional fields and validation
- **Responsive Design**: Works on all device sizes
- **Component-Based Architecture**: Modular design for maintainability
- **SSO Authentication**: Secure login via Single Sign-On
- **Environment-Specific Configuration**: Development, integration, and production settings

## Technology Stack

- **React**: UI library
- **Vite**: Build tool and development server
- **Material UI (MUI)**: Component library for consistent design
- **Formik**: Form management
- **Yup**: Form validation
- **React Router**: Navigation
- **Dayjs**: Date manipulation
- **Google OAuth**: Authentication

## Getting Started

### Prerequisites

- Node.js (v16+)
- npm or yarn

### Installation

```bash
# Clone the repository
git clone <repository-url>

# Navigate to the project directory
cd mandi-apple-fe

# Install dependencies
npm install

# Start the development server with Vite
npm run dev
```

The application will be available at [http://localhost:9000](http://localhost:9000).

## Environment Configuration

The application supports different environments:

- `.env.development` - Local development settings (PORT=9000)
- `.env.integration` - Integration environment settings
- `.env.production` - Production environment settings

To run with a specific environment configuration:

```bash
# Development (default)
npm start

# Integration
npm run start:integration

# Production
npm run start:production
```

## Project Structure

```
src/
├── components/       # UI components
│   ├── common/       # Shared components
│   ├── form/         # Form builder and fields
│   └── examples/     # Example implementations
├── hooks/            # Custom hooks
├── layouts/          # Page layouts
├── pages/            # Application pages
├── theme/            # Theme configuration
├── utilities/            # Utility functions
├── App.js            # App component
└── index.js          # Entry point
```

## Form Builder System

The Form Builder system is a key feature that allows for quick creation of complex forms with conditional logic and validation.

### Basic Usage

```jsx
import { FormBuilder } from '../components/form';

const MyForm = () => {
  // Define fields
  const fields = [
    {
      name: 'email',
      label: 'Email Address',
      type: 'email',
      validations: ['email'],
      helperText: 'Enter your email address',
      gridProps: { md: 6 },
    },
    // Add more fields as needed
  ];

  // Handle form submission
  const handleSubmit = values => {
    console.log('Form values:', values);
    // Process form values
  };

  return (
    <FormBuilder
      fields={fields}
      initialValues={{ email: '' }}
      onSubmit={handleSubmit}
      title='Contact Form'
      submitButtonText='Submit'
    />
  );
};
```

### Field Types

The Form Builder supports various field types:

- `text`: Text input
- `email`: Email input
- `password`: Password input
- `number`: Number input
- `tel`: Telephone input
- `select`: Dropdown select
- `multiselect`: Multiple select with chips
- `checkbox`: Checkbox input
- `radio`: Radio button group
- `date`: Date picker
- `file`: File upload

### Conditional Fields

Fields can be conditionally displayed based on the values of other fields:

```jsx
{
  name: 'otherReason',
  label: 'Please specify',
  type: 'text',
  // Only show this field if 'reason' is 'other'
  conditional: (values) => values.reason === 'other'
}
```

### Validation

Built-in validation rules include:

- `required`: Field must have a value
- `email`: Must be a valid email address
- `phone`: Must be a valid phone number
- `min`: Minimum length/value
- `max`: Maximum length/value
- `positiveNumber`: Must be a positive number
- And many more...

For more information about the Form Builder, see the [Form Builder Documentation](./docs/form-builder.md).

## Documentation

Detailed documentation about project features, workflows, and components can be found in the [docs](./docs) directory:

- [Authentication Flow](./docs/authentication.md)
- [Form Builder](./docs/form-builder.md)
- [Project Architecture](./docs/architecture.md)
- [User Workflows](./docs/workflows.md)

## Available Scripts

- `npm run dev` - Runs the app in development mode using Vite
- `npm run build` - Builds the app for production using Vite
- `npm run preview` - Preview the production build locally
- `npm run storybook` - Runs Storybook for component development
- `npm run build-storybook` - Builds Storybook for production

## Contributing

1. Ensure any new components follow the existing architecture
2. Maintain components under 200 lines of code
3. Break pages into smaller component chunks
4. Update documentation when making changes
5. Follow the established coding style

## License

[License information]
