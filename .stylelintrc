{
  "defaultSeverity": "warning",
  "processors": [
    "stylelint-processor-styled-components"
  ],
  "extends": [
    "stylelint-config-standard",
    "stylelint-config-styled-components",
    "stylelint-config-rational-order"
  ],
  "plugins": [
    "stylelint-prettier",
    "stylelint-order",
    "stylelint-config-rational-order/plugin"
  ],
  "rules": {
    "prettier/prettier": [
      true,
      {
        "singleQuote": true,
        "tabWidth": 2
      }
    ],
    "order/properties-order": [],
    "plugin/rational-order": [
      true,
      {
        "border-in-box-model": false,
        "empty-line-between-groups": false,
      }
    ],
    "block-opening-brace-space-before": "always",
    "block-closing-brace-newline-after": [
      "always",
      {
        "ignoreAtRules": [
          "if",
          "else"
        ]
      }
    ],
    "max-empty-lines": 1,
    "declaration-empty-line-before": "never",
    "at-rule-name-space-after": "always",
    "rule-empty-line-before": "always",
  },
  "syntax": "scss"
}