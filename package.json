{"name": "mandi-apple-fe", "version": "0.1.0", "private": true, "dependencies": {"@date-io/dayjs": "^3.2.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mdi/js": "^7.4.47", "@mdi/react": "^1.6.1", "@mui/icons-material": "^7.1.0", "@mui/lab": "^7.0.0-beta.14", "@mui/material": "^7.2.0", "@mui/styles": "^6.5.0", "@mui/x-date-pickers": "^8.3.0", "@react-oauth/google": "^0.12.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "activestorage": "^5.2.8-1", "any-number-to-words": "^2.0.5", "axios": "^1.9.0", "browser-image-compression": "^2.0.2", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "formik": "^2.4.6", "jwt-decode": "^4.0.0", "notistack": "^3.0.2", "query-string": "^9.2.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-qr-code": "^2.0.18", "react-qr-reader": "^3.0.0-beta-1", "react-router-dom": "^7.6.0", "react-swipeable-views": "^0.14.0", "react-swipeable-views-utils": "^0.14.0", "react-to-print": "^3.1.1", "react-webcam": "^7.2.0", "styled-components": "^6.1.19", "web-vitals": "^2.1.4", "yup": "^1.6.1"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx,ts,tsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "devDependencies": {"@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/react-vite": "^8.6.12", "@storybook/test": "^8.6.12", "@vitejs/plugin-react": "^4.7.0", "eslint": "^8.57.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.32.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "eslint-plugin-storybook": "^0.12.0", "prettier": "^3.6.2", "prop-types": "^15.8.1", "storybook": "^8.6.12", "vite": "^7.0.6"}}