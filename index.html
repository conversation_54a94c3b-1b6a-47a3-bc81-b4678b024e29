<!--
  This file is served by Vite for development and used as the template for production builds.
  See https://vitejs.dev/guide/#index-html-and-project-root for more info.
-->
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="icon" type="image/png" sizes="192x192" href="/favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Web site created using Vite and React" />
    <link rel="apple-touch-icon" href="/logo192.png" />

    <link rel="manifest" href="/manifest.json" />

    <title>Apple FruitX</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <!-- Vite injects your JS bundle here in development and build -->
    <div id="root"></div>
    <script type="module" src="/src/app/index.jsx"></script>
  </body>
</html>
