import { fileURLToPath, URL } from 'node:url';

import react from '@vitejs/plugin-react';
import { defineConfig } from 'vite';

export default defineConfig({
  plugins: [react()],
  server: {
    port: 9000,
  },
  resolve: {
    alias: {
      // Path aliases for absolute imports
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      'Components': fileURLToPath(new URL('./src/components', import.meta.url)),
      'App': fileURLToPath(new URL('./src/app', import.meta.url)),
      'Pages': fileURLToPath(new URL('./src/pages', import.meta.url)),
      'Utilities': fileURLToPath(new URL('./src/utilities', import.meta.url)),
      'Services': fileURLToPath(new URL('./src/services', import.meta.url)),
      'State': fileURLToPath(new URL('./src/state', import.meta.url)),
      'Constants': fileURLToPath(new URL('./src/utilities/constants', import.meta.url)),
      'Hooks': fileURLToPath(new URL('./src/hooks', import.meta.url)),
      'Contexts': fileURLToPath(new URL('./src/contexts', import.meta.url)),
      'Config': fileURLToPath(new URL('./src/config', import.meta.url)),
      'Theme': fileURLToPath(new URL('./src/theme', import.meta.url)),
      'Assets': fileURLToPath(new URL('./src/assets', import.meta.url)),
    },
  },
});
