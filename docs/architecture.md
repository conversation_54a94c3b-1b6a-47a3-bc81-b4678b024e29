# Project Architecture

## Overview

The Apple FruitX UI follows a component-based architecture built on React. It's designed to be modular, maintainable, and scalable as the application grows. This document outlines the high-level architecture, design patterns, and organization principles.

## Architectural Principles

1. **Component Modularity**: Each component should be focused on a single responsibility
2. **Code Size Limits**: No component should exceed 200 lines of code
3. **Separation of Concerns**: UI components, business logic, and API calls are separated
4. **Reusability**: Components are designed to be reused across the application
5. **Maintainability**: Code organization that facilitates easy maintenance and updates

## Directory Structure

```
src/
├── components/          # Reusable UI components
│   ├── common/          # General UI components used across the app
│   ├── form/            # Form components and form builder system
│   │   ├── fields/      # Individual form field components
│   │   └── validation/  # Validation utilities and rules
│   └── layouts/         # Layout components (sidebar, header, etc.)
├── hooks/               # Custom React hooks
├── pages/               # Page components (one per route)
├── theme/               # Theming and styling configuration
├── utilities/               # Utility functions and helpers
├── services/            # API services and data fetching
│   ├── api/             # API client configuration
│   └── endpoints/       # API endpoint modules
├── store/               # State management (if applicable)
├── constants/           # Application constants
├── routes/              # Route definitions
├── App.js               # Main application component
└── index.js             # Application entry point
```

## Component Hierarchy

```
App
├── AuthProvider         # Authentication context provider
├── ThemeProvider        # Theme context provider
├── Router               # Application routing
│   ├── AuthGuard        # Authentication protection
│   │   ├── Layout       # Main application layout
│   │   │   ├── Sidebar  # Navigation sidebar
│   │   │   ├── Header   # Application header
│   │   │   └── Pages    # Individual page components
│   │   │       ├── Dashboard
│   │   │       ├── Users
│   │   │       ├── Products
│   │   │       └── etc...
│   │   └── Components   # Shared components used by pages
```

## Key Design Patterns

### Component Composition

Components are composed from smaller, focused components rather than creating large, monolithic components. This approach makes the codebase more maintainable and testable.

```jsx
// Example of component composition
function ProductPage() {
  return (
    <PageContainer>
      <PageHeader title='Products' />
      <FilterPanel />
      <ProductList>
        {products.map(product => (
          <ProductCard key={product.id} product={product} />
        ))}
      </ProductList>
      <Pagination />
    </PageContainer>
  );
}
```

### Container/Presentation Pattern

Components are separated into container components (which manage state and data) and presentation components (which are purely for display).

```jsx
// Container component
function ProductListContainer() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProducts().then(data => {
      setProducts(data);
      setLoading(false);
    });
  }, []);

  return <ProductList products={products} loading={loading} />;
}

// Presentation component
function ProductList({ products, loading }) {
  if (loading) return <LoadingSpinner />;

  return (
    <div className='product-list'>
      {products.map(product => (
        <ProductItem key={product.id} product={product} />
      ))}
    </div>
  );
}
```

### Custom Hooks

Common functionality is extracted into custom hooks to promote reuse and separate concerns.

```jsx
// Example custom hook
function useProductData(productId) {
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    setLoading(true);
    fetchProduct(productId)
      .then(data => {
        setProduct(data);
        setLoading(false);
      })
      .catch(err => {
        setError(err);
        setLoading(false);
      });
  }, [productId]);

  return { product, loading, error };
}
```

### Context for Global State

React Context is used for global state management, such as user authentication, theme preferences, and global notifications.

```jsx
// Example context usage
const AuthContext = createContext();

function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Auth methods...

  return (
    <AuthContext.Provider value={{ user, loading, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
}
```

## Form Builder Architecture

The Form Builder system is a core architectural component:

1. **Field Components**: Individual field types (TextField, SelectField, etc.)
2. **FormBuilder Component**: Orchestrates the form creation from field definitions
3. **Validation System**: Uses Yup for schema-based validation
4. **Conditional Logic**: Allows dynamic showing/hiding of fields

See the [Form Builder Documentation](./form-builder.md) for more details.

## Styling Approach

The application uses Material UI (MUI) as its component library, with a custom theme defined in `src/theme/index.js`. The theming approach:

1. Uses MUI's theming system for consistent styling
2. Defines custom color palettes, typography, and component overrides
3. Uses CSS-in-JS via Emotion for component-specific styling

## Responsive Design

All components and pages are designed to be responsive from the ground up:

1. Uses MUI's Grid system for layout
2. Employs responsive breakpoints for different screen sizes
3. Implements mobile-first approach to ensure good mobile experience

## Performance Considerations

To maintain good performance, the architecture includes:

1. Component memoization with React.memo for expensive renders
2. useMemo and useCallback for optimization of computed values and callbacks
3. Pagination and virtualization for large data sets
4. Code splitting with React.lazy for route-based code splitting

## Error Handling

Consistent error handling approach throughout the application:

1. API errors are centrally managed and transformed into user-friendly messages
2. Form validation errors are presented inline with the relevant fields
3. Global error boundary catches unexpected errors
4. Error logging for monitoring and debugging

## Testing Strategy

The architecture supports testability through:

1. Component isolation for unit testing
2. Container/presentation separation for easier mocking
3. Custom hooks that can be tested independently

## Deployment Architecture

The application is built for deployment to multiple environments:

1. Development: Local development environment
2. Integration: Testing environment with integration APIs
3. Production: Live environment with production APIs

Environment-specific configurations are managed through `.env` files and environment variables.

## Future Architectural Considerations

1. Server-side rendering for improved initial load performance
2. Micro-frontend architecture for scaling development across teams
3. GraphQL integration for more efficient data fetching
4. Progressive Web App features for offline support
