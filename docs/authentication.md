# Authentication Flow

## Overview

The Apple FruitX UI uses a secure authentication system based on Single Sign-On (SSO) via Google. This document outlines the authentication flow and security measures implemented in the application.

## Key Authentication Rules

1. **Google SSO Only**: Only Google SSO login is allowed. Direct username/password authentication is not supported.
2. **Forced Authentication**: Unauthenticated users will always be redirected to the login page.
3. **Session Management**: User sessions are managed with secure tokens stored in localStorage and have configurable expiration times.
4. **Role-Based Access**: Different admin roles have different access levels and permissions.
5. **Auto-Redirect**: Any attempt to access the application without authentication will automatically redirect to the login page.

## Authentication Flow

```
┌─────────────┐     ┌────────────────┐     ┌───────────────┐     ┌────────────────┐
│ Access App  │────▶│ Check Auth     │────▶│ Redirect to   │────▶│ Google OAuth   │
└─────────────┘     │ Status         │     │ Login Page    │     │ Provider       │
                    └────────────────┘     └───────────────┘     └────────────────┘
                           │                                              │
                           │                                              │
                           │                                              ▼
                           │               ┌───────────────┐     ┌────────────────┐
                           └──────────────▶│ Store User    │◀────│ Google OAuth   │
                                           │ Data & Token  │     │ Callback       │
                                           └───────────────┘     └────────────────┘
                                                  │
                                                  ▼
                                           ┌───────────────┐
                                           │ Redirect to   │
                                           │ Dashboard     │
                                           └───────────────┘
```

## Implementation Details

### Authentication Guard

All routes in the application are protected by an Authentication Guard that verifies the user's authentication status before allowing access to any page. If a user is not authenticated, they will be automatically redirected to the login page.

```jsx
// Route protection implementation
<Route
  path='/dashboard'
  element={
    <AuthGuard>
      <Dashboard />
    </AuthGuard>
  }
/>
```

The `AuthGuard` component uses the authentication context to check if the user is logged in:

```jsx
const AuthGuard = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  // Show loading spinner while checking authentication
  if (loading) {
    return <CircularProgress />;
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to={routes.login} state={{ from: location }} replace />;
  }

  // Render protected content if authenticated
  return children;
};
```

### Google OAuth Integration

The application uses Google OAuth for authentication, implemented with the `@react-oauth/google` library. The flow follows these steps:

1. User attempts to access the Apple FruitX UI
2. System checks for a valid authentication token in localStorage
3. If no valid token is found, user is redirected to the login page
4. User clicks on the Google Sign-In button
5. Google OAuth consent screen appears for the user to authorize access
6. After successful authentication with Google, the Google OAuth callback returns a credential token
7. The application sends this token to the backend API for verification
8. Backend validates the token with Google, creates/retrieves the user account, and returns user data with an authentication token
9. The frontend stores this token and user data in localStorage
10. User is redirected to the dashboard

```jsx
// Google OAuth implementation
const handleGoogleLoginSuccess = async response => {
  try {
    // Google returns a credential token
    if (!response?.credential) {
      throw new Error('Google authentication failed');
    }

    // Decode token to get basic user info
    const decodedToken = jwtDecode(response.credential);

    // Send token to backend for verification
    const loginResponse = await googleLogin({
      token: response.credential,
      email: decodedToken.email,
      name: decodedToken.name,
      profile_image: decodedToken.picture,
    });

    // Store user data and redirect to dashboard
    loginSuccess(loginResponse.data);
  } catch (error) {
    // Handle errors
    setError('Google login failed');
  }
};
```

### Token Management

Authentication tokens are:

- Stored securely in browser storage
- Never exposed to client-side JavaScript
- Automatically refreshed before expiration
- Immediately invalidated on logout

### Session Timeout

For security reasons, user sessions have an automatic timeout. After a period of inactivity (configurable in the environment settings), the user will be required to re-authenticate.

## User Roles

The application supports multiple admin roles with different permission levels:

1. **Super Admin**: Full access to all features and settings
2. **Admin**: Access to most features but limited system configuration
3. **Moderator**: Limited access to specific functionalities only
4. **Viewer**: Read-only access to dashboards and reports

## Troubleshooting

Common authentication issues and their solutions:

1. **Unable to Login**: Ensure your SSO credentials are correct and your account has been granted access to the admin panel
2. **Session Expired**: Re-authenticate through the SSO provider
3. **Permission Denied**: Contact a Super Admin to request appropriate permissions

## Security Considerations

1. All authentication requests are made over HTTPS
2. Failed login attempts are rate-limited to prevent brute force attacks
3. Authentication tokens are rotated regularly
4. Sensitive operations require re-authentication for additional security

## Future Enhancements

1. Multi-factor authentication
2. IP-based access restrictions
3. Enhanced audit logging for authentication events
