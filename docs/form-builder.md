# Form Builder Documentation

## Overview

The Form Builder is a powerful, reusable component system that enables easy creation of complex forms throughout the Apple FruitX UI. It's designed to be flexible, maintainable, and to reduce repetitive code when creating forms.

## Key Features

- **Dynamic Form Generation**: Create forms through configuration objects rather than manual markup
- **Built-in Validation**: Comprehensive validation rules using Yup
- **Conditional Fields**: Show/hide fields based on values of other fields
- **Responsive Layout**: Automatic grid-based layout with customizable column spans
- **Component Modularity**: All components under 200 lines of code for maintainability

## Basic Usage

```jsx
import { FormBuilder } from '../components/form';

const MyComponent = () => {
  // Define fields
  const fields = [
    {
      name: 'fullName',
      label: 'Full Name',
      type: 'text',
      validations: ['required'],
      helperText: 'Enter your full name',
      gridProps: { md: 6 },
    },
    {
      name: 'email',
      label: 'Email Address',
      type: 'email',
      validations: ['email'],
      helperText: 'Enter your email address',
      gridProps: { md: 6 },
    },
  ];

  // Initial values
  const initialValues = {
    fullName: '',
    email: '',
  };

  // Handle form submission
  const handleSubmit = (values, { setSubmitting }) => {
    console.log('Form values:', values);
    // API call or other processing here
    setSubmitting(false);
  };

  return (
    <FormBuilder
      fields={fields}
      initialValues={initialValues}
      onSubmit={handleSubmit}
      title='Contact Information'
      submitButtonText='Save'
    />
  );
};
```

## Field Configuration

Each field is defined as an object with the following properties:

| Property      | Type     | Description                                                         |
| ------------- | -------- | ------------------------------------------------------------------- |
| `name`        | string   | Field name (used as form value key)                                 |
| `label`       | string   | Display label                                                       |
| `type`        | string   | Field type (text, email, select, etc.)                              |
| `validations` | array    | Array of validation rules                                           |
| `helperText`  | string   | Helper text displayed below the field                               |
| `required`    | boolean  | Whether the field is required                                       |
| `gridProps`   | object   | Grid props for responsive layout (xs, sm, md, lg, xl)               |
| `conditional` | function | Function that returns boolean to determine if field should be shown |
| `options`     | array    | Options for select, multiselect, radio types                        |
| `...rest`     | any      | Any additional props passed to the field component                  |

## Field Types

The Form Builder supports the following field types:

### Text Input Fields

- `text` - Standard text input
- `email` - Email input with email validation
- `password` - Password input with masking
- `number` - Numeric input with number validation
- `tel` - Telephone input with phone validation

### Selection Fields

- `select` - Dropdown selection
- `multiselect` - Multiple selection with chips
- `radio` - Radio button group
- `checkbox` - Single checkbox (boolean value)

### Specialized Fields

- `date` - Date picker
- `file` - File upload with preview

## Validation Rules

The Form Builder includes preset validation rules:

### String Validations

- `required` - Field must not be empty
- `email` - Must be valid email format
- `password` - Must meet password complexity requirements
- `phone` - Must be valid phone number format
- `min(length)` - Minimum length
- `max(length)` - Maximum length

### Number Validations

- `number` - Must be a valid number
- `positiveNumber` - Must be a positive number
- `integer` - Must be an integer
- `min(value)` - Minimum value
- `max(value)` - Maximum value

### Date Validations

- `date` - Must be a valid date
- `futureDate` - Must be a date in the future
- `pastDate` - Must be a date in the past

### Array Validations

- `array` - Must be an array
- `nonEmptyArray` - Array must not be empty

### Custom Validation

```jsx
{
  name: 'customField',
  type: 'text',
  validations: [
    {
      type: 'test',
      params: {
        test: (value) => value === 'expected value',
      },
      message: 'Custom validation failed'
    }
  ]
}
```

## Conditional Fields

Fields can be conditionally rendered based on the values of other fields:

```jsx
{
  name: 'hasChildren',
  label: 'Do you have children?',
  type: 'radio',
  options: [
    { value: 'yes', label: 'Yes' },
    { value: 'no', label: 'No' }
  ],
  validations: ['required']
},
{
  name: 'numberOfChildren',
  label: 'Number of Children',
  type: 'number',
  validations: ['positiveNumber'],
  // This field only shows if hasChildren is 'yes'
  conditional: (values) => values.hasChildren === 'yes'
}
```

## Form Layout Customization

The form layout can be customized using the `formLayout` prop:

```jsx
<FormBuilder
  fields={fields}
  initialValues={initialValues}
  onSubmit={handleSubmit}
  formLayout={{
    spacing: 3, // Grid spacing
    direction: 'row', // Grid direction
    containerProps: {
      // Props for the container Box
      sx: { mt: 2, p: 3 },
    },
    submitButtonProps: {
      // Props for the submit button
      size: 'large',
      variant: 'contained',
    },
    cancelButtonProps: {
      // Props for the cancel button
      size: 'large',
    },
  }}
/>
```

## Advanced Usage

### Dynamic Field Generation

Fields can be generated dynamically based on data or conditions:

```jsx
// Generate fields for each product attribute
const generateProductFields = attributes => {
  return attributes.map(attr => ({
    name: `attribute_${attr.id}`,
    label: attr.name,
    type: attr.type === 'boolean' ? 'checkbox' : 'text',
    validations: attr.required ? ['required'] : [],
    gridProps: { md: 6 },
  }));
};

// Later in component
const fields = [
  // Basic fields
  ...baseFields,
  // Dynamic attribute fields
  ...generateProductFields(productAttributes),
];
```

### Form Sections

Complex forms can be organized into sections using Typography and Dividers:

```jsx
const fields = [
  {
    name: '_section_personal', // Non-form fields can use _ prefix
    component: (
      <Box mt={4} mb={2}>
        <Typography variant='h6'>Personal Information</Typography>
        <Divider />
      </Box>
    ),
  },
  // Personal information fields...
  {
    name: '_section_contact',
    component: (
      <Box mt={4} mb={2}>
        <Typography variant='h6'>Contact Information</Typography>
        <Divider />
      </Box>
    ),
  },
  // Contact information fields...
];
```

## Best Practices

1. **Component Size**: Keep field components under 200 lines of code
2. **Validation Isolation**: Create reusable validation schemas for common patterns
3. **Field Naming**: Use consistent naming conventions for field names
4. **Conditional Logic**: Keep conditional functions simple and focused
5. **Performance**: Avoid complex conditionals that could impact form performance

## Troubleshooting

Common issues and solutions:

1. **Field not updating**: Check that the field name matches the initialValues key
2. **Validation not working**: Ensure validation rules are correctly defined
3. **Conditional field not showing**: Verify the conditional function logic

## Examples

See the [FormBuilderExample component](/src/components/examples/FormBuilderExample.jsx) for comprehensive examples of different form types, including conditional fields and validation.
