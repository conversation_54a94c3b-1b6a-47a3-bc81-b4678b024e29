# Apple FruitX Panel Documentation

This documentation provides information about the Apple FruitX Panel (mandi-apple-fe), including its architecture, components, and how to make changes to the codebase.

## Tech Stack

The Apple FruitX Panel is built using the following technologies:

- **React** - Frontend library for building user interfaces
- **Vite** - Build tool and development server for fast development
- **Material UI** - Component library for implementing Google's Material Design
- **React Router** - For handling navigation and routing
- **Axios** - For making API requests
- **Google OAuth** - For authentication
- **Formik & Yup** - Form management and validation
- **Dayjs** - Date manipulation library

## Application Structure

The application follows a modular structure:

```
mandi-apple-fe/
├── public/           # Static assets
├── src/
│   ├── components/   # Reusable UI components
│   │   └── Layout/   # Main layout components (AppHeader, Sidebar, etc.)
│   ├── contexts/     # React contexts for state management
│   │   └── AuthContext.js # Authentication state and functions
│   ├── pages/        # Application pages
│   │   ├── Dashboard/ # Dashboard page
│   │   ├── Login/    # Login page
│   │   └── ...       # Other pages
│   ├── services/     # API services
│   │   └── base/     # Base API configuration
│   ├── utilities/        # Utility functions
│   └── App.js        # Main application component
├── docs/             # Documentation files
├── .storybook/       # Storybook configuration
├── vite.config.js    # Vite configuration
├── package.json      # Dependencies and scripts
└── index.html        # Entry HTML file
```

## Authentication Flow

The application uses Google OAuth for authentication:

1. User clicks "Sign in with Google" on the login page
2. Google returns a credential token
3. The token is sent to the backend for verification
4. The backend validates the token and returns a JWT token
5. The JWT token is stored in localStorage for future API requests
6. The user profile is fetched from the backend API

## Layout and Navigation

The application uses a responsive layout with:

1. **AppHeader** - Top navigation with hamburger menu and user profile
2. **Sidebar** - Side navigation with expandable menu items
3. **Main Content Area** - Content area for pages

For detailed information on how to add new menu items and routes, see [layout-navigation.md](./layout-navigation.md).

## Adding New Features

### Creating a New Page

1. Create a new directory in `src/pages/` for your page
2. Create a React component for your page
3. Update the routing in `App.js` to include your new page
4. Add a menu item in the sidebar (see layout-navigation.md)

### Adding API Services

1. Create a new file in `src/services/` for your API service
2. Use the `supplyChainService` from `services/base/index.js` to make API requests
3. Export functions that call your API endpoints

## Development Workflow with Vite

### Getting Started

```bash
# Clone the repository
git clone <repository-url>
cd mandi-apple-fe

# Install dependencies
npm install

# Start development server
npm run dev
```

### Available Scripts

- `npm run dev` - Start Vite development server (http://localhost:9000)
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally
- `npm run storybook` - Start Storybook for component development
- `npm run build-storybook` - Build Storybook for production

### Vite Configuration

The project uses Vite for fast development and building. Key configuration in `vite.config.js`:

```javascript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  server: {
    port: 9000,
  },
});
```

### Environment Variables

Vite uses different environment files:

- `.env.development` - Development environment
- `.env.integration` - Integration environment
- `.env.production` - Production environment

Environment variables must be prefixed with `VITE_` to be accessible in the client-side code.

## Best Practices

When making changes to the codebase, follow these best practices:

1. **Keep documentation updated** as changes are made
2. Use **consistent naming conventions** throughout the codebase
3. Break down complex components into smaller, reusable components
4. Use the existing design system and components when possible
5. Write clean, maintainable code with proper comments

## Troubleshooting

### Authentication Issues

If you encounter authentication issues:

1. Check browser console for error messages
2. Verify the Google client ID in the environment variables
3. Ensure the backend API is correctly configured to accept Google tokens

### UI/Layout Issues

For layout or UI issues:

1. Check for responsive design issues on different screen sizes
2. Inspect the component hierarchy in React DevTools
3. Review the Material UI documentation for proper component usage

## Development Guidelines

1. **Component Structure**: Follow the existing component structure
2. **State Management**: Use React Context for global state
3. **API Calls**: Use the service layer for all API calls
4. **Error Handling**: Implement proper error handling for API calls
5. **Styling**: Use Material UI styled components for consistent styling

## Further Resources

- [Vite Documentation](https://vitejs.dev/guide/)
- [React Documentation](https://react.dev/learn)
- [Material UI Documentation](https://mui.com/material-ui/getting-started/overview/)
- [React Router Documentation](https://reactrouter.com/)
- [Formik Documentation](https://formik.org/docs/overview)
- [Yup Validation Documentation](https://github.com/jquense/yup)
- [React Router Documentation](https://reactrouter.com/en/main)
