# Contributing to Apple FruitX Panel

This guide explains how to contribute to the Apple FruitX Panel (mandi-apple-fe) codebase.

## Development Workflow

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd mandi-apple-fe
   ```

2. **Install dependencies** with `npm install`

3. **Start the development server** with `npm run dev` (uses Vite)
   - The app will be available at http://localhost:9000
   - Vite provides fast hot module replacement for quick development

4. **Make changes** to the codebase

5. **Test your changes** thoroughly
   - Test in development mode with `npm run dev`
   - Build and preview with `npm run build && npm run preview`

6. **Commit and push** your changes

## Documentation

Documentation is critical for maintaining the codebase. As per the project guidelines:

> Keep on updating docs as per the changes in the code.
> Docs are basically for reference of developers on how to make changes in the code and give them information about business flows.

When contributing to the codebase:

1. **Document all significant changes** you make
2. **Update existing documentation** when you modify functionality
3. **Add new documentation** for new features or components
4. **Include business logic explanations** where necessary

## Code Structure

Follow the existing code structure when adding new features:

- **Components**: Place reusable UI components in `/src/components/`
- **Pages**: Place full pages in `/src/pages/`
- **Services**: Place API service files in `/src/services/`
- **Utilities**: Place utility functions in `/src/utilities/`
- **Contexts**: Place context providers in `/src/contexts/`

## Coding Standards

1. **Use modern JavaScript features** (ES6+)
2. **Use functional components** with hooks where possible
3. **Follow Material UI patterns** for consistency
4. **Write clean, maintainable code** with proper comments
5. **Use descriptive variable and function names**

## Adding New Features

### UI Components

When adding new UI components:

1. Create a new component in the appropriate directory
2. Follow Material UI design patterns
3. Make components responsive
4. Document the component's props and usage

### API Integration

When adding new API integrations:

1. Create or update service files in `/src/services/`
2. Use the base API service for consistency
3. Handle errors properly
4. Document the API endpoints and parameters

### Authentication

The application uses Google OAuth authentication. If you're working on authentication:

1. Make sure to maintain the login flow
2. Ensure proper token handling
3. Test authentication thoroughly across page refreshes

## Testing

Before submitting changes:

1. **Test on multiple browsers** (Chrome, Firefox, Safari)
2. **Test on multiple screen sizes** (desktop, tablet, mobile)
3. **Test all user flows** affected by your changes
4. **Verify API integration** works properly

## Deployment

The admin panel is deployed using a CI/CD pipeline. When your changes are ready for deployment:

1. Make sure all documentation is up to date
2. Ensure all tests pass
3. Submit your changes for review
4. Once approved, changes will be deployed to the appropriate environment

## Getting Help

If you need help with the codebase:

1. Refer to the documentation in `/docs/`
2. Check the code comments for specific implementations
3. Reach out to the development team for assistance
